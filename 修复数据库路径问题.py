#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复打包后数据库路径不一致的问题
"""

import os
import re

def fix_database_paths():
    """修复server.py中的数据库路径问题"""
    print("🔧 修复数据库路径问题...")
    
    # 读取server.py文件
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计需要修复的地方
    direct_connections = re.findall(r'sqlite3\.connect\("server_data\.db"', content)
    print(f"找到 {len(direct_connections)} 处直接连接数据库的代码")
    
    # 替换直接连接为使用统一的连接函数
    replacements = [
        # 替换直接的sqlite3.connect调用
        (
            r'import sqlite3\s*\n\s*conn = sqlite3\.connect\("server_data\.db"',
            'from user_manager import get_sqlite_connection\n            conn = get_sqlite_connection()'
        ),
        (
            r'sqlite3\.connect\("server_data\.db"',
            'get_sqlite_connection()'
        ),
        # 确保导入了get_sqlite_connection
        (
            r'(\s+)import sqlite3\s*\n(\s+)conn = sqlite3\.connect\("server_data\.db"',
            r'\1from user_manager import get_sqlite_connection\n\2conn = get_sqlite_connection()'
        ),
    ]
    
    original_content = content
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # 检查是否有修改
    if content != original_content:
        # 备份原文件
        backup_file = 'server.py.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"✅ 已备份原文件到: {backup_file}")
        
        # 写入修改后的内容
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已修复server.py中的数据库路径问题")
        
        # 统计修复后的情况
        fixed_connections = re.findall(r'sqlite3\.connect\("server_data\.db"', content)
        print(f"修复后剩余直接连接: {len(fixed_connections)} 处")
        
        return True
    else:
        print("ℹ️  未发现需要修复的数据库路径问题")
        return False

def add_database_path_helper():
    """在user_manager.py中添加数据库路径辅助函数"""
    print("\n🔧 添加数据库路径辅助函数...")
    
    with open('user_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已经有get_database_path函数
    if 'def get_database_path():' in content:
        print("ℹ️  数据库路径辅助函数已存在")
        return False
    
    # 在LOCAL_DB_PATH定义后添加辅助函数
    helper_function = '''
def get_database_path():
    """获取数据库文件路径，兼容打包后的环境"""
    try:
        # 在打包后的环境中，__file__可能指向不同的位置
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        # 数据库文件放在程序目录下
        db_path = os.path.join(base_path, "server_data.db")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        return db_path
    except Exception as e:
        # 如果出错，使用默认路径
        return LOCAL_DB_PATH

def get_sqlite_connection_safe():
    """安全的SQLite连接函数，自动处理路径问题"""
    try:
        import sqlite3
        import sys
        
        # 获取正确的数据库路径
        db_path = get_database_path()
        
        # 设置超时参数，避免"database is locked"错误
        conn = sqlite3.connect(db_path, timeout=30.0, check_same_thread=False)
        # 设置更长的忙等待超时
        conn.execute("PRAGMA busy_timeout = 30000")
        # 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON")
        # 设置日志模式为WAL（Write-Ahead Logging），提高并发性能
        conn.execute("PRAGMA journal_mode = WAL")
        
        logger.info(f"本地 SQLite 数据库连接成功: {db_path}")
        return conn
    except Exception as e:
        logger.error(f"本地 SQLite 数据库连接失败: {str(e)}")
        return None
'''
    
    # 在LOCAL_DB_PATH定义后插入辅助函数
    pattern = r'(# 本地 SQLite 数据库路径\nLOCAL_DB_PATH = os\.path\.join\(os\.path\.dirname\(__file__\), "server_data\.db"\))'
    replacement = r'\1' + helper_function
    
    content = re.sub(pattern, replacement, content)
    
    # 确保导入了sys模块
    if 'import sys' not in content:
        content = re.sub(r'(import os)', r'\1\nimport sys', content)
    
    # 写入修改后的内容
    with open('user_manager.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已添加数据库路径辅助函数")
    return True

def update_local_db_path():
    """更新LOCAL_DB_PATH的定义以兼容打包环境"""
    print("\n🔧 更新LOCAL_DB_PATH定义...")
    
    with open('user_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 更新LOCAL_DB_PATH的定义
    old_pattern = r'# 本地 SQLite 数据库路径\nLOCAL_DB_PATH = os\.path\.join\(os\.path\.dirname\(__file__\), "server_data\.db"\)'
    
    new_definition = '''# 本地 SQLite 数据库路径
def get_local_db_path():
    """获取本地数据库路径，兼容打包环境"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        db_path = os.path.join(base_path, "server_data.db")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        return db_path
    except Exception:
        # 如果出错，使用当前目录
        return os.path.join(os.getcwd(), "server_data.db")

LOCAL_DB_PATH = get_local_db_path()'''
    
    content = re.sub(old_pattern, new_definition, content)
    
    # 写入修改后的内容
    with open('user_manager.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 已更新LOCAL_DB_PATH定义")
    return True

def create_database_init_script():
    """创建数据库初始化脚本"""
    print("\n🔧 创建数据库初始化脚本...")
    
    init_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
确保所有必要的表都被创建
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        return os.path.join(base_path, "server_data.db")
    except Exception:
        return os.path.join(os.getcwd(), "server_data.db")

def init_all_tables():
    """初始化所有必要的数据库表"""
    db_path = get_database_path()
    logger.info(f"初始化数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建日志表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建直播状态表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        ''')
        
        # 创建token表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        ''')
        
        # 创建客户端更新表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        ''')
        
        # 创建API配置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建弹幕记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS danmaku_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            sender TEXT,
            content TEXT NOT NULL,
            timestamp TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("所有数据库表初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = init_all_tables()
    if success:
        print("✅ 数据库初始化成功")
    else:
        print("❌ 数据库初始化失败")
        sys.exit(1)
'''
    
    with open('init_database.py', 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    print("✅ 已创建数据库初始化脚本: init_database.py")
    return True

def main():
    """主函数"""
    print("🚀 修复打包后数据库路径问题")
    print("=" * 50)
    
    try:
        # 1. 修复server.py中的数据库路径
        fix_database_paths()
        
        # 2. 更新user_manager.py中的LOCAL_DB_PATH定义
        update_local_db_path()
        
        # 3. 创建数据库初始化脚本
        create_database_init_script()
        
        print("\n" + "=" * 50)
        print("🎉 修复完成！")
        print("\n📝 修复内容:")
        print("✅ 统一了数据库连接方式")
        print("✅ 更新了数据库路径获取逻辑")
        print("✅ 创建了数据库初始化脚本")
        
        print("\n💡 建议:")
        print("1. 重新打包程序")
        print("2. 在打包后的程序中运行 init_database.py 初始化数据库")
        print("3. 测试所有功能是否正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
