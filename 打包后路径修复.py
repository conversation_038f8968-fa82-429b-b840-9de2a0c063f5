#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包后路径修复脚本
解决PyInstaller打包后更新界面出错的问题
"""

import os
import sys
import shutil

def get_base_path():
    """获取程序基础路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的程序
        base_path = sys._MEIPASS
        return os.path.join(base_path, relative_path)
    else:
        # 开发环境
        return os.path.join(os.path.dirname(__file__), relative_path)

def fix_server_py():
    """修复server.py中的路径问题"""
    print("🔧 修复server.py中的路径问题...")
    
    # 读取原始server.py
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加路径修复代码
    path_fix_code = '''
# ==================== 打包后路径修复 ====================
import os
import sys

def get_base_path():
    """获取程序基础路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的程序，资源在临时目录
        base_path = sys._MEIPASS
        return os.path.join(base_path, relative_path)
    else:
        # 开发环境
        return os.path.join(os.path.dirname(__file__), relative_path)

def get_data_path(relative_path):
    """获取数据文件路径（永久存储）"""
    base_path = get_base_path()
    return os.path.join(base_path, relative_path)

# 设置Flask应用的模板和静态文件路径
TEMPLATE_DIR = get_resource_path('templates')
STATIC_DIR = get_resource_path('static')

print(f"Template directory: {TEMPLATE_DIR}")
print(f"Static directory: {STATIC_DIR}")

# 确保目录存在
os.makedirs(TEMPLATE_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)

# 数据库路径修复
def get_database_path(db_name):
    """获取数据库文件路径"""
    if getattr(sys, 'frozen', False):
        # 打包后，数据库放在exe同目录
        base_path = get_base_path()
        return os.path.join(base_path, db_name)
    else:
        # 开发环境
        return db_name

# ==================== 路径修复结束 ====================

'''
    
    # 在Flask应用创建之前插入路径修复代码
    flask_app_pattern = "app = Flask(__name__"
    if flask_app_pattern in content:
        # 替换Flask应用创建
        content = content.replace(
            "app = Flask(__name__)",
            f"{path_fix_code}\napp = Flask(__name__, template_folder=TEMPLATE_DIR, static_folder=STATIC_DIR)"
        )
    else:
        # 如果没找到，在文件开头添加
        content = path_fix_code + '\n' + content
    
    # 修复数据库连接路径
    db_patterns = [
        ("'server_data.db'", "get_database_path('server_data.db')"),
        ('"server_data.db"', 'get_database_path("server_data.db")'),
        ("'local.db'", "get_database_path('local.db')"),
        ('"local.db"', 'get_database_path("local.db")'),
    ]
    
    for old_pattern, new_pattern in db_patterns:
        content = content.replace(old_pattern, new_pattern)
    
    # 保存修复后的文件
    with open('server_fixed.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ server.py路径修复完成，保存为server_fixed.py")

def create_fixed_spec():
    """创建修复后的打包配置"""
    print("📝 创建修复后的打包配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os

block_cipher = None

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['server_fixed.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('gift_id_map.json', '.'),
        ('keyword_response_pairs.json', '.'),
        ('user_manager.py', '.'),
    ],
    hiddenimports=[
        'waitress',
        'flask',
        'flask_cors',
        'flask_socketio',
        'pymysql',
        'sqlite3',
        'json',
        'logging',
        'datetime',
        'os',
        'sys',
        'threading',
        'time',
        'requests',
        'urllib.parse',
        'hashlib',
        'base64',
        'uuid',
        'psutil',
        'gevent',
        'eventlet',
        'socketio',
        'engineio',
        'jinja2',
        'werkzeug',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'cryptography',
        'cffi',
        'pycparser',
        'six',
        'greenlet',
        'zope.interface',
        'zope.event',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统服务器_路径修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('AI主播系统服务器_路径修复版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 打包配置创建完成")

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # 创建批处理启动脚本
    bat_content = '''@echo off
chcp 65001 > nul
title AI主播系统服务器 - 路径修复版
echo.
echo ========================================
echo    AI主播系统服务器 - 路径修复版
echo ========================================
echo.
echo 正在启动服务器...
echo 服务器地址: http://localhost:12456
echo 管理后台: http://localhost:12456/admin
echo 默认账号: kaer / a13456A
echo.

"AI主播系统服务器_路径修复版.exe"

echo.
echo 服务器已停止运行
pause
'''
    
    with open('启动路径修复版.bat', 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print("✅ 启动脚本创建完成")

def copy_database_files():
    """复制数据库文件到当前目录"""
    print("🗄️ 复制数据库文件...")
    
    db_files = ['server_data.db', 'local.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"  ✅ {db_file} 已存在")
        else:
            # 尝试从其他位置复制
            possible_paths = [
                f'AI主播系统服务器_完整独立版/data/database/{db_file}',
                f'dist/{db_file}',
                f'data/{db_file}'
            ]
            
            copied = False
            for path in possible_paths:
                if os.path.exists(path):
                    shutil.copy2(path, db_file)
                    print(f"  ✅ 从 {path} 复制 {db_file}")
                    copied = True
                    break
            
            if not copied:
                print(f"  ⚠️ 未找到 {db_file}，将在运行时创建")

def run_packaging():
    """执行打包"""
    print("🚀 开始打包路径修复版...")
    
    import subprocess
    
    # 清理旧的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理旧的build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的dist目录")
    
    # 执行打包命令
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'AI主播系统服务器_路径修复版.spec'
    ]
    
    print(f"📦 执行打包命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🔧 AI主播系统服务器打包后路径修复工具")
    print("=" * 60)
    
    # 修复server.py
    fix_server_py()
    
    # 创建打包配置
    create_fixed_spec()
    
    # 复制数据库文件
    copy_database_files()
    
    # 创建启动脚本
    create_startup_script()
    
    # 执行打包
    if run_packaging():
        # 复制数据库文件到dist目录
        for db_file in ['server_data.db', 'local.db']:
            if os.path.exists(db_file):
                shutil.copy2(db_file, f'dist/{db_file}')
                print(f"  ✅ 复制 {db_file} 到 dist 目录")
        
        # 复制启动脚本
        shutil.copy2('启动路径修复版.bat', 'dist/')
        
        print("=" * 60)
        print("🎉 路径修复版打包完成！")
        print("📁 输出目录: dist/")
        print("📦 可执行文件: AI主播系统服务器_路径修复版.exe")
        print("🚀 启动脚本: 启动路径修复版.bat")
        print("=" * 60)
        print("💡 修复内容:")
        print("✅ 修复了模板文件路径问题")
        print("✅ 修复了静态文件路径问题") 
        print("✅ 修复了数据库文件路径问题")
        print("✅ 修复了资源文件访问问题")
        print("=" * 60)
        print("🎯 现在更新界面应该不会出错了！")
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 路径修复完成！现在打包后的程序应该和直接运行Python一样正常工作！")
    else:
        print("💥 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
