<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .table tbody tr:hover {
            background-color: #f1f1f1;
        }
        .table-responsive {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }
        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filter-item {
            display: flex;
            align-items: center;
        }
        .filter-item label {
            margin-right: 8px;
            font-weight: bold;
        }
        .filter-item input, .filter-item select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .btn-info {
            background-color: #17a2b8;
            color: white;
        }
        .btn-info:hover {
            background-color: #138496;
        }
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 20px;
        }
        .pagination a {
            color: #007bff;
            padding: 8px 16px;
            text-decoration: none;
            border: 1px solid #dee2e6;
            margin: 0 4px;
        }
        .pagination a.active {
            background-color: #007bff;
            color: white;
            border-color: #007bff;
        }
        .pagination a:hover:not(.active) {
            background-color: #e9ecef;
        }
        .pagination a.disabled {
            color: #6c757d;
            pointer-events: none;
            cursor: default;
        }
        .log-level {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        .log-level-info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .log-level-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        .log-level-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .log-level-debug {
            background-color: #d4edda;
            color: #155724;
        }
        .log-details {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 5px;
            width: 70%;
            max-width: 800px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .modal-body {
            max-height: 400px;
            overflow-y: auto;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            width: 120px;
            flex-shrink: 0;
        }
        .detail-value {
            flex-grow: 1;
        }
        .detail-value pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 0;
        }
        .actions-column {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management" class="active"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>系统日志</h2>
                <div class="filter-container">
                    <div class="filter-item">
                        <label for="username-filter">用户名:</label>
                        <input type="text" id="username-filter" class="form-control" placeholder="输入用户名">
                    </div>
                    <div class="filter-item">
                        <label for="date-filter">日期:</label>
                        <input type="date" id="date-filter" class="form-control">
                    </div>
                    <div class="filter-item">
                        <label for="level-filter">级别:</label>
                        <select id="level-filter" class="form-control">
                            <option value="all">全部</option>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                            <option value="debug">调试</option>
                        </select>
                    </div>
                    <div class="filter-item">
                        <button id="filter-btn" class="btn btn-primary">
                            <i class="fas fa-filter"></i> 筛选
                        </button>
                        <button id="clear-filter-btn" class="btn btn-danger">
                            <i class="fas fa-times"></i> 清除筛选
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="logs-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>IP地址</th>
                                <th>操作</th>
                                <th>详情</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" style="text-align: center;">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination" id="pagination">
                    <!-- 分页将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div id="log-detail-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>日志详情</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body" id="log-detail-content">
                <!-- 日志详情内容将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页码和筛选条件
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = parseInt(urlParams.get('page')) || 1;
            const usernameFilter = urlParams.get('username') || '';
            const dateFilter = urlParams.get('date') || '';
            const levelFilter = urlParams.get('level') || 'all';

            // 设置筛选输入框的值
            document.getElementById('username-filter').value = usernameFilter;
            document.getElementById('date-filter').value = dateFilter;
            document.getElementById('level-filter').value = levelFilter;

            // 获取日志列表
            fetchLogs(currentPage, usernameFilter, dateFilter, levelFilter);

            // 初始化筛选功能
            initFilters();

            // 初始化模态框
            initModal();
        });

        function fetchLogs(page, username, date, level) {
            const pageSize = 20; // 每页显示20条记录
            let url = `/admin/logs?page=${page}&page_size=${pageSize}`;

            if (username) {
                url += `&username=${encodeURIComponent(username)}`;
            }

            if (date) {
                url += `&date=${encodeURIComponent(date)}`;
            }

            if (level && level !== 'all') {
                url += `&level=${encodeURIComponent(level)}`;
            }

            // 显示加载中
            document.querySelector('#logs-table tbody').innerHTML = '<tr><td colspan="7" style="text-align: center;">加载中...</td></tr>';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log('API返回数据:', data);

                    if (data.状态 === '成功') {
                        const logs = data.数据.日志列表;
                        const tbody = document.querySelector('#logs-table tbody');

                        if (!logs || logs.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        logs.forEach(log => {
                            // 处理可能的null值
                            const id = log.id || '';
                            const username = log.username || '--';
                            const ip = log.ip || '--';
                            const action = log.action || '--';
                            let details = log.details || '--';
                            const time = log.time || '--';

                            // 如果details是JSON字符串，尝试解析并格式化
                            try {
                                if (typeof details === 'string' && (details.startsWith('{') || details.startsWith('['))) {
                                    const parsedDetails = JSON.parse(details);
                                    details = JSON.stringify(parsedDetails, null, 2);
                                }
                            } catch (e) {
                                console.warn('解析日志详情失败:', e);
                            }

                            // 确定日志级别样式
                            let levelClass = '';
                            if (action.toLowerCase().includes('error') || action.toLowerCase().includes('错误')) {
                                levelClass = 'log-level log-level-error';
                            } else if (action.toLowerCase().includes('warning') || action.toLowerCase().includes('警告')) {
                                levelClass = 'log-level log-level-warning';
                            } else if (action.toLowerCase().includes('debug') || action.toLowerCase().includes('调试')) {
                                levelClass = 'log-level log-level-debug';
                            } else {
                                levelClass = 'log-level log-level-info';
                            }

                            // 截断详情文本，避免表格过宽
                            const shortDetails = typeof details === 'string' && details.length > 50
                                ? details.substring(0, 47) + '...'
                                : details;

                            tbody.innerHTML += `
                                <tr>
                                    <td>${id}</td>
                                    <td>${username}</td>
                                    <td>${ip}</td>
                                    <td class="${levelClass}">${action}</td>
                                    <td class="log-details">${shortDetails}</td>
                                    <td>${time}</td>
                                    <td class="actions-column">
                                        <button class="btn btn-info btn-sm view-log-btn" data-log-id="${id}">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });

                        // 绑定查看按钮事件
                        document.querySelectorAll('.view-log-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const logId = this.getAttribute('data-log-id');
                                viewLogDetail(logId);
                            });
                        });

                        // 生成分页
                        generatePagination(data.数据.当前页, data.数据.总页数, data.数据.总数);
                    } else {
                        const tbody = document.querySelector('#logs-table tbody');
                        tbody.innerHTML = `<tr><td colspan="7" style="text-align: center;">加载失败: ${data.信息 || '未知错误'}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('获取日志列表出错:', error);
                    const tbody = document.querySelector('#logs-table tbody');
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">加载失败: 网络错误</td></tr>';
                });
        }

        function generatePagination(currentPage, totalPages, totalCount) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                pagination.innerHTML = `<div>共 ${totalCount} 条记录</div>`;
                return;
            }

            // 获取当前筛选条件
            const username = document.getElementById('username-filter').value;
            const date = document.getElementById('date-filter').value;
            const level = document.getElementById('level-filter').value;

            // 上一页
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.innerHTML = '<i class="fas fa-chevron-left"></i> 上一页';
            prevLink.setAttribute('data-page', currentPage - 1);
            if (currentPage === 1) {
                prevLink.classList.add('disabled');
            }
            pagination.appendChild(prevLink);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.setAttribute('data-page', i);
                if (i === currentPage) {
                    pageLink.classList.add('active');
                }
                pagination.appendChild(pageLink);
            }

            // 下一页
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.innerHTML = '下一页 <i class="fas fa-chevron-right"></i>';
            nextLink.setAttribute('data-page', currentPage + 1);
            if (currentPage === totalPages) {
                nextLink.classList.add('disabled');
            }
            pagination.appendChild(nextLink);

            // 添加总记录数显示
            const totalInfo = document.createElement('div');
            totalInfo.style.marginLeft = '15px';
            totalInfo.textContent = `共 ${totalCount} 条记录`;
            pagination.appendChild(totalInfo);

            // 添加点击事件
            const pageLinks = pagination.querySelectorAll('a:not(.disabled)');
            pageLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page) {
                        // 获取当前筛选条件
                        const username = document.getElementById('username-filter').value;
                        const date = document.getElementById('date-filter').value;
                        const level = document.getElementById('level-filter').value;

                        // 重新加载日志
                        fetchLogs(page, username, date, level);

                        // 更新URL，但不刷新页面
                        const url = new URL(window.location.href);
                        url.searchParams.set('page', page);
                        window.history.pushState({}, '', url);
                    }
                });
            });
        }

        function initFilters() {
            // 筛选按钮点击事件
            document.getElementById('filter-btn').addEventListener('click', function() {
                const username = document.getElementById('username-filter').value;
                const date = document.getElementById('date-filter').value;
                const level = document.getElementById('level-filter').value;

                // 重置到第一页
                fetchLogs(1, username, date, level);

                // 更新URL，但不刷新页面
                const url = new URL(window.location.href);
                url.searchParams.set('page', 1);
                if (username) url.searchParams.set('username', username);
                else url.searchParams.delete('username');

                if (date) url.searchParams.set('date', date);
                else url.searchParams.delete('date');

                if (level !== 'all') url.searchParams.set('level', level);
                else url.searchParams.delete('level');

                window.history.pushState({}, '', url);
            });

            // 清除筛选按钮点击事件
            document.getElementById('clear-filter-btn').addEventListener('click', function() {
                document.getElementById('username-filter').value = '';
                document.getElementById('date-filter').value = '';
                document.getElementById('level-filter').value = 'all';

                // 重置到第一页，无筛选条件
                fetchLogs(1, '', '', 'all');

                // 更新URL，但不刷新页面
                const url = new URL(window.location.href);
                url.searchParams.set('page', 1);
                url.searchParams.delete('username');
                url.searchParams.delete('date');
                url.searchParams.delete('level');

                window.history.pushState({}, '', url);
            });

            // 回车键触发筛选
            document.getElementById('username-filter').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    document.getElementById('filter-btn').click();
                }
            });
        }

        function initModal() {
            const modal = document.getElementById('log-detail-modal');
            const closeBtn = document.querySelector('.close');

            // 点击关闭按钮关闭模态框
            closeBtn.addEventListener('click', function() {
                modal.style.display = 'none';
            });

            // 点击模态框外部关闭模态框
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        function viewLogDetail(logId) {
            // 显示模态框
            const modal = document.getElementById('log-detail-modal');
            modal.style.display = 'block';

            // 显示加载中
            document.getElementById('log-detail-content').innerHTML = '<div style="text-align: center; padding: 20px;">加载中...</div>';

            // 获取日志详情
            fetch(`/admin/api/logs/${logId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const logData = data.数据;
                        let detailHtml = '';

                        // 遍历日志数据的所有字段
                        for (const [key, value] of Object.entries(logData)) {
                            let displayValue = value;

                            // 如果是详情字段且是JSON字符串，尝试格式化
                            if (key === 'details' && typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
                                try {
                                    const parsedValue = JSON.parse(value);
                                    displayValue = `<pre>${JSON.stringify(parsedValue, null, 2)}</pre>`;
                                } catch (e) {
                                    console.warn('解析日志详情失败:', e);
                                }
                            }

                            detailHtml += `
                                <div class="detail-row">
                                    <div class="detail-label">${key}:</div>
                                    <div class="detail-value">${displayValue || '--'}</div>
                                </div>
                            `;
                        }

                        document.getElementById('log-detail-content').innerHTML = detailHtml;
                    } else {
                        document.getElementById('log-detail-content').innerHTML = `
                            <div style="text-align: center; color: red; padding: 20px;">
                                加载失败: ${data.信息 || '未知错误'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取日志详情出错:', error);
                    document.getElementById('log-detail-content').innerHTML = `
                        <div style="text-align: center; color: red; padding: 20px;">
                            加载失败: 网络错误
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>
