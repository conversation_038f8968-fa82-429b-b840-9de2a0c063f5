#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的数据库修复脚本
修复所有数据库相关问题
"""

import os
import sys
import sqlite3
import logging
import glob

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_database_files():
    """查找所有可能的数据库文件"""
    possible_paths = []
    
    # 当前目录
    current_dir = os.getcwd()
    possible_paths.append(os.path.join(current_dir, "server_data.db"))
    
    # 打包环境的临时目录
    if hasattr(sys, '_MEIPASS'):
        base_path = sys._MEIPASS
        possible_paths.append(os.path.join(base_path, "server_data.db"))
    
    # 在临时目录中查找
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        mei_patterns = [
            os.path.join(temp_dir, "_MEI*", "server_data.db"),
            os.path.join(temp_dir, "_MEI*", "_internal", "server_data.db")
        ]
        for pattern in mei_patterns:
            matches = glob.glob(pattern)
            possible_paths.extend(matches)
    
    # 返回存在的数据库文件
    existing_dbs = []
    for path in possible_paths:
        if os.path.exists(path):
            existing_dbs.append(path)
    
    return existing_dbs

def fix_database_structure(db_path):
    """修复单个数据库的结构"""
    logger.info(f"修复数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. 创建client_updates表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 2. 检查现有表结构并添加缺失的列
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0",
            "is_folder_update": "INTEGER DEFAULT 0",
            "file_path": "TEXT",
            "file_size": "INTEGER",
            "file_hash": "TEXT",
            "download_count": "INTEGER DEFAULT 0",
            "status": "TEXT DEFAULT 'pending'"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"✅ 添加列: {column}")
                    print(f"✅ 添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
        
        # 3. 创建其他必要的表
        tables = {
            "api_configs": """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "logs": """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "live_status": """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """,
            "tokens": """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """,
            "danmaku_records": """
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                sender TEXT,
                content TEXT NOT NULL,
                timestamp TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        }
        
        for table_name, create_sql in tables.items():
            cursor.execute(create_sql)
            logger.info(f"✅ 确保表存在: {table_name}")
            print(f"✅ 确保表存在: {table_name}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"数据库修复完成: {db_path}")
        print(f"✅ 数据库修复完成: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        print(f"❌ 数据库修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始全面修复数据库...")
    print("=" * 60)
    
    # 查找所有数据库文件
    db_files = find_database_files()
    
    if not db_files:
        print("⚠️  未找到数据库文件，创建新的数据库...")
        # 创建新的数据库
        db_path = os.path.join(os.getcwd(), "server_data.db")
        success = fix_database_structure(db_path)
    else:
        print(f"📁 找到 {len(db_files)} 个数据库文件:")
        for db_file in db_files:
            print(f"  - {db_file}")
        
        success = True
        for db_file in db_files:
            if not fix_database_structure(db_file):
                success = False
    
    print("=" * 60)
    if success:
        print("🎉 数据库修复完成！")
        print("💡 所有已知的数据库问题都已修复")
        print("💡 现在可以正常运行服务器了")
    else:
        print("❌ 数据库修复失败！")
        print("💡 请检查错误信息并重试")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
