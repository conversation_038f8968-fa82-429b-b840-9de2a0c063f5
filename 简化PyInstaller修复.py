#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化PyInstaller修复方案
专门解决server.py打包后运行出错的问题
"""

import os
import sys
import shutil
import subprocess

def create_fixed_server():
    """创建修复后的server.py"""
    print("🔧 创建修复后的server.py...")
    
    # 读取原始server.py
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 在文件开头添加PyInstaller修复代码
    fix_code = '''import os
import sys

# PyInstaller路径修复
def get_exe_dir():
    if getattr(sys, 'frozen', False):
        return os.path.dirname(sys.executable)
    return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    if getattr(sys, 'frozen', False):
        return os.path.join(sys._MEIPASS, relative_path)
    return relative_path

def get_data_path(relative_path):
    return os.path.join(get_exe_dir(), relative_path)

# 设置Flask路径
if getattr(sys, 'frozen', False):
    TEMPLATE_FOLDER = get_resource_path('templates')
    STATIC_FOLDER = get_resource_path('static')
else:
    TEMPLATE_FOLDER = 'templates'
    STATIC_FOLDER = 'static'

'''
    
    # 修复Flask应用创建
    if "app = Flask(__name__)" in content:
        content = content.replace(
            "app = Flask(__name__)",
            "app = Flask(__name__, template_folder=TEMPLATE_FOLDER, static_folder=STATIC_FOLDER)"
        )
    
    # 修复数据库路径
    content = content.replace(
        "sqlite3.connect('server_data.db')",
        "sqlite3.connect(get_data_path('server_data.db'))"
    )
    content = content.replace(
        'sqlite3.connect("server_data.db")',
        'sqlite3.connect(get_data_path("server_data.db"))'
    )
    content = content.replace(
        "sqlite3.connect('local.db')",
        "sqlite3.connect(get_data_path('local.db'))"
    )
    content = content.replace(
        'sqlite3.connect("local.db")',
        'sqlite3.connect(get_data_path("local.db"))'
    )
    
    # 合并代码
    modified_content = fix_code + '\n' + content
    
    # 保存修复后的文件
    with open('server_fixed_simple.py', 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 修复后的server.py已保存为: server_fixed_simple.py")

def create_simple_spec():
    """创建简单的PyInstaller配置"""
    print("📝 创建PyInstaller配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['server_fixed_simple.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('gift_id_map.json', '.'),
        ('keyword_response_pairs.json', '.'),
        ('user_manager.py', '.'),
        ('server_data.db', '.'),
        ('local.db', '.'),
    ],
    hiddenimports=[
        'waitress', 'flask', 'flask_cors', 'flask_socketio', 'pymysql', 'sqlite3',
        'json', 'logging', 'datetime', 'os', 'sys', 'threading', 'time', 'requests',
        'urllib.parse', 'hashlib', 'base64', 'uuid', 'psutil', 'gevent', 'eventlet',
        'socketio', 'engineio', 'jinja2', 'werkzeug', 'click', 'itsdangerous',
        'markupsafe', 'blinker', 'cryptography', 'cffi', 'pycparser', 'six',
        'greenlet', 'zope.interface', 'zope.event',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'pandas', 'scipy'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统服务器_简化修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('AI主播系统服务器_简化修复版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller配置创建完成")

def prepare_files():
    """准备必要文件"""
    print("📁 准备必要文件...")
    
    # 确保数据库文件存在
    if not os.path.exists('server_data.db'):
        print("创建server_data.db...")
        import sqlite3
        conn = sqlite3.connect('server_data.db')
        cursor = conn.cursor()
        
        cursor.execute('''
CREATE TABLE IF NOT EXISTS client_updates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL,
    release_date TEXT NOT NULL,
    description TEXT,
    download_url TEXT,
    fast_download_url TEXT,
    force_update INTEGER DEFAULT 0,
    is_exe INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)''')
        
        cursor.execute('''
INSERT OR REPLACE INTO client_updates 
(id, version, release_date, description, download_url, fast_download_url, force_update, is_exe)
VALUES (1, '1.8', '2025-06-14', 
'1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
'http://localhost:12456/static/downloads/AI主播系统.zip',
'http://localhost:12456/api/fast-download/AI主播系统.zip', 0, 0)''')
        
        cursor.execute('''
CREATE TABLE IF NOT EXISTS api_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    endpoint TEXT NOT NULL,
    method TEXT DEFAULT 'GET',
    description TEXT,
    is_active INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)''')
        
        conn.commit()
        conn.close()
        print("✅ server_data.db 创建完成")
    
    if not os.path.exists('local.db'):
        print("创建local.db...")
        import sqlite3
        conn = sqlite3.connect('local.db')
        cursor = conn.cursor()
        
        cursor.execute('''
CREATE TABLE IF NOT EXISTS logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    module TEXT,
    function TEXT
)''')
        
        cursor.execute('''
CREATE TABLE IF NOT EXISTS live_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL,
    status TEXT DEFAULT 'offline',
    last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    room_id TEXT,
    platform TEXT,
    viewer_count INTEGER DEFAULT 0
)''')
        
        conn.commit()
        conn.close()
        print("✅ local.db 创建完成")

def run_packaging():
    """执行打包"""
    print("🚀 开始打包...")
    
    # 清理旧文件
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🧹 清理 {dir_name} 目录")
    
    # 执行打包
    cmd = ['pyinstaller', '--clean', '--noconfirm', 'AI主播系统服务器_简化修复版.spec']
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    bat_content = '''@echo off
chcp 65001 > nul
title AI主播系统服务器 - 简化修复版
echo.
echo ========================================
echo    AI主播系统服务器 - 简化修复版
echo ========================================
echo.
echo 正在启动服务器...
echo 服务器地址: http://localhost:12456
echo 管理后台: http://localhost:12456/admin
echo 默认账号: kaer / a13456A
echo.

"AI主播系统服务器_简化修复版.exe"

echo.
echo 服务器已停止运行
pause
'''
    
    with open('启动简化修复版.bat', 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print("✅ 启动脚本创建完成")

def main():
    """主函数"""
    print("🚀 简化PyInstaller修复方案")
    print("=" * 50)
    
    # 创建修复后的server.py
    create_fixed_server()
    
    # 准备必要文件
    prepare_files()
    
    # 创建PyInstaller配置
    create_simple_spec()
    
    # 创建启动脚本
    create_startup_script()
    
    # 执行打包
    if run_packaging():
        # 复制数据库文件到dist目录
        for db_file in ['server_data.db', 'local.db']:
            if os.path.exists(db_file):
                shutil.copy2(db_file, f'dist/{db_file}')
                print(f"✅ 复制 {db_file} 到 dist 目录")
        
        # 复制启动脚本
        shutil.copy2('启动简化修复版.bat', 'dist/')
        
        print("=" * 50)
        print("🎉 简化修复完成！")
        print("📁 输出目录: dist/")
        print("📦 可执行文件: AI主播系统服务器_简化修复版.exe")
        print("🚀 启动脚本: 启动简化修复版.bat")
        print("=" * 50)
        print("💡 修复内容:")
        print("✅ 修复了PyInstaller路径问题")
        print("✅ 修复了数据库文件路径问题")
        print("✅ 修复了模板和静态文件路径问题")
        print("✅ 确保数据库文件在exe同目录")
        print("=" * 50)
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 修复完成！现在打包后的程序应该和python server.py一样正常工作！")
    else:
        print("💥 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
