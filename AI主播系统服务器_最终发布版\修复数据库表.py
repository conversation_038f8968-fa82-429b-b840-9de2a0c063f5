#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库表脚本
创建缺失的数据库表，解决更新功能问题
"""

import os
import sys
import sqlite3
import glob

def find_database_file():
    """查找数据库文件"""
    possible_paths = [
        "server_data.db",
        "local.db",
        os.path.join(os.getcwd(), "server_data.db"),
        os.path.join(os.getcwd(), "local.db")
    ]
    
    # 在打包环境中查找
    if hasattr(sys, '_MEIPASS'):
        base_path = sys._MEIPASS
        possible_paths.extend([
            os.path.join(base_path, "server_data.db"),
            os.path.join(base_path, "local.db")
        ])
    
    # 在临时目录中查找
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        mei_patterns = [
            os.path.join(temp_dir, "_MEI*", "server_data.db"),
            os.path.join(temp_dir, "_MEI*", "local.db")
        ]
        for pattern in mei_patterns:
            matches = glob.glob(pattern)
            possible_paths.extend(matches)
    
    # 返回第一个存在的数据库文件
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    # 如果都不存在，创建新的
    return "server_data.db"

def create_missing_tables(db_path):
    """创建缺失的数据库表"""
    print(f"正在修复数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. 创建client_updates表
        print("创建client_updates表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 2. 创建logs表
        print("创建logs表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 3. 创建api_configs表（如果不存在）
        print("确保api_configs表存在...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 4. 创建danmaku_records表
        print("创建danmaku_records表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS danmaku_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            sender TEXT,
            content TEXT NOT NULL,
            timestamp TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 5. 创建live_status表
        print("创建live_status表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        # 6. 创建tokens表
        print("创建tokens表...")
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        # 提交更改
        conn.commit()
        conn.close()
        
        print("✅ 所有数据库表创建完成！")
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库表失败: {str(e)}")
        return False

def insert_sample_update():
    """插入示例更新记录"""
    db_path = find_database_file()
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 检查是否已有更新记录
        cursor.execute("SELECT COUNT(*) FROM client_updates")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("插入示例更新记录...")
            cursor.execute("""
            INSERT INTO client_updates (
                version, release_date, description, download_url, 
                force_update, is_current, status, fast_download_url
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "1.0.0",
                "2024-06-14",
                "初始版本发布",
                "/static/downloads/AI主播系统1.0.0.zip",
                0,
                1,
                "published",
                "/static/downloads/AI主播系统1.0.0.zip"
            ))
            
            conn.commit()
            print("✅ 示例更新记录插入完成！")
        else:
            print("ℹ️  更新记录已存在，跳过插入")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 插入示例更新记录失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始修复数据库表...")
    print("=" * 50)
    
    # 查找数据库文件
    db_path = find_database_file()
    print(f"数据库文件: {db_path}")
    
    # 创建缺失的表
    if create_missing_tables(db_path):
        print("\n✅ 数据库表修复成功！")
        
        # 插入示例更新记录
        if insert_sample_update():
            print("✅ 示例数据插入成功！")
        
        print("\n🎉 修复完成！现在可以正常使用更新功能了。")
        print("\n💡 修复内容:")
        print("- ✅ client_updates表 (更新功能)")
        print("- ✅ logs表 (日志功能)")
        print("- ✅ api_configs表 (API配置)")
        print("- ✅ danmaku_records表 (弹幕记录)")
        print("- ✅ live_status表 (直播状态)")
        print("- ✅ tokens表 (令牌管理)")
        print("- ✅ 示例更新记录")
        
    else:
        print("\n❌ 数据库表修复失败！")
    
    print("\n" + "=" * 50)
    input("按回车键退出...")

if __name__ == "__main__":
    main()
