#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统服务器数据独立打包脚本
将程序和数据分离打包，便于数据管理和迁移
"""

import os
import sys
import shutil
import subprocess
import sqlite3
import json
from datetime import datetime

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查必要文件
    required_files = [
        'server.py',
        'config.py',
        'templates',
        'static'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def create_data_directory():
    """创建数据目录结构"""
    print("📁 创建数据目录结构...")
    
    # 创建数据目录
    data_dirs = [
        'data',
        'data/database',
        'data/uploads',
        'data/downloads',
        'data/logs',
        'data/config',
        'data/backup'
    ]
    
    for dir_path in data_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"  ✅ 创建目录: {dir_path}")
    
    return True

def prepare_database_files():
    """准备数据库文件"""
    print("🗄️ 准备数据库文件...")
    
    # 创建独立的数据库文件
    db_files = {
        'data/database/server_data.db': '主数据库',
        'data/database/local.db': '本地数据库'
    }
    
    for db_path, description in db_files.items():
        print(f"  📝 创建{description}: {db_path}")
        
        # 如果源文件存在，复制过去
        source_file = os.path.basename(db_path)
        if os.path.exists(source_file):
            shutil.copy2(source_file, db_path)
            print(f"    ✅ 复制现有数据库文件")
        else:
            # 创建新的数据库文件
            create_database_structure(db_path)
            print(f"    ✅ 创建新数据库文件")
    
    return True

def create_database_structure(db_path):
    """创建数据库结构"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL,
                release_date TEXT NOT NULL,
                description TEXT,
                download_url TEXT,
                fast_download_url TEXT,
                force_update INTEGER DEFAULT 0,
                is_exe INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入示例数据
        cursor.execute('''
            INSERT OR REPLACE INTO client_updates (id, version, release_date, description, download_url, fast_download_url, force_update, is_exe)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '1.8',
            '2025-06-14',
            '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
            'http://localhost:12456/static/downloads/AI主播系统.zip',
            'http://localhost:12456/api/fast-download/AI主播系统.zip',
            0,
            0
        ))
        
        # 创建api_configs表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                endpoint TEXT NOT NULL,
                method TEXT DEFAULT 'GET',
                description TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建live_status表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                status TEXT DEFAULT 'offline',
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                room_id TEXT,
                platform TEXT,
                viewer_count INTEGER DEFAULT 0
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"    ✅ 数据库结构创建完成")
        
    except Exception as e:
        print(f"    ❌ 数据库创建失败: {e}")

def create_config_files():
    """创建配置文件"""
    print("⚙️ 创建配置文件...")
    
    # 数据库配置
    db_config = {
        "database": {
            "sqlite": {
                "path": "./data/database/server_data.db",
                "local_path": "./data/database/local.db"
            },
            "mysql": {
                "host": "gz-cynosdbmysql-grp-4ow8k8kw.sql.tencentcdb.com",
                "port": 25641,
                "user": "root",
                "password": "Aa123456",
                "database": "aizhubo"
            }
        },
        "server": {
            "host": "0.0.0.0",
            "port": 12456,
            "debug": False
        },
        "paths": {
            "uploads": "./data/uploads",
            "downloads": "./data/downloads",
            "logs": "./data/logs",
            "backup": "./data/backup"
        }
    }
    
    with open('data/config/database.json', 'w', encoding='utf-8') as f:
        json.dump(db_config, f, indent=2, ensure_ascii=False)
    
    # 应用配置
    app_config = {
        "app_name": "AI主播系统服务器",
        "version": "1.8",
        "data_directory": "./data",
        "auto_backup": True,
        "backup_interval": 24,  # 小时
        "log_level": "INFO",
        "max_log_files": 10
    }
    
    with open('data/config/app.json', 'w', encoding='utf-8') as f:
        json.dump(app_config, f, indent=2, ensure_ascii=False)
    
    print("  ✅ 配置文件创建完成")

def modify_server_py():
    """修改server.py以支持数据独立"""
    print("🔧 修改server.py以支持数据独立...")
    
    # 读取原始server.py
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加数据目录配置
    data_config_code = '''
# 数据独立配置
import json
import os

def load_data_config():
    """加载数据配置"""
    config_path = os.path.join(os.path.dirname(__file__), 'data', 'config', 'database.json')
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def get_data_path(relative_path):
    """获取数据文件路径"""
    base_dir = os.path.dirname(__file__)
    return os.path.join(base_dir, 'data', relative_path)

# 加载数据配置
DATA_CONFIG = load_data_config()
if DATA_CONFIG:
    # 更新数据库路径
    if 'database' in DATA_CONFIG:
        sqlite_config = DATA_CONFIG['database'].get('sqlite', {})
        if 'path' in sqlite_config:
            # 这里可以根据需要更新数据库路径
            pass

'''
    
    # 在文件开头添加数据配置代码
    modified_content = data_config_code + '\n' + content
    
    # 保存修改后的文件
    with open('server_data_independent.py', 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("  ✅ 创建数据独立版本: server_data_independent.py")

def create_spec_file():
    """创建PyInstaller配置文件"""
    print("📝 创建打包配置文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server_data_independent.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('gift_id_map.json', '.'),
        ('keyword_response_pairs.json', '.'),
        ('data', 'data'),
    ],
    hiddenimports=[
        'waitress',
        'flask',
        'flask_cors',
        'flask_socketio',
        'pymysql',
        'sqlite3',
        'json',
        'logging',
        'datetime',
        'os',
        'sys',
        'threading',
        'time',
        'requests',
        'urllib.parse',
        'hashlib',
        'base64',
        'uuid',
        'psutil',
        'gevent',
        'eventlet',
        'socketio',
        'engineio',
        'jinja2',
        'werkzeug',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'cryptography',
        'cffi',
        'pycparser',
        'six',
        'greenlet',
        'zope.interface',
        'zope.event',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统服务器_数据独立版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('AI主播系统服务器_数据独立版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 配置文件创建完成: AI主播系统服务器_数据独立版.spec")

def run_packaging():
    """执行打包"""
    print("🚀 开始打包...")
    
    # 清理旧的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理旧的build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的dist目录")
    
    # 执行打包命令
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'AI主播系统服务器_数据独立版.spec'
    ]
    
    print(f"📦 执行打包命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_data_package():
    """创建数据包"""
    print("📦 创建数据包...")
    
    # 复制数据目录到dist
    if os.path.exists('dist/data'):
        shutil.rmtree('dist/data')
    
    shutil.copytree('data', 'dist/data')
    print("  ✅ 数据目录复制完成")
    
    # 创建数据包说明
    data_readme = f'''# 数据包说明

## 📁 目录结构
```
data/
├── database/           # 数据库文件
│   ├── server_data.db  # 主数据库
│   └── local.db        # 本地数据库
├── uploads/            # 上传文件
├── downloads/          # 下载文件
├── logs/              # 日志文件
├── config/            # 配置文件
│   ├── database.json  # 数据库配置
│   └── app.json       # 应用配置
└── backup/            # 备份文件

```

## 🔧 使用方法

### 1. 数据迁移
将整个 `data` 目录复制到新环境即可完成数据迁移

### 2. 数据备份
定期备份 `data` 目录，特别是 `database` 子目录

### 3. 配置修改
修改 `data/config/` 下的配置文件来调整系统设置

## 📊 数据库说明

### server_data.db
- client_updates: 客户端更新信息
- api_configs: API配置信息
- live_status: 直播状态信息

### local.db
- 本地缓存数据
- 临时数据存储

## 🔄 数据恢复
1. 停止服务器
2. 替换 `data` 目录
3. 重新启动服务器

---
📅 创建时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
'''
    
    with open('dist/data/README.md', 'w', encoding='utf-8') as f:
        f.write(data_readme)
    
    print("  ✅ 数据包说明创建完成")

def main():
    """主函数"""
    print("🚀 AI主播系统服务器数据独立打包工具")
    print("=" * 60)
    
    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请解决问题后重试")
        return False
    
    # 创建数据目录
    create_data_directory()
    
    # 准备数据库文件
    prepare_database_files()
    
    # 创建配置文件
    create_config_files()
    
    # 修改server.py
    modify_server_py()
    
    # 创建配置文件
    create_spec_file()
    
    # 执行打包
    if not run_packaging():
        print("❌ 打包失败")
        return False
    
    # 创建数据包
    create_data_package()
    
    # 检查打包结果
    exe_path = 'dist/AI主播系统服务器_数据独立版.exe'
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print("=" * 60)
        print("🎉 数据独立打包完成！")
        print(f"📁 输出目录: dist/")
        print(f"📦 可执行文件: {exe_path}")
        print(f"💾 程序大小: {file_size:.1f} MB")
        print(f"📊 数据目录: dist/data/")
        print("=" * 60)
        print("🚀 特性:")
        print("✅ 程序和数据完全分离")
        print("✅ 数据可独立备份和迁移")
        print("✅ 配置文件独立管理")
        print("✅ 支持数据热备份")
        print("=" * 60)
        return True
    else:
        print("❌ 打包失败，未找到可执行文件")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 数据独立打包成功！")
        print("💡 现在程序和数据完全分离，便于管理和迁移！")
    else:
        print("💥 打包失败，请检查错误信息")
    
    input("\n按回车键退出...")
