// 用户管理修复脚本
// 在浏览器控制台中运行此脚本来修复编辑按钮功能

console.log('🔧 开始修复用户管理编辑按钮...');

// 定义initUserManagement函数
function initUserManagement() {
    console.log('📝 初始化用户管理功能...');
    
    // 初始化编辑按钮事件
    const editButtons = document.querySelectorAll('.edit-user-btn');
    console.log(`找到 ${editButtons.length} 个编辑按钮`);
    
    editButtons.forEach((button, index) => {
        // 移除旧的事件监听器
        button.replaceWith(button.cloneNode(true));
        const newButton = document.querySelectorAll('.edit-user-btn')[index];
        
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🖱️ 编辑按钮被点击');
            
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            const phone = this.getAttribute('data-phone');
            const machineCode = this.getAttribute('data-machine-code');
            const expireTime = this.getAttribute('data-expire-time');
            const status = this.getAttribute('data-status');
            const remark = this.getAttribute('data-remark');

            console.log('📋 用户数据:', { userId, username, phone, machineCode, expireTime, status, remark });

            // 检查编辑表单元素是否存在
            const editForm = document.getElementById('edit-user-modal');
            if (!editForm) {
                console.error('❌ 编辑表单不存在，创建简单的编辑对话框');
                showSimpleEditDialog(userId, username, phone, machineCode, expireTime, status, remark);
                return;
            }

            // 填充编辑表单
            const fields = {
                'edit-user-id': userId,
                'edit-username': username,
                'edit-phone': phone,
                'edit-machine-code': machineCode,
                'edit-expire-time': expireTime,
                'edit-status': status,
                'edit-remark': remark
            };

            for (const [fieldId, value] of Object.entries(fields)) {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.value = value || '';
                    console.log(`✅ 设置字段 ${fieldId}: ${value}`);
                } else {
                    console.warn(`⚠️ 字段不存在: ${fieldId}`);
                }
            }

            // 显示模态框
            showModal('edit-user-modal');
        });
    });

    // 初始化删除按钮事件
    const deleteButtons = document.querySelectorAll('.delete-user-btn');
    console.log(`找到 ${deleteButtons.length} 个删除按钮`);
    
    deleteButtons.forEach((button, index) => {
        // 移除旧的事件监听器
        button.replaceWith(button.cloneNode(true));
        const newButton = document.querySelectorAll('.delete-user-btn')[index];
        
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🗑️ 删除按钮被点击');
            
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');

            if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
                deleteUser(userId);
            }
        });
    });
    
    console.log('✅ 用户管理功能初始化完成');
}

// 显示模态框函数
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        console.log(`✅ 显示模态框: ${modalId}`);
    } else {
        console.error(`❌ 模态框不存在: ${modalId}`);
    }
}

// 关闭模态框函数
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        console.log(`✅ 关闭模态框: ${modalId}`);
    }
}

// 简单的编辑对话框
function showSimpleEditDialog(userId, username, phone, machineCode, expireTime, status, remark) {
    const newPhone = prompt(`编辑用户 ${username} 的手机号:`, phone || '');
    if (newPhone === null) return; // 用户取消
    
    const newRemark = prompt(`编辑用户 ${username} 的备注:`, remark || '');
    if (newRemark === null) return; // 用户取消
    
    const newStatus = prompt(`编辑用户 ${username} 的状态 (0=禁用, 1=启用):`, status || '1');
    if (newStatus === null) return; // 用户取消
    
    // 发送更新请求
    const formData = {
        phone: newPhone,
        machine_code: machineCode,
        expire_time: expireTime,
        status: parseInt(newStatus),
        remark: newRemark
    };
    
    console.log('📤 发送更新请求:', formData);
    
    fetch(`/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        console.log('📥 更新响应:', data);
        if (data.状态 === '成功') {
            alert('用户信息更新成功');
            location.reload(); // 重新加载页面
        } else {
            alert('更新失败: ' + data.信息);
        }
    })
    .catch(error => {
        console.error('❌ 更新用户信息出错:', error);
        alert('更新失败，请稍后重试');
    });
}

// 删除用户函数
function deleteUser(userId) {
    console.log(`🗑️ 删除用户: ${userId}`);
    
    fetch(`/admin/users/${userId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('📥 删除响应:', data);
        if (data.状态 === '成功') {
            alert('用户删除成功');
            location.reload(); // 重新加载页面
        } else {
            alert('删除失败: ' + data.信息);
        }
    })
    .catch(error => {
        console.error('❌ 删除用户出错:', error);
        alert('删除失败，请稍后重试');
    });
}

// 立即执行修复
try {
    initUserManagement();
    console.log('🎉 用户管理修复完成！编辑和删除按钮现在应该可以正常工作了。');
} catch (error) {
    console.error('❌ 修复过程中出错:', error);
}

// 将函数添加到全局作用域，以便其他代码可以调用
window.initUserManagement = initUserManagement;
window.showModal = showModal;
window.closeModal = closeModal;
window.deleteUser = deleteUser;

console.log('🔧 修复脚本加载完成！如果编辑按钮仍然不工作，请刷新页面后再次运行此脚本。');
