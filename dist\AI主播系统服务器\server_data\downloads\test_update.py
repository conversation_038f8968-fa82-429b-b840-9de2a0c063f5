import tkinter as tk
from tkinter import messagebox
import os

def main():
    # 创建主窗口
    root = tk.Tk()
    root.title("更新测试程序")
    root.geometry("400x200")
    
    # 显示版本信息
    version_label = tk.Label(root, text="AI主播系统 1.1.0 版本", font=("Arial", 16))
    version_label.pack(pady=20)
    
    # 显示更新成功信息
    update_label = tk.Label(root, text="更新已成功安装！", font=("Arial", 12))
    update_label.pack(pady=10)
    
    # 显示当前时间
    import datetime
    time_label = tk.Label(root, text=f"当前时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    time_label.pack(pady=10)
    
    # 显示文件路径
    path_label = tk.Label(root, text=f"程序路径: {os.path.abspath(__file__)}")
    path_label.pack(pady=10)
    
    # 启动主循环
    root.mainloop()

if __name__ == "__main__":
    main()
