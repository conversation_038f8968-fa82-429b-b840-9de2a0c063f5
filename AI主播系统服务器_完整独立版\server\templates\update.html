<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件更新 - AI主播系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 0;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            margin-top: 50px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .update-info {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .update-info h2 {
            margin-top: 0;
            color: #3498db;
        }
        .update-info p {
            margin: 10px 0;
            line-height: 1.6;
        }
        .update-info .version {
            font-weight: bold;
            font-size: 18px;
        }
        .update-info .date {
            color: #7f8c8d;
            font-style: italic;
        }
        .update-info .description {
            white-space: pre-line;
            margin-top: 15px;
        }
        .download-btn {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #3498db;
            color: white;
            text-align: center;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .download-btn:hover {
            background-color: #2980b9;
        }
        .force-update {
            color: #e74c3c;
            font-weight: bold;
            margin-top: 10px;
        }
        .loading {
            text-align: center;
            padding: 30px;
        }
        .error {
            color: #e74c3c;
            text-align: center;
            padding: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI主播系统更新</h1>
        
        <div id="loading" class="loading">
            <p>正在获取更新信息，请稍候...</p>
        </div>
        
        <div id="error" class="error" style="display: none;">
            <p>获取更新信息失败，请稍后再试。</p>
        </div>
        
        <div id="update-info" class="update-info" style="display: none;">
            <h2>发现新版本</h2>
            <p class="version">版本: <span id="version"></span></p>
            <p class="date">发布日期: <span id="release-date"></span></p>
            <div id="force-update" class="force-update" style="display: none;">
                <p>⚠️ 这是一个强制更新，您必须更新才能继续使用软件。</p>
            </div>
            <h3>更新内容:</h3>
            <p class="description" id="description"></p>
            
            <a id="download-btn" href="#" class="download-btn">下载更新</a>
        </div>
        
        <div id="no-update" style="display: none;">
            <p style="text-align: center; font-size: 18px;">您当前使用的已经是最新版本。</p>
        </div>
        
        <div class="footer">
            <p>© 2023-2024 AI主播系统 版权所有</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数中获取版本信息
            const urlParams = new URLSearchParams(window.location.search);
            const clientVersion = urlParams.get('version');
            const machineCode = urlParams.get('machine_code');
            
            // 如果URL中没有版本信息，尝试从本地存储获取
            const storedVersion = localStorage.getItem('client_version');
            const storedMachineCode = localStorage.getItem('machine_code');
            
            const version = clientVersion || storedVersion || '未知';
            const machine = machineCode || storedMachineCode || '未知';
            
            // 检查更新
            checkUpdate(version, machine);
        });
        
        function checkUpdate(version, machineCode) {
            fetch('/api/check-update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version: version,
                    machine_code: machineCode
                })
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loading').style.display = 'none';
                
                if (data.状态 === '成功') {
                    if (data.数据.has_update) {
                        // 显示更新信息
                        document.getElementById('update-info').style.display = 'block';
                        document.getElementById('version').textContent = data.数据.version;
                        document.getElementById('release-date').textContent = data.数据.release_date;
                        document.getElementById('description').textContent = data.数据.description;
                        document.getElementById('download-btn').href = data.数据.download_url;
                        
                        // 如果是强制更新，显示强制更新提示
                        if (data.数据.force_update) {
                            document.getElementById('force-update').style.display = 'block';
                        }
                        
                        // 保存版本信息到本地存储
                        localStorage.setItem('client_version', version);
                        localStorage.setItem('machine_code', machineCode);
                    } else {
                        // 显示无更新信息
                        document.getElementById('no-update').style.display = 'block';
                    }
                } else {
                    // 显示错误信息
                    document.getElementById('error').style.display = 'block';
                    document.getElementById('error').querySelector('p').textContent = 
                        '获取更新信息失败: ' + (data.信息 || '未知错误');
                }
            })
            .catch(error => {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').querySelector('p').textContent = 
                    '获取更新信息失败: ' + error.message;
            });
        }
    </script>
</body>
</html>
