<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .container {
            max-width: 1200px;
            padding: 0;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .setting-section {
            margin-bottom: 30px;
        }
        .setting-item {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings" class="active"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>系统设置</h1>
                <div>
                    <button id="refreshBtn" class="btn btn-primary me-2">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">服务器设置</h5>
                    </div>
                    <div class="card-body">
                        <form id="serverSettingsForm">
                            <div class="setting-section">
                                <h6>基本设置</h6>
                                <div class="setting-item">
                                    <label for="serverPort" class="form-label">服务器端口</label>
                                    <input type="number" class="form-control" id="serverPort" value="12456">
                                    <div class="form-text">修改后需要重启服务器生效</div>
                                </div>
                                <div class="setting-item">
                                    <label for="debugMode" class="form-label">调试模式</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="debugMode" checked>
                                        <label class="form-check-label" for="debugMode">启用调试模式</label>
                                    </div>
                                </div>
                            </div>

                            <div class="setting-section">
                                <h6>数据库设置</h6>
                                <div class="setting-item">
                                    <label for="dbHost" class="form-label">数据库主机</label>
                                    <input type="text" class="form-control" id="dbHost" value="**************">
                                </div>
                                <div class="setting-item">
                                    <label for="dbPort" class="form-label">数据库端口</label>
                                    <input type="number" class="form-control" id="dbPort" value="3306">
                                </div>
                                <div class="setting-item">
                                    <label for="dbName" class="form-label">数据库名称</label>
                                    <input type="text" class="form-control" id="dbName" value="reg">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">保存服务器设置</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">应用设置</h5>
                    </div>
                    <div class="card-body">
                        <form id="appSettingsForm">
                            <div class="setting-section">
                                <h6>用户设置</h6>
                                <div class="setting-item">
                                    <label for="tokenExpiry" class="form-label">Token有效期（天）</label>
                                    <input type="number" class="form-control" id="tokenExpiry" value="1">
                                </div>
                                <div class="setting-item">
                                    <label for="defaultExpiry" class="form-label">默认账号有效期（天）</label>
                                    <input type="number" class="form-control" id="defaultExpiry" value="30">
                                </div>
                            </div>

                            <div class="setting-section">
                                <h6>API设置</h6>
                                <div class="setting-item">
                                    <label for="voiceApiUrl" class="form-label">语音API地址</label>
                                    <input type="text" class="form-control" id="voiceApiUrl" value="http://ct.scjanelife.com/voice/speakers">
                                </div>
                                <div class="setting-item">
                                    <label for="enableCors" class="form-label">启用CORS</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enableCors" checked>
                                        <label class="form-check-label" for="enableCors">允许跨域请求</label>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">保存应用设置</button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">系统维护</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-3">
                            <button id="backupDbBtn" class="btn btn-warning">
                                <i class="bi bi-download"></i> 备份数据库
                            </button>
                            <button id="clearLogsBtn" class="btn btn-danger">
                                <i class="bi bi-trash"></i> 清理日志
                            </button>
                            <button id="restartServerBtn" class="btn btn-secondary">
                                <i class="bi bi-arrow-repeat"></i> 重启服务器
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin.js"></script>
    <script>
        // 退出登录功能
        document.getElementById('logout-btn').addEventListener('click', function() {
            fetch('/admin/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    window.location.href = '/admin';
                } else {
                    alert('退出登录失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('退出登录出错:', error);
                alert('退出登录出错，请重试');
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // 加载设置
            loadSettings();

            // 表单提交事件
            document.getElementById('serverSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveServerSettings();
            });

            document.getElementById('appSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveAppSettings();
            });

            // 系统维护按钮事件
            document.getElementById('backupDbBtn').addEventListener('click', backupDatabase);
            document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
            document.getElementById('restartServerBtn').addEventListener('click', restartServer);

            // 刷新按钮事件
            document.getElementById('refreshBtn').addEventListener('click', loadSettings);
        });

        function loadSettings() {
            // 这里应该从服务器加载设置
            fetch('/admin/api/settings')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        // 填充表单
                        const settings = data.数据;

                        // 服务器设置
                        if (settings.server) {
                            document.getElementById('serverPort').value = settings.server.port || 12456;
                            document.getElementById('debugMode').checked = settings.server.debug_mode || false;
                            document.getElementById('dbHost').value = settings.server.db_host || '**************';
                            document.getElementById('dbPort').value = settings.server.db_port || 3306;
                            document.getElementById('dbName').value = settings.server.db_name || 'reg';
                        }

                        // 应用设置
                        if (settings.app) {
                            document.getElementById('tokenExpiry').value = settings.app.token_expiry || 1;
                            document.getElementById('defaultExpiry').value = settings.app.default_expiry || 30;
                            document.getElementById('voiceApiUrl').value = settings.app.voice_api_url || 'http://ct.scjanelife.com/voice/speakers';
                            document.getElementById('enableCors').checked = settings.app.enable_cors || true;
                        }
                    } else {
                        alert('加载设置失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('加载设置出错:', error);
                    // 使用默认值
                });
        }

        function saveServerSettings() {
            const settings = {
                port: parseInt(document.getElementById('serverPort').value),
                debug_mode: document.getElementById('debugMode').checked,
                db_host: document.getElementById('dbHost').value,
                db_port: parseInt(document.getElementById('dbPort').value),
                db_name: document.getElementById('dbName').value
            };

            saveSettings('server', settings);
        }

        function saveAppSettings() {
            const settings = {
                token_expiry: parseInt(document.getElementById('tokenExpiry').value),
                default_expiry: parseInt(document.getElementById('defaultExpiry').value),
                voice_api_url: document.getElementById('voiceApiUrl').value,
                enable_cors: document.getElementById('enableCors').checked
            };

            saveSettings('app', settings);
        }

        function saveSettings(type, settings) {
            fetch('/admin/api/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: type,
                    settings: settings
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('设置保存成功');
                } else {
                    alert('保存设置失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('保存设置出错:', error);
                alert('保存设置出错，请重试');
            });
        }

        function backupDatabase() {
            if (confirm('确定要备份数据库吗？')) {
                fetch('/admin/api/backup', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert('数据库备份成功: ' + data.文件路径);
                    } else {
                        alert('数据库备份失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('备份数据库出错:', error);
                    alert('备份数据库出错，请重试');
                });
            }
        }

        function clearLogs() {
            if (confirm('确定要清理日志吗？此操作不可恢复！')) {
                fetch('/admin/api/logs/clear', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert('日志清理成功');
                    } else {
                        alert('日志清理失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('清理日志出错:', error);
                    alert('清理日志出错，请重试');
                });
            }
        }

        function restartServer() {
            if (confirm('确定要重启服务器吗？这将中断所有当前连接！')) {
                fetch('/admin/api/restart', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert('服务器正在重启，请稍后刷新页面');
                    } else {
                        alert('重启服务器失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('重启服务器出错:', error);
                    alert('重启服务器出错，请重试');
                });
            }
        }
    </script>
</body>
</html>
