<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播状态管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .online-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .danmaku-container {
            max-height: 150px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        .danmaku-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .danmaku-item:last-child {
            border-bottom: none;
        }
        .last-update {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">直播状态管理</h1>
        
        <div class="d-flex justify-content-between align-items-center mb-4">
            <button id="refreshBtn" class="btn btn-primary">
                <i class="bi bi-arrow-clockwise"></i> 刷新数据
            </button>
            <a href="/admin" class="btn btn-secondary">返回管理面板</a>
        </div>
        
        <div class="row" id="statusContainer">
            <!-- 直播状态卡片将在这里动态生成 -->
            <div class="col-12 text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载数据...</p>
            </div>
        </div>
    </div>

    <!-- 踢下线确认模态框 -->
    <div class="modal fade" id="kickModal" tabindex="-1" aria-labelledby="kickModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="kickModalLabel">确认踢下线</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要将用户 <span id="kickUsername" class="fw-bold"></span> 踢下线吗？
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmKickBtn">确认踢下线</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 格式化时间函数
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 格式化时间间隔
        function formatTimeAgo(dateTimeStr) {
            if (!dateTimeStr) return '';
            
            const date = new Date(dateTimeStr);
            const now = new Date();
            const diffMs = now - date;
            
            // 转换为秒
            const diffSec = Math.floor(diffMs / 1000);
            
            if (diffSec < 60) {
                return `${diffSec}秒前`;
            } else if (diffSec < 3600) {
                return `${Math.floor(diffSec / 60)}分钟前`;
            } else if (diffSec < 86400) {
                return `${Math.floor(diffSec / 3600)}小时前`;
            } else {
                return `${Math.floor(diffSec / 86400)}天前`;
            }
        }

        // 格式化播放时间
        function formatPlayTime(seconds) {
            if (!seconds && seconds !== 0) return '未知';
            
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;
            
            let result = '';
            if (hours > 0) {
                result += `${hours}小时`;
            }
            if (minutes > 0 || hours > 0) {
                result += `${minutes}分钟`;
            }
            result += `${remainingSeconds}秒`;
            
            return result;
        }

        // 加载直播状态数据
        function loadLiveStatus() {
            fetch('/live/status')
                .then(response => response.json())
                .then(data => {
                    const statusContainer = document.getElementById('statusContainer');
                    statusContainer.innerHTML = '';
                    
                    if (data.状态 === '成功' && data.数据 && data.数据.length > 0) {
                        data.数据.forEach(status => {
                            // 解析弹幕数据
                            let danmakuHtml = '<p class="text-muted">无弹幕数据</p>';
                            if (status.danmaku) {
                                try {
                                    const danmakuList = JSON.parse(status.danmaku);
                                    if (danmakuList && danmakuList.length > 0) {
                                        danmakuHtml = '<div class="danmaku-container">';
                                        danmakuList.forEach(item => {
                                            danmakuHtml += `<div class="danmaku-item">${item}</div>`;
                                        });
                                        danmakuHtml += '</div>';
                                    }
                                } catch (e) {
                                    danmakuHtml = `<p class="text-danger">弹幕数据解析错误: ${e.message}</p>`;
                                }
                            }
                            
                            // 创建状态卡片
                            const card = document.createElement('div');
                            card.className = 'col-md-6 col-lg-4 mb-4';
                            card.innerHTML = `
                                <div class="card status-card h-100">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">${status.username}</h5>
                                        <span class="badge bg-success online-badge">
                                            ${status.online_count ? status.online_count + '人在线' : '在线'}
                                        </span>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <strong>登录时间:</strong> ${formatDateTime(status.login_time)}
                                        </div>
                                        <div class="mb-3">
                                            <strong>播放时间:</strong> ${formatPlayTime(status.voice_play_time)}
                                        </div>
                                        <div class="mb-3">
                                            <strong>当前话术:</strong> ${status.current_script || '无'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>OBS源:</strong> ${status.obs_source || '未知'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>弹幕:</strong>
                                            ${danmakuHtml}
                                        </div>
                                        <div class="mb-3">
                                            <strong>IP地址:</strong> ${status.ip || '未知'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>机器码:</strong> ${status.machine_code || '未知'}
                                        </div>
                                        <p class="last-update mb-0 text-end">
                                            最后更新: ${formatTimeAgo(status.last_update_time)}
                                        </p>
                                    </div>
                                    <div class="card-footer">
                                        <button class="btn btn-danger w-100 kick-btn" data-username="${status.username}">
                                            踢下线
                                        </button>
                                    </div>
                                </div>
                            `;
                            statusContainer.appendChild(card);
                        });
                        
                        // 添加踢下线按钮事件
                        document.querySelectorAll('.kick-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const username = this.getAttribute('data-username');
                                document.getElementById('kickUsername').textContent = username;
                                
                                // 存储用户名到确认按钮
                                document.getElementById('confirmKickBtn').setAttribute('data-username', username);
                                
                                // 显示模态框
                                const kickModal = new bootstrap.Modal(document.getElementById('kickModal'));
                                kickModal.show();
                            });
                        });
                    } else {
                        statusContainer.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-info">
                                    当前没有直播状态数据
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取直播状态失败:', error);
                    document.getElementById('statusContainer').innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-danger">
                                获取直播状态失败: ${error.message}
                            </div>
                        </div>
                    `;
                });
        }

        // 踢下线用户
        function kickUser(username) {
            fetch(`/admin/users/${username}/kick`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert(`用户 ${username} 已成功踢下线`);
                    // 重新加载状态
                    loadLiveStatus();
                } else {
                    alert(`踢下线失败: ${data.信息}`);
                }
            })
            .catch(error => {
                console.error('踢下线请求失败:', error);
                alert(`踢下线请求失败: ${error.message}`);
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载初始数据
            loadLiveStatus();
            
            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', loadLiveStatus);
            
            // 确认踢下线按钮点击事件
            document.getElementById('confirmKickBtn').addEventListener('click', function() {
                const username = this.getAttribute('data-username');
                if (username) {
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('kickModal')).hide();
                    // 执行踢下线
                    kickUser(username);
                }
            });
            
            // 设置定时刷新 (每30秒)
            setInterval(loadLiveStatus, 30000);
        });
    </script>
</body>
</html>
