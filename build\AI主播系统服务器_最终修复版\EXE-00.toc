('C:\\Users\\<USER>\\wrzb\\backups\\5.28\\dist\\AI主播系统服务器_最终修复版.exe',
 True,
 False,
 False,
 'D:\\Program Files '
 '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-console.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\AI主播系统服务器_最终修复版.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('server',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server.py',
   'PYSOURCE'),
  ('python312.dll',
   'D:\\Program Files (x86)\\python32\\python312.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('gevent\\resolver\\cares.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\cares.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\libuv\\_corecffi.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi.pyd',
   'EXTENSION'),
  ('gevent\\libev\\corecext.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecext.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cqueue.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cqueue.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_clocal.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_clocal.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cevent.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cevent.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_imap.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_imap.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_ident.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_ident.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_c.cp312-win32.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_c.cp312-win32.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_writer.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_parser.cp312-win32.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\_frozenlist.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files (x86)\\python32\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files (x86)\\python32\\python3.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\Program Files (x86)\\python32\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('zlib1.dll', 'D:\\Program Files (x86)\\python32\\DLLs\\zlib1.dll', 'BINARY'),
  ('config.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\config.py',
   'DATA'),
  ('gift_id_map.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\gift_id_map.json',
   'DATA'),
  ('keyword_response_pairs.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\keyword_response_pairs.json',
   'DATA'),
  ('static\\admin\\api\\updates\\current',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\admin\\api\\updates\\current',
   'DATA'),
  ('static\\api\\check-update',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\api\\check-update',
   'DATA'),
  ('static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\css\\admin.css',
   'DATA'),
  ('static\\downloads\\fast_updates\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\downloads\\fast_updates\\version.json',
   'DATA'),
  ('static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\js\\admin.js',
   'DATA'),
  ('templates\\admin\\api_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\api_management.html',
   'DATA'),
  ('templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\card_management.html',
   'DATA'),
  ('templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\dashboard.html',
   'DATA'),
  ('templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\index.html',
   'DATA'),
  ('templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745245087',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745245087',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745246592',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745246592',
   'DATA'),
  ('templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745243487',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745243487',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745244551',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745244551',
   'DATA'),
  ('templates\\admin\\login.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\login.html',
   'DATA'),
  ('templates\\admin\\logs.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\logs.html',
   'DATA'),
  ('templates\\admin\\recharge.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\recharge.html',
   'DATA'),
  ('templates\\admin\\settings.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\stats.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\stats.html',
   'DATA'),
  ('templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\update_management.html',
   'DATA'),
  ('templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\user_management.html',
   'DATA'),
  ('templates\\update.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\update.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('zope.interface-7.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'DATA'),
  ('pycparser-2.22.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\entry_points.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\LICENSE',
   'DATA'),
  ('cffi-1.17.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\RECORD',
   'DATA'),
  ('zope.event-5.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('zope.interface-7.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\WHEEL',
   'DATA'),
  ('cffi-1.17.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\entry_points.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\REQUESTED',
   'DATA'),
  ('zope.event-5.0.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\namespace_packages.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\INSTALLER',
   'DATA'),
  ('gevent-25.5.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\RECORD',
   'DATA'),
  ('gevent-25.5.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\RECORD',
   'DATA'),
  ('zope.interface-7.2.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\namespace_packages.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\WHEEL',
   'DATA'),
  ('gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\RECORD',
   'DATA'),
  ('gevent-25.5.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\RECORD',
   'DATA'),
  ('pycparser-2.22.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\top_level.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\INSTALLER',
   'DATA'),
  ('pycparser-2.22.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_最终修复版\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-32bit-intel\\run.exe',
   'EXECUTABLE')],
 'D:\\Program Files (x86)\\python32\\python312.dll')
