<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新管理 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .update-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .update-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-group.full-width {
            grid-column: span 2;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary:hover {
            background-color: #45a049;
        }
        .update-history {
            margin-top: 30px;
        }
        .update-history h3 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .update-item {
            border-left: 3px solid #4CAF50;
            padding: 10px 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .update-item h4 {
            margin: 0 0 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .update-item p {
            margin: 5px 0;
        }
        .version-tag {
            background-color: #4CAF50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .update-actions {
            margin-top: 10px;
        }
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        .current-version {
            background-color: #2196F3;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management" class="active"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>
    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>更新管理</h2>
            </div>
            <div class="card-body">
                <div class="tabs">
                    <div class="tab active" data-tab="updates-list">更新列表</div>
                    <div class="tab" data-tab="add-update">添加更新</div>
                    <div class="tab" data-tab="upload-package">上传更新包</div>
                </div>

                <!-- 更新列表 -->
                <div class="tab-content active" id="updates-list">
                    <div class="update-list" id="update-items-container">
                        <p>加载中...</p>
                    </div>
                </div>

                <!-- 添加更新 -->
                <div class="tab-content" id="add-update">
                    <div class="update-form">
                        <h3>添加新版本</h3>
                        <form id="update-form">
                            <div class="form-group">
                                <label for="version">版本号</label>
                                <input type="text" id="version" class="form-control" placeholder="例如: 1.1.0" required>
                            </div>
                            <div class="form-group">
                                <label for="release-date">发布日期</label>
                                <input type="date" id="release-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="description">更新内容</label>
                                <textarea id="description" class="form-control" placeholder="每行一个更新内容，例如:&#10;1. 修复了xxx问题&#10;2. 优化了xxx功能" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="download-url">下载地址</label>
                                <input type="text" id="download-url" class="form-control" placeholder="例如: /downloads/update_1.1.0.zip" required>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="force-update"> 强制更新
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，用户必须更新才能继续使用软件</small>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存</button>
                                <button type="button" id="cancel-update-btn" class="btn btn-secondary">取消</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 上传更新包 -->
                <div class="tab-content" id="upload-package">
                    <div class="file-upload-container" id="drop-area">
                        <p>拖放更新包文件到这里，或者点击下方按钮选择文件</p>
                        <input type="file" id="file-upload" accept=".zip">
                        <label for="file-upload">选择文件</label>
                        <div class="upload-progress" id="upload-progress">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" id="progress-bar-fill"></div>
                            </div>
                            <div class="progress-text" id="progress-text">0%</div>
                        </div>
                    </div>
                    <div id="upload-result" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期为今天
            document.getElementById('release_date').valueAsDate = new Date();

            // 加载更新历史
            loadUpdateHistory();

            // 加载当前版本
            loadCurrentVersion();

            // 表单提交处理
            document.getElementById('update-form').addEventListener('submit', function(e) {
                e.preventDefault();
                publishUpdate();
            });

            // 退出登录
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        });

        // 加载更新历史
        function loadUpdateHistory() {
            fetch('/admin/api/updates')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        displayUpdateHistory(data.数据);
                    } else {
                        showError('加载更新历史失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    showError('加载更新历史出错: ' + error);
                });
        }

        // 加载当前版本
        function loadCurrentVersion() {
            fetch('/admin/api/updates/current')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        document.getElementById('current-version').textContent = data.数据.version || '未发布';
                    } else {
                        document.getElementById('current-version').textContent = '未发布';
                    }
                })
                .catch(error => {
                    document.getElementById('current-version').textContent = '加载失败';
                    console.error('加载当前版本出错:', error);
                });
        }

        // 显示更新历史
        function displayUpdateHistory(updates) {
            const updateList = document.getElementById('update-list');
            updateList.innerHTML = '';

            if (updates.length === 0) {
                updateList.innerHTML = '<p>暂无更新历史</p>';
                return;
            }

            updates.forEach(update => {
                const updateItem = document.createElement('div');
                updateItem.className = 'update-item';

                const header = document.createElement('h4');
                header.innerHTML = `
                    版本 ${update.version}
                    <span class="version-tag">${update.is_current ? '当前版本' : '历史版本'}</span>
                `;

                const releaseDate = document.createElement('p');
                releaseDate.textContent = `发布日期: ${update.release_date}`;

                const forceUpdate = document.createElement('p');
                forceUpdate.textContent = `强制更新: ${update.force_update ? '是' : '否'}`;

                const description = document.createElement('p');
                description.textContent = `更新说明: ${update.description}`;

                const downloadLink = document.createElement('p');
                downloadLink.innerHTML = `下载链接: <a href="${update.download_url}" target="_blank">${update.download_url}</a>`;

                const actions = document.createElement('div');
                actions.className = 'update-actions';

                if (update.is_current) {
                    // 当前版本不能删除
                    actions.innerHTML = `
                        <button class="btn-primary btn-sm" onclick="setAsCurrent('${update.id}')">已是当前版本</button>
                    `;
                } else {
                    actions.innerHTML = `
                        <button class="btn-primary btn-sm" onclick="setAsCurrent('${update.id}')">设为当前版本</button>
                        <button class="btn-danger btn-sm" onclick="deleteUpdate('${update.id}')">删除</button>
                    `;
                }

                updateItem.appendChild(header);
                updateItem.appendChild(releaseDate);
                updateItem.appendChild(forceUpdate);
                updateItem.appendChild(description);
                updateItem.appendChild(downloadLink);
                updateItem.appendChild(actions);

                updateList.appendChild(updateItem);
            });
        }

        // 发布更新
        function publishUpdate() {
            const form = document.getElementById('update-form');
            const formData = new FormData(form);

            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '发布中...';
            submitBtn.disabled = true;

            fetch('/admin/api/updates', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('更新发布成功!');
                    form.reset();
                    document.getElementById('release_date').valueAsDate = new Date();
                    loadUpdateHistory();
                    loadCurrentVersion();
                } else {
                    showError('发布更新失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('发布更新出错: ' + error);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        // 设置为当前版本
        function setAsCurrent(updateId) {
            if (!confirm('确定要将此版本设置为当前版本吗?')) {
                return;
            }

            fetch(`/admin/api/updates/${updateId}/set-current`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('已成功设置为当前版本!');
                    loadUpdateHistory();
                    loadCurrentVersion();
                } else {
                    showError('设置当前版本失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('设置当前版本出错: ' + error);
            });
        }

        // 删除更新
        function deleteUpdate(updateId) {
            if (!confirm('确定要删除此版本吗? 此操作不可恢复!')) {
                return;
            }

            fetch(`/admin/api/updates/${updateId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('版本已成功删除!');
                    loadUpdateHistory();
                } else {
                    showError('删除版本失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('删除版本出错: ' + error);
            });
        }

        // 退出登录
        function logout() {
            fetch('/admin/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    window.location.href = '/admin';
                } else {
                    showError('退出登录失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('退出登录出错: ' + error);
            });
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
            console.error(message);
        }
    </script>
</body>
</html>
