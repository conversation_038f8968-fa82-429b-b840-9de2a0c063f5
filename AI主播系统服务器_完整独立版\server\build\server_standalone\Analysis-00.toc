(['C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\server_standalone.py'],
 ['C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server'],
 [],
 [('D:\\Program Files (x86)\\python32\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pygame\\__pyinstaller',
   0),
  ('D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('config.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\config.py',
   'DATA'),
  ('gift_id_map.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\gift_id_map.json',
   'DATA'),
  ('keyword_response_pairs.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\keyword_response_pairs.json',
   'DATA'),
  ('static\\admin\\api\\updates\\current',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\admin\\api\\updates\\current',
   'DATA'),
  ('static\\api\\check-update',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\api\\check-update',
   'DATA'),
  ('static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\css\\admin.css',
   'DATA'),
  ('static\\downloads\\fast_updates\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\downloads\\fast_updates\\version.json',
   'DATA'),
  ('static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\js\\admin.js',
   'DATA'),
  ('templates\\admin\\api_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\api_management.html',
   'DATA'),
  ('templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\card_management.html',
   'DATA'),
  ('templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\dashboard.html',
   'DATA'),
  ('templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\index.html',
   'DATA'),
  ('templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745245087',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html.bak.1745245087',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745246592',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html.bak.1745246592',
   'DATA'),
  ('templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745243487',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html.bak.1745243487',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745244551',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html.bak.1745244551',
   'DATA'),
  ('templates\\admin\\login.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\login.html',
   'DATA'),
  ('templates\\admin\\logs.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\logs.html',
   'DATA'),
  ('templates\\admin\\recharge.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\recharge.html',
   'DATA'),
  ('templates\\admin\\settings.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\stats.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\stats.html',
   'DATA'),
  ('templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\update_management.html',
   'DATA'),
  ('templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\user_management.html',
   'DATA'),
  ('templates\\update.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\update.html',
   'DATA'),
  ('user_manager.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\user_manager.py',
   'DATA')],
 '3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 11:58:42) [MSC v.1943 32 bit '
 '(Intel)]',
 [('pyi_rth_inspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('server_standalone',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\server_standalone.py',
   'PYSOURCE')],
 [('pkgutil', 'D:\\Program Files (x86)\\python32\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\Program Files (x86)\\python32\\Lib\\zipimport.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('typing', 'D:\\Program Files (x86)\\python32\\Lib\\typing.py', 'PYMODULE'),
  ('contextlib',
   'D:\\Program Files (x86)\\python32\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Program Files (x86)\\python32\\Lib\\pathlib.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\Program Files (x86)\\python32\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files (x86)\\python32\\Lib\\fnmatch.py', 'PYMODULE'),
  ('inspect', 'D:\\Program Files (x86)\\python32\\Lib\\inspect.py', 'PYMODULE'),
  ('argparse',
   'D:\\Program Files (x86)\\python32\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Program Files (x86)\\python32\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files (x86)\\python32\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files (x86)\\python32\\Lib\\gettext.py', 'PYMODULE'),
  ('struct', 'D:\\Program Files (x86)\\python32\\Lib\\struct.py', 'PYMODULE'),
  ('token', 'D:\\Program Files (x86)\\python32\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Program Files (x86)\\python32\\Lib\\tokenize.py',
   'PYMODULE'),
  ('dis', 'D:\\Program Files (x86)\\python32\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files (x86)\\python32\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Program Files (x86)\\python32\\Lib\\ast.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files (x86)\\python32\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files (x86)\\python32\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files (x86)\\python32\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'D:\\Program Files (x86)\\python32\\Lib\\calendar.py',
   'PYMODULE'),
  ('socket', 'D:\\Program Files (x86)\\python32\\Lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\Program Files (x86)\\python32\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'D:\\Program Files (x86)\\python32\\Lib\\quopri.py', 'PYMODULE'),
  ('email',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('signal', 'D:\\Program Files (x86)\\python32\\Lib\\signal.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\Program Files (x86)\\python32\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'D:\\Program Files (x86)\\python32\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Program Files (x86)\\python32\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files (x86)\\python32\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Program Files (x86)\\python32\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Program Files (x86)\\python32\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Program Files (x86)\\python32\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files (x86)\\python32\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('bisect', 'D:\\Program Files (x86)\\python32\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files (x86)\\python32\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files (x86)\\python32\\Lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files (x86)\\python32\\Lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files (x86)\\python32\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files (x86)\\python32\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Program Files (x86)\\python32\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\Program Files (x86)\\python32\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files (x86)\\python32\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files (x86)\\python32\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files (x86)\\python32\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files (x86)\\python32\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Program Files (x86)\\python32\\Lib\\py_compile.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'D:\\Program Files (x86)\\python32\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig',
   'D:\\Program Files (x86)\\python32\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files (x86)\\python32\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Program Files (x86)\\python32\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\Program Files (x86)\\python32\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\Program Files (x86)\\python32\\Lib\\bz2.py', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files (x86)\\python32\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files (x86)\\python32\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files (x86)\\python32\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files (x86)\\python32\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files (x86)\\python32\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files (x86)\\python32\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Program Files (x86)\\python32\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files (x86)\\python32\\Lib\\tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files (x86)\\python32\\Lib\\configparser.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('glob', 'D:\\Program Files (x86)\\python32\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Program Files (x86)\\python32\\Lib\\__future__.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files (x86)\\python32\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files (x86)\\python32\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Program Files (x86)\\python32\\Lib\\stringprep.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('code', 'D:\\Program Files (x86)\\python32\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files (x86)\\python32\\Lib\\codeop.py', 'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('colorama',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('waitress',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\__init__.py',
   'PYMODULE'),
  ('pstats', 'D:\\Program Files (x86)\\python32\\Lib\\pstats.py', 'PYMODULE'),
  ('cmd', 'D:\\Program Files (x86)\\python32\\Lib\\cmd.py', 'PYMODULE'),
  ('profile', 'D:\\Program Files (x86)\\python32\\Lib\\profile.py', 'PYMODULE'),
  ('optparse',
   'D:\\Program Files (x86)\\python32\\Lib\\optparse.py',
   'PYMODULE'),
  ('waitress.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\server.py',
   'PYMODULE'),
  ('waitress.proxy_headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\proxy_headers.py',
   'PYMODULE'),
  ('waitress.task',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\task.py',
   'PYMODULE'),
  ('waitress.buffers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\buffers.py',
   'PYMODULE'),
  ('waitress.compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\compat.py',
   'PYMODULE'),
  ('waitress.channel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\channel.py',
   'PYMODULE'),
  ('waitress.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\parser.py',
   'PYMODULE'),
  ('waitress.rfc7230',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\rfc7230.py',
   'PYMODULE'),
  ('waitress.receiver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\receiver.py',
   'PYMODULE'),
  ('waitress.adjustments',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\adjustments.py',
   'PYMODULE'),
  ('waitress.trigger',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\trigger.py',
   'PYMODULE'),
  ('waitress.wasyncore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\wasyncore.py',
   'PYMODULE'),
  ('waitress.utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\utilities.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\subprocess.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files (x86)\\python32\\Lib\\csv.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files (x86)\\python32\\Lib\\shutil.py', 'PYMODULE'),
  ('tempfile',
   'D:\\Program Files (x86)\\python32\\Lib\\tempfile.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files (x86)\\python32\\Lib\\uuid.py', 'PYMODULE'),
  ('jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\__init__.py',
   'PYMODULE'),
  ('jwt.jwks_client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\jwks_client.py',
   'PYMODULE'),
  ('jwt.jwk_set_cache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jwt\\jwk_set_cache.py',
   'PYMODULE'),
  ('jwt.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\exceptions.py',
   'PYMODULE'),
  ('jwt.api_jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwt.py',
   'PYMODULE'),
  ('jwt.algorithms',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\algorithms.py',
   'PYMODULE'),
  ('jwt.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\utils.py',
   'PYMODULE'),
  ('jwt.types',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\types.py',
   'PYMODULE'),
  ('jwt.warnings',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\warnings.py',
   'PYMODULE'),
  ('jwt.api_jws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jws.py',
   'PYMODULE'),
  ('jwt.api_jwk',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwk.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Program Files (x86)\\python32\\Lib\\hashlib.py', 'PYMODULE'),
  ('user_manager',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\user_manager.py',
   'PYMODULE'),
  ('pymysql',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql.connections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.charset',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.converters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql._auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.times',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('pymysql.err',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('flask_socketio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey._main',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_main.py',
   'PYMODULE'),
  ('gevent.events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\events.py',
   'PYMODULE'),
  ('zope.event',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\event\\__init__.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.interface',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface.verify',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\verify.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('gevent.socket',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\socket.py',
   'PYMODULE'),
  ('gevent._socketcommon',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_socketcommon.py',
   'PYMODULE'),
  ('gevent.win32util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\win32util.py',
   'PYMODULE'),
  ('gevent.timeout',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\timeout.py',
   'PYMODULE'),
  ('greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gevent._hub_primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_hub_primitives.py',
   'PYMODULE'),
  ('gevent._waiter',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_waiter.py',
   'PYMODULE'),
  ('gevent.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\exceptions.py',
   'PYMODULE'),
  ('gevent._greenlet_primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_greenlet_primitives.py',
   'PYMODULE'),
  ('gevent._hub_local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_hub_local.py',
   'PYMODULE'),
  ('gevent._socket3',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_socket3.py',
   'PYMODULE'),
  ('gevent._util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_util.py',
   'PYMODULE'),
  ('gevent._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_compat.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_gte313',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_gte313.py',
   'PYMODULE'),
  ('gevent.thread',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\thread.py',
   'PYMODULE'),
  ('gevent.local',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\local.py',
   'PYMODULE'),
  ('gevent.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\lock.py',
   'PYMODULE'),
  ('gevent._semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_semaphore.py',
   'PYMODULE'),
  ('gevent.greenlet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\greenlet.py',
   'PYMODULE'),
  ('gevent._tblib',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_tblib.py',
   'PYMODULE'),
  ('gevent.hub',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\hub.py',
   'PYMODULE'),
  ('gevent._monitor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_monitor.py',
   'PYMODULE'),
  ('gevent._tracer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_tracer.py',
   'PYMODULE'),
  ('gevent.util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\util.py',
   'PYMODULE'),
  ('gevent._ident',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_ident.py',
   'PYMODULE'),
  ('gevent.threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\threading.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_common',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_common.py',
   'PYMODULE'),
  ('gevent.event',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\event.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_lt313',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_lt313.py',
   'PYMODULE'),
  ('gevent._config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_config.py',
   'PYMODULE'),
  ('gevent.fileobject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\fileobject.py',
   'PYMODULE'),
  ('gevent._fileobjectcommon',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_fileobjectcommon.py',
   'PYMODULE'),
  ('gevent._fileobjectposix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_fileobjectposix.py',
   'PYMODULE'),
  ('gevent.os',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\os.py',
   'PYMODULE'),
  ('gevent.monkey.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\api.py',
   'PYMODULE'),
  ('gevent.monkey._state',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_state.py',
   'PYMODULE'),
  ('gevent.monkey._util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_util.py',
   'PYMODULE'),
  ('gevent.monkey._errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_errors.py',
   'PYMODULE'),
  ('gevent.pywsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\pywsgi.py',
   'PYMODULE'),
  ('gevent.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\server.py',
   'PYMODULE'),
  ('gevent.ssl',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\ssl.py',
   'PYMODULE'),
  ('gevent.baseserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\baseserver.py',
   'PYMODULE'),
  ('gevent.pool',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\pool.py',
   'PYMODULE'),
  ('gevent._imap',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_imap.py',
   'PYMODULE'),
  ('gevent.queue',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\queue.py',
   'PYMODULE'),
  ('gevent',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.test__util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__util.py',
   'PYMODULE'),
  ('gevent.tests.test__timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool_executor_patched',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threadpool_executor_patched.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threadpool.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_vs_settrace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_vs_settrace.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_patched_local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_patched_local.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_native_before_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_native_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_monkey_in_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_monkey_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_holding_lock_while_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_holding_lock_while_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_fork_from_dummy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_fork_from_dummy.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_before_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_2.py',
   'PYMODULE'),
  ('gevent.tests.test__threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading.py',
   'PYMODULE'),
  ('gevent.tests.test__thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__thread.py',
   'PYMODULE'),
  ('gevent.tests.test__systemerror',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__systemerror.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_poll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess_poll.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_interrupted',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess_interrupted.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess.py',
   'PYMODULE'),
  ('gevent.tests.test__ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socketpair',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socketpair.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_send_memoryview',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_send_memoryview.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ex',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_ex.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_errors.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns6',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_dns6.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_dns.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_close',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_close.py',
   'PYMODULE'),
  ('gevent.tests.test__socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket.py',
   'PYMODULE'),
  ('gevent.tests.test__sleep0',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__sleep0.py',
   'PYMODULE'),
  ('gevent.tests.test__signal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__signal.py',
   'PYMODULE'),
  ('gevent.tests.test__server_pywsgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__server_pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__server.py',
   'PYMODULE'),
  ('gevent.tests.test__semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__semaphore.py',
   'PYMODULE'),
  ('gevent.tests.test__selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__select.py',
   'PYMODULE'),
  ('gevent.tests.test__resolver_dnspython',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__resolver_dnspython.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount_core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__refcount_core.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__refcount.py',
   'PYMODULE'),
  ('gevent.tests.test__real_greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__real_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__queue.py',
   'PYMODULE'),
  ('gevent.tests.test__pywsgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__pywsgi.py',
   'PYMODULE'),
  ('wsgiref.validate',
   'D:\\Program Files (x86)\\python32\\Lib\\wsgiref\\validate.py',
   'PYMODULE'),
  ('wsgiref',
   'D:\\Program Files (x86)\\python32\\Lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.test__pool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__pool.py',
   'PYMODULE'),
  ('gevent.tests.test__os',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__os.py',
   'PYMODULE'),
  ('gevent.tests.test__order',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__order.py',
   'PYMODULE'),
  ('gevent.tests.test__nondefaultloop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__nondefaultloop.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_select.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_queue.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_multiple_imports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_multiple_imports.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_module_run',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_module_run.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_logging.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_hub_in_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_hub_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_builtins_future',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_builtins_future.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey.py',
   'PYMODULE'),
  ('gevent.time',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\time.py',
   'PYMODULE'),
  ('gevent.tests.test__memleak',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__memleak.py',
   'PYMODULE'),
  ('gevent.tests.test__makefile_ref',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__makefile_ref.py',
   'PYMODULE'),
  ('gevent.tests.test__loop_callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__loop_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__lock',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__lock.py',
   'PYMODULE'),
  ('gevent.tests.test__local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__local.py',
   'PYMODULE'),
  ('gevent.tests.test__joinall',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__joinall.py',
   'PYMODULE'),
  ('gevent.tests.test__iwait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__iwait.py',
   'PYMODULE'),
  ('gevent.tests.test__issues461_471',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issues461_471.py',
   'PYMODULE'),
  ('gevent.tests.test__issue_728',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue_728.py',
   'PYMODULE'),
  ('gevent.tests.test__issue639',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue639.py',
   'PYMODULE'),
  ('gevent.tests.test__issue607',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue607.py',
   'PYMODULE'),
  ('gevent.tests.test__issue600',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue600.py',
   'PYMODULE'),
  ('gevent.tests.test__issue6',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue6.py',
   'PYMODULE'),
  ('gevent.tests.test__issue467',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue467.py',
   'PYMODULE'),
  ('gevent.tests.test__issue330',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue330.py',
   'PYMODULE'),
  ('gevent.tests.test__issue230',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue230.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1864',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue1864.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1686',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue1686.py',
   'PYMODULE'),
  ('gevent.tests.test__issue112',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue112.py',
   'PYMODULE'),
  ('gevent.tests.test__import_wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__import_wait.py',
   'PYMODULE'),
  ('gevent.tests.test__import_blocking_in_greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__import_blocking_in_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub_join_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub_join.py',
   'PYMODULE'),
  ('gevent.tests.test__hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub.py',
   'PYMODULE'),
  ('gevent.tests.test__greenness',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenness.py',
   'PYMODULE'),
  ('gevent.tests.test__greenletset',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenletset.py',
   'PYMODULE'),
  ('gevent.tests.test__greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__greenio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenio.py',
   'PYMODULE'),
  ('gevent.tests.test__getaddrinfo_import',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__getaddrinfo_import.py',
   'PYMODULE'),
  ('gevent.tests.test__fileobject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__fileobject.py',
   'PYMODULE'),
  ('gevent.tests.test__execmodules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__execmodules.py',
   'PYMODULE'),
  ('gevent.tests.test__exc_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__exc_info.py',
   'PYMODULE'),
  ('gevent.tests.test__examples',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__examples.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver_ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_webproxy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_webproxy.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_udp_server.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_udp_client.py',
   'PYMODULE'),
  ('gevent.tests.test__example_portforwarder',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_portforwarder.py',
   'PYMODULE'),
  ('gevent.tests.test__example_echoserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_echoserver.py',
   'PYMODULE'),
  ('gevent.tests.test__events',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__events.py',
   'PYMODULE'),
  ('gevent.tests.test__event',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__event.py',
   'PYMODULE'),
  ('gevent.tests.test__environ',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__environ.py',
   'PYMODULE'),
  ('gevent.tests.test__doctests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__doctests.py',
   'PYMODULE'),
  ('doctest', 'D:\\Program Files (x86)\\python32\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files (x86)\\python32\\Lib\\pdb.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files (x86)\\python32\\Lib\\bdb.py', 'PYMODULE'),
  ('gevent.tests.test__destroy_default_loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__destroy_default_loop.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__destroy.py',
   'PYMODULE'),
  ('gevent.tests.test__core_watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_watcher.py',
   'PYMODULE'),
  ('gevent.tests.test__core_timer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_timer.py',
   'PYMODULE'),
  ('gevent.tests.test__core_stat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_stat.py',
   'PYMODULE'),
  ('gevent.tests.test__core_loop_run',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_loop_run.py',
   'PYMODULE'),
  ('gevent.tests.test__core_fork',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_fork.py',
   'PYMODULE'),
  ('gevent.tests.test__core_callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__core_async',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_async.py',
   'PYMODULE'),
  ('gevent.tests.test__core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core.py',
   'PYMODULE'),
  ('gevent.tests.test__contextvars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__contextvars.py',
   'PYMODULE'),
  ('gevent.tests.test__compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__compat.py',
   'PYMODULE'),
  ('gevent.tests.test__close_backend_fd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__close_backend_fd.py',
   'PYMODULE'),
  ('gevent.tests.test__backdoor',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__backdoor.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ares_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_host_result',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ares_host_result.py',
   'PYMODULE'),
  ('gevent.tests.test__api_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__api_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__api.py',
   'PYMODULE'),
  ('gevent.tests.test__all__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__all__.py',
   'PYMODULE'),
  ('gevent.tests.test___monkey_patching',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___monkey_patching.py',
   'PYMODULE'),
  ('gevent.tests.test___monitor',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___monitor.py',
   'PYMODULE'),
  ('gevent.tests.test___ident',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___ident.py',
   'PYMODULE'),
  ('gevent.tests.test___config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___config.py',
   'PYMODULE'),
  ('gevent.tests.test__GreenletExit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__GreenletExit.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_monkey_patches',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_monkey_patches.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.script',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\script.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue302monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue302monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_with_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_with_monkey.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__main__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.lock_tests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\lock_tests.py',
   'PYMODULE'),
  ('gevent.tests.known_failures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\known_failures.py',
   'PYMODULE'),
  ('gevent.tests.getaddrinfo_module',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\getaddrinfo_module.py',
   'PYMODULE'),
  ('gevent.tests._imports_imports_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_imports_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._imports_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._import_wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_wait.py',
   'PYMODULE'),
  ('gevent.tests._import_patch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_import_patch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._blocks_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_blocks_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\__main__.py',
   'PYMODULE'),
  ('gevent.tests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\util.py',
   'PYMODULE'),
  ('gevent.testing.travis',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\travis.py',
   'PYMODULE'),
  ('gevent.testing.timing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\timing.py',
   'PYMODULE'),
  ('gevent.testing.testrunner',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\testrunner.py',
   'PYMODULE'),
  ('gevent.testing.testcase',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\testcase.py',
   'PYMODULE'),
  ('gevent.testing.sysinfo',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\sysinfo.py',
   'PYMODULE'),
  ('gevent.testing.switching',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\switching.py',
   'PYMODULE'),
  ('gevent.testing.support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\support.py',
   'PYMODULE'),
  ('test.support',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\threading_helper.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Program Files (x86)\\python32\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files (x86)\\python32\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('test',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.sockets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\sockets.py',
   'PYMODULE'),
  ('gevent.testing.skipping',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\skipping.py',
   'PYMODULE'),
  ('gevent.testing.six',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\six.py',
   'PYMODULE'),
  ('gevent.testing.resources',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\resources.py',
   'PYMODULE'),
  ('test.libregrtest',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\libregrtest\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.patched_tests_setup',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\patched_tests_setup.py',
   'PYMODULE'),
  ('gevent.testing.params',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\params.py',
   'PYMODULE'),
  ('gevent.testing.openfiles',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\openfiles.py',
   'PYMODULE'),
  ('gevent.testing.monkey_test',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\monkey_test.py',
   'PYMODULE'),
  ('test.lock_tests',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\lock_tests.py',
   'PYMODULE'),
  ('gevent.testing.modules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\modules.py',
   'PYMODULE'),
  ('gevent.testing.leakcheck',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\leakcheck.py',
   'PYMODULE'),
  ('gevent.testing.hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\hub.py',
   'PYMODULE'),
  ('gevent.testing.flaky',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\flaky.py',
   'PYMODULE'),
  ('gevent.testing.exception',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\exception.py',
   'PYMODULE'),
  ('gevent.testing.errorhandler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\errorhandler.py',
   'PYMODULE'),
  ('gevent.testing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\__init__.py',
   'PYMODULE'),
  ('gevent.signal',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\signal.py',
   'PYMODULE'),
  ('gevent.selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\selectors.py',
   'PYMODULE'),
  ('gevent.resolver.thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\thread.py',
   'PYMODULE'),
  ('gevent.resolver.dnspython',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\dnspython.py',
   'PYMODULE'),
  ('gevent.resolver.blocking',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\blocking.py',
   'PYMODULE'),
  ('gevent.resolver.ares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\ares.py',
   'PYMODULE'),
  ('gevent.resolver._hostsfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\_hostsfile.py',
   'PYMODULE'),
  ('gevent.resolver._addresses',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\_addresses.py',
   'PYMODULE'),
  ('gevent.resolver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\__main__.py',
   'PYMODULE'),
  ('gevent.libuv.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\watcher.py',
   'PYMODULE'),
  ('gevent.libuv.loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\loop.py',
   'PYMODULE'),
  ('gevent.libuv._corecffi_build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi_build.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('gevent.libuv',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\__init__.py',
   'PYMODULE'),
  ('gevent.libev.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\watcher.py',
   'PYMODULE'),
  ('gevent.libev.corecffi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecffi.py',
   'PYMODULE'),
  ('gevent.libev._corecffi_build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libev',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\__init__.py',
   'PYMODULE'),
  ('gevent.contextvars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\contextvars.py',
   'PYMODULE'),
  ('gevent.builtins',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\builtins.py',
   'PYMODULE'),
  ('gevent.backdoor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\backdoor.py',
   'PYMODULE'),
  ('gevent.ares',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\ares.py',
   'PYMODULE'),
  ('gevent._threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_threading.py',
   'PYMODULE'),
  ('gevent._patcher',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_patcher.py',
   'PYMODULE'),
  ('gevent._interfaces',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_interfaces.py',
   'PYMODULE'),
  ('gevent._ffi.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\watcher.py',
   'PYMODULE'),
  ('gevent._ffi.loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\loop.py',
   'PYMODULE'),
  ('gevent._ffi.callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\callback.py',
   'PYMODULE'),
  ('gevent._ffi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\__init__.py',
   'PYMODULE'),
  ('gevent._abstract_linkable',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_abstract_linkable.py',
   'PYMODULE'),
  ('gevent.subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\subprocess.py',
   'PYMODULE'),
  ('gevent.select',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\select.py',
   'PYMODULE'),
  ('gevent.threadpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\threadpool.py',
   'PYMODULE'),
  ('gevent.resolver_ares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver_ares.py',
   'PYMODULE'),
  ('gevent.resolver_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver_thread.py',
   'PYMODULE'),
  ('gevent.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\core.py',
   'PYMODULE'),
  ('eventlet.green',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.BaseHTTPServer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\BaseHTTPServer.py',
   'PYMODULE'),
  ('eventlet.patcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\patcher.py',
   'PYMODULE'),
  ('eventlet.green.thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\thread.py',
   'PYMODULE'),
  ('eventlet.corolocal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\corolocal.py',
   'PYMODULE'),
  ('eventlet.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\lock.py',
   'PYMODULE'),
  ('eventlet.semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\semaphore.py',
   'PYMODULE'),
  ('eventlet.hubs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\__init__.py',
   'PYMODULE'),
  ('eventlet.hubs.hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\hub.py',
   'PYMODULE'),
  ('eventlet.hubs.timer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\timer.py',
   'PYMODULE'),
  ('eventlet.timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\timeout.py',
   'PYMODULE'),
  ('eventlet.greenthread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenthread.py',
   'PYMODULE'),
  ('eventlet.hubs.asyncio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\asyncio.py',
   'PYMODULE'),
  ('eventlet.event',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\event.py',
   'PYMODULE'),
  ('eventlet.support.greenlets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\greenlets.py',
   'PYMODULE'),
  ('eventlet.support.psycopg2_patcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\psycopg2_patcher.py',
   'PYMODULE'),
  ('eventlet.support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\__init__.py',
   'PYMODULE'),
  ('eventlet.support.greendns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\greendns.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('eventlet.green.SocketServer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\SocketServer.py',
   'PYMODULE'),
  ('eventlet.green.zmq',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\zmq.py',
   'PYMODULE'),
  ('eventlet.green.builtin',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\builtin.py',
   'PYMODULE'),
  ('eventlet.green.MySQLdb',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\MySQLdb.py',
   'PYMODULE'),
  ('eventlet.tpool',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\tpool.py',
   'PYMODULE'),
  ('eventlet.greenio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\__init__.py',
   'PYMODULE'),
  ('eventlet.greenio.py3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\py3.py',
   'PYMODULE'),
  ('_pyio', 'D:\\Program Files (x86)\\python32\\Lib\\_pyio.py', 'PYMODULE'),
  ('eventlet.greenio.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\base.py',
   'PYMODULE'),
  ('eventlet.green.Queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\Queue.py',
   'PYMODULE'),
  ('eventlet.queue',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\queue.py',
   'PYMODULE'),
  ('eventlet.green.subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\subprocess.py',
   'PYMODULE'),
  ('eventlet.green.threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\threading.py',
   'PYMODULE'),
  ('eventlet.green.socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\socket.py',
   'PYMODULE'),
  ('eventlet.green.ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\ssl.py',
   'PYMODULE'),
  ('eventlet.green.time',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\time.py',
   'PYMODULE'),
  ('eventlet.green._socket_nodns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\_socket_nodns.py',
   'PYMODULE'),
  ('eventlet.green.selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\selectors.py',
   'PYMODULE'),
  ('eventlet.green.select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\select.py',
   'PYMODULE'),
  ('eventlet.green.os',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\os.py',
   'PYMODULE'),
  ('eventlet.wsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\wsgi.py',
   'PYMODULE'),
  ('eventlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\__init__.py',
   'PYMODULE'),
  ('eventlet._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\_version.py',
   'PYMODULE'),
  ('eventlet.convenience',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\convenience.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.SSL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\version.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.tsafe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\tsafe.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.crypto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('eventlet.greenpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenpool.py',
   'PYMODULE'),
  ('simple_websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\__init__.py',
   'PYMODULE'),
  ('simple_websocket.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\errors.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('wsproto.events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('simple_websocket.aiows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\aiows.py',
   'PYMODULE'),
  ('simple_websocket.asgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\asgi.py',
   'PYMODULE'),
  ('simple_websocket.ws',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\ws.py',
   'PYMODULE'),
  ('flask_socketio.test_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\test_client.py',
   'PYMODULE'),
  ('socketio.pubsub_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\pubsub_manager.py',
   'PYMODULE'),
  ('socketio.manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\manager.py',
   'PYMODULE'),
  ('socketio.base_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_manager.py',
   'PYMODULE'),
  ('bidict',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\__init__.py',
   'PYMODULE'),
  ('bidict.metadata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\metadata.py',
   'PYMODULE'),
  ('bidict._orderedbidict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\bidict\\_orderedbidict.py',
   'PYMODULE'),
  ('bidict._typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_typing.py',
   'PYMODULE'),
  ('bidict._orderedbase',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\bidict\\_orderedbase.py',
   'PYMODULE'),
  ('bidict._iter',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_iter.py',
   'PYMODULE'),
  ('bidict._frozen',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_frozen.py',
   'PYMODULE'),
  ('bidict._exc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_exc.py',
   'PYMODULE'),
  ('bidict._dup',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_dup.py',
   'PYMODULE'),
  ('bidict._bidict',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_bidict.py',
   'PYMODULE'),
  ('bidict._base',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_base.py',
   'PYMODULE'),
  ('bidict._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_abc.py',
   'PYMODULE'),
  ('engineio.packet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\packet.py',
   'PYMODULE'),
  ('engineio.json',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\json.py',
   'PYMODULE'),
  ('engineio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.tornado',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\tornado.py',
   'PYMODULE'),
  ('engineio.async_drivers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.asgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\asgi.py',
   'PYMODULE'),
  ('engineio.static_files',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\static_files.py',
   'PYMODULE'),
  ('engineio.async_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_client.py',
   'PYMODULE'),
  ('aiohttp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('yarl',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._url',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('yarl._quoters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._path',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._parse',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('propcache.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('propcache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('idna',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('yarl._query',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('multidict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multidict._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('attr',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('frozenlist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('aiosignal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('aiohttp.web',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('engineio.async_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_server.py',
   'PYMODULE'),
  ('engineio.async_socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_socket.py',
   'PYMODULE'),
  ('engineio.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\server.py',
   'PYMODULE'),
  ('engineio.socket',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\socket.py',
   'PYMODULE'),
  ('engineio.base_socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_socket.py',
   'PYMODULE'),
  ('engineio.base_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_server.py',
   'PYMODULE'),
  ('engineio.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\middleware.py',
   'PYMODULE'),
  ('engineio.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\client.py',
   'PYMODULE'),
  ('websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('requests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('engineio.payload',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\payload.py',
   'PYMODULE'),
  ('engineio.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\exceptions.py',
   'PYMODULE'),
  ('engineio.base_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_client.py',
   'PYMODULE'),
  ('socketio.packet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\packet.py',
   'PYMODULE'),
  ('flask_socketio.namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\exceptions.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('click',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.types',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('blinker',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('flask.sansio.app',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('itsdangerous',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('socketio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\__init__.py',
   'PYMODULE'),
  ('socketio.asgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\asgi.py',
   'PYMODULE'),
  ('socketio.async_aiopika_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_aiopika_manager.py',
   'PYMODULE'),
  ('socketio.async_pubsub_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_pubsub_manager.py',
   'PYMODULE'),
  ('socketio.async_redis_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_redis_manager.py',
   'PYMODULE'),
  ('socketio.async_namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_namespace.py',
   'PYMODULE'),
  ('socketio.async_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_server.py',
   'PYMODULE'),
  ('socketio.async_admin',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_admin.py',
   'PYMODULE'),
  ('socketio.admin',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\admin.py',
   'PYMODULE'),
  ('socketio.async_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_manager.py',
   'PYMODULE'),
  ('socketio.async_simple_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_simple_client.py',
   'PYMODULE'),
  ('socketio.async_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_client.py',
   'PYMODULE'),
  ('socketio.tornado',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\tornado.py',
   'PYMODULE'),
  ('socketio.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\middleware.py',
   'PYMODULE'),
  ('socketio.namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\server.py',
   'PYMODULE'),
  ('socketio.base_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_server.py',
   'PYMODULE'),
  ('socketio.zmq_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\zmq_manager.py',
   'PYMODULE'),
  ('socketio.kafka_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\kafka_manager.py',
   'PYMODULE'),
  ('socketio.redis_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\redis_manager.py',
   'PYMODULE'),
  ('socketio.kombu_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\kombu_manager.py',
   'PYMODULE'),
  ('socketio.simple_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\simple_client.py',
   'PYMODULE'),
  ('socketio.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\client.py',
   'PYMODULE'),
  ('socketio.base_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_client.py',
   'PYMODULE'),
  ('socketio.msgpack_packet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\msgpack_packet.py',
   'PYMODULE'),
  ('socketio.base_namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_namespace.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files (x86)\\python32\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('flask_cors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('websockets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.uri',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.streams',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.cli',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.typing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.http11',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.frames',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.imports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('platform',
   'D:\\Program Files (x86)\\python32\\Lib\\platform.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Program Files (x86)\\python32\\Lib\\_strptime.py',
   'PYMODULE'),
  ('threading',
   'D:\\Program Files (x86)\\python32\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files (x86)\\python32\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Program Files (x86)\\python32\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('random', 'D:\\Program Files (x86)\\python32\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files (x86)\\python32\\Lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Program Files (x86)\\python32\\Lib\\fractions.py',
   'PYMODULE'),
  ('json',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\scanner.py',
   'PYMODULE')],
 [('python312.dll',
   'D:\\Program Files (x86)\\python32\\python312.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('gevent\\resolver\\cares.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\cares.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\libuv\\_corecffi.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi.pyd',
   'EXTENSION'),
  ('gevent\\libev\\corecext.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecext.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cqueue.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cqueue.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_clocal.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_clocal.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cevent.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cevent.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_imap.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_imap.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_ident.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_ident.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_c.cp312-win32.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_c.cp312-win32.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_writer.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_parser.cp312-win32.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\_frozenlist.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files (x86)\\python32\\VCRUNTIME140.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files (x86)\\python32\\python3.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\Program Files (x86)\\python32\\DLLs\\tk86t.dll', 'BINARY'),
  ('tcl86t.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('zlib1.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\zlib1.dll',
   'BINARY')],
 [],
 [],
 [('config.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\config.py',
   'DATA'),
  ('gift_id_map.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\gift_id_map.json',
   'DATA'),
  ('keyword_response_pairs.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\keyword_response_pairs.json',
   'DATA'),
  ('static\\admin\\api\\updates\\current',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\admin\\api\\updates\\current',
   'DATA'),
  ('static\\api\\check-update',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\api\\check-update',
   'DATA'),
  ('static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\css\\admin.css',
   'DATA'),
  ('static\\downloads\\fast_updates\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\downloads\\fast_updates\\version.json',
   'DATA'),
  ('static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\static\\js\\admin.js',
   'DATA'),
  ('templates\\admin\\api_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\api_management.html',
   'DATA'),
  ('templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\card_management.html',
   'DATA'),
  ('templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\dashboard.html',
   'DATA'),
  ('templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\index.html',
   'DATA'),
  ('templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745245087',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html.bak.1745245087',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745246592',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\live_status.html.bak.1745246592',
   'DATA'),
  ('templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745243487',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html.bak.1745243487',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745244551',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\log_management_new.html.bak.1745244551',
   'DATA'),
  ('templates\\admin\\login.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\login.html',
   'DATA'),
  ('templates\\admin\\logs.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\logs.html',
   'DATA'),
  ('templates\\admin\\recharge.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\recharge.html',
   'DATA'),
  ('templates\\admin\\settings.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\stats.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\stats.html',
   'DATA'),
  ('templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\update_management.html',
   'DATA'),
  ('templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\admin\\user_management.html',
   'DATA'),
  ('templates\\update.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\templates\\update.html',
   'DATA'),
  ('user_manager.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\user_manager.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\LICENSE',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('zope.event-5.0.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\namespace_packages.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('zope.event-5.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\METADATA',
   'DATA'),
  ('cffi-1.17.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\RECORD',
   'DATA'),
  ('pycparser-2.22.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.interface-7.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\WHEEL',
   'DATA'),
  ('cffi-1.17.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\LICENSE',
   'DATA'),
  ('zope.event-5.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\REQUESTED',
   'DATA'),
  ('zope.event-5.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\top_level.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\WHEEL',
   'DATA'),
  ('pycparser-2.22.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\RECORD',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\RECORD',
   'DATA'),
  ('pycparser-2.22.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\METADATA',
   'DATA'),
  ('gevent-25.5.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\RECORD',
   'DATA'),
  ('zope.interface-7.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\top_level.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\RECORD',
   'DATA'),
  ('zope.interface-7.2.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\namespace_packages.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\entry_points.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\METADATA',
   'DATA'),
  ('gevent-25.5.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.interface-7.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\INSTALLER',
   'DATA'),
  ('zope.event-5.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\METADATA',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'DATA'),
  ('cffi-1.17.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\entry_points.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\RECORD',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\Program Files (x86)\\python32\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Program Files '
   '(x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\Program Files (x86)\\python32\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\RECORD',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\AI主播系统服务器_完整独立版\\server\\build\\server_standalone\\base_library.zip',
   'DATA')],
 [('ntpath', 'D:\\Program Files (x86)\\python32\\Lib\\ntpath.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\Program Files (x86)\\python32\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('warnings',
   'D:\\Program Files (x86)\\python32\\Lib\\warnings.py',
   'PYMODULE'),
  ('weakref', 'D:\\Program Files (x86)\\python32\\Lib\\weakref.py', 'PYMODULE'),
  ('abc', 'D:\\Program Files (x86)\\python32\\Lib\\abc.py', 'PYMODULE'),
  ('sre_parse',
   'D:\\Program Files (x86)\\python32\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('keyword', 'D:\\Program Files (x86)\\python32\\Lib\\keyword.py', 'PYMODULE'),
  ('operator',
   'D:\\Program Files (x86)\\python32\\Lib\\operator.py',
   'PYMODULE'),
  ('locale', 'D:\\Program Files (x86)\\python32\\Lib\\locale.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Program Files (x86)\\python32\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Program Files (x86)\\python32\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('posixpath',
   'D:\\Program Files (x86)\\python32\\Lib\\posixpath.py',
   'PYMODULE'),
  ('codecs', 'D:\\Program Files (x86)\\python32\\Lib\\codecs.py', 'PYMODULE'),
  ('genericpath',
   'D:\\Program Files (x86)\\python32\\Lib\\genericpath.py',
   'PYMODULE'),
  ('enum', 'D:\\Program Files (x86)\\python32\\Lib\\enum.py', 'PYMODULE'),
  ('types', 'D:\\Program Files (x86)\\python32\\Lib\\types.py', 'PYMODULE'),
  ('linecache',
   'D:\\Program Files (x86)\\python32\\Lib\\linecache.py',
   'PYMODULE'),
  ('stat', 'D:\\Program Files (x86)\\python32\\Lib\\stat.py', 'PYMODULE'),
  ('heapq', 'D:\\Program Files (x86)\\python32\\Lib\\heapq.py', 'PYMODULE'),
  ('reprlib', 'D:\\Program Files (x86)\\python32\\Lib\\reprlib.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Program Files (x86)\\python32\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('copyreg', 'D:\\Program Files (x86)\\python32\\Lib\\copyreg.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\Program Files (x86)\\python32\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('_weakrefset',
   'D:\\Program Files (x86)\\python32\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('re._parser',
   'D:\\Program Files (x86)\\python32\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\Program Files (x86)\\python32\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\Program Files (x86)\\python32\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\Program Files (x86)\\python32\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('traceback',
   'D:\\Program Files (x86)\\python32\\Lib\\traceback.py',
   'PYMODULE'),
  ('io', 'D:\\Program Files (x86)\\python32\\Lib\\io.py', 'PYMODULE'),
  ('functools',
   'D:\\Program Files (x86)\\python32\\Lib\\functools.py',
   'PYMODULE'),
  ('re', 'D:\\Program Files (x86)\\python32\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'D:\\Program Files (x86)\\python32\\Lib\\os.py', 'PYMODULE')])
