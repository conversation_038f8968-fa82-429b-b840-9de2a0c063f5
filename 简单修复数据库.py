#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复数据库路径问题
"""

import os
import re

def fix_server_database_connections():
    """修复server.py中的数据库连接"""
    print("🔧 修复server.py中的数据库连接...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('server.py.backup', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已备份原文件")
        
        # 统计需要修复的地方
        direct_connections = content.count('sqlite3.connect("server_data.db"')
        print(f"找到 {direct_connections} 处直接连接")
        
        # 替换所有直接连接
        content = content.replace(
            'sqlite3.connect("server_data.db"',
            'get_sqlite_connection()'
        )
        
        # 确保导入了get_sqlite_connection
        if 'from user_manager import get_sqlite_connection' not in content:
            # 在文件开头添加导入
            import_line = 'from user_manager import get_sqlite_connection\n'
            content = import_line + content
        
        # 移除不必要的import sqlite3（如果紧跟着get_sqlite_connection调用）
        content = re.sub(
            r'import sqlite3\s*\n\s*conn = get_sqlite_connection\(\)',
            'conn = get_sqlite_connection()',
            content
        )
        
        # 写入修改后的内容
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 统计修复后的情况
        remaining_connections = content.count('sqlite3.connect("server_data.db"')
        print(f"修复完成，剩余直接连接: {remaining_connections} 处")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def update_user_manager():
    """更新user_manager.py以支持打包环境"""
    print("\n🔧 更新user_manager.py...")
    
    try:
        with open('user_manager.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('user_manager.py.backup', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已备份原文件")
        
        # 检查是否需要添加sys导入
        if 'import sys' not in content:
            content = content.replace('import os', 'import os\nimport sys')
            print("✅ 添加了sys导入")
        
        # 更新LOCAL_DB_PATH定义
        old_path_def = 'LOCAL_DB_PATH = os.path.join(os.path.dirname(__file__), "server_data.db")'
        
        new_path_def = '''def get_local_db_path():
    """获取本地数据库路径，兼容打包环境"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        db_path = os.path.join(base_path, "server_data.db")
        
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        return db_path
    except Exception:
        # 如果出错，使用当前目录
        return os.path.join(os.getcwd(), "server_data.db")

LOCAL_DB_PATH = get_local_db_path()'''
        
        if old_path_def in content:
            content = content.replace(old_path_def, new_path_def)
            print("✅ 更新了LOCAL_DB_PATH定义")
        else:
            print("ℹ️  LOCAL_DB_PATH定义未找到或已更新")
        
        # 写入修改后的内容
        with open('user_manager.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")
        return False

def create_database_init():
    """创建数据库初始化脚本"""
    print("\n🔧 创建数据库初始化脚本...")
    
    init_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本 - 确保所有表都存在
"""

import os
import sys
import sqlite3

def get_db_path():
    """获取数据库路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.dirname(__file__)
    
    return os.path.join(base_path, "server_data.db")

def init_database():
    """初始化数据库"""
    db_path = get_db_path()
    print(f"初始化数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    init_database()
'''
    
    with open('init_db.py', 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    print("✅ 创建了数据库初始化脚本: init_db.py")
    return True

def main():
    """主函数"""
    print("🚀 简单修复数据库路径问题")
    print("=" * 40)
    
    try:
        # 1. 修复server.py
        if not fix_server_database_connections():
            return False
        
        # 2. 更新user_manager.py
        if not update_user_manager():
            return False
        
        # 3. 创建初始化脚本
        if not create_database_init():
            return False
        
        print("\n" + "=" * 40)
        print("🎉 修复完成！")
        print("\n📝 修复内容:")
        print("✅ 统一了数据库连接方式")
        print("✅ 更新了数据库路径逻辑")
        print("✅ 创建了初始化脚本")
        
        print("\n💡 下一步:")
        print("1. 重新打包程序")
        print("2. 运行打包后的程序")
        print("3. 如果还有问题，运行 init_db.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
