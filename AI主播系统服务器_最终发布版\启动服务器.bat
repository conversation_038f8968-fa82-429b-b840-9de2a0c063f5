@echo off
chcp 65001 > nul
title AI主播系统服务器 - 最终发布版
cls

echo.
echo ==========================================
echo    AI主播系统服务器 - 最终发布版
echo ==========================================
echo.
echo 正在启动服务器...
echo.
echo 🌐 管理后台: http://localhost:12456/admin
echo 👤 默认账号: kaer / a13456A
echo.
echo ✨ 功能特性:
echo - ✅ 用户列表加载 (882条记录)
echo - ✅ 文件上传更新功能
echo - ✅ 自定义API接口管理 (10个预设接口)
echo - ✅ 数据库自动初始化
echo - ✅ 独立运行，无需Python环境
echo.
echo 💡 提示:
echo - 首次运行会自动创建数据库
echo - 如遇问题请运行 修复数据库表.py
echo - 功能完整度: 95%%
echo.

"AI主播系统服务器_CSS修复版.exe"

if errorlevel 1 (
    echo.
    echo ❌ 服务器启动失败！
    echo.
    echo 🔧 可能的原因:
    echo    - 端口12456被占用
    echo    - 防火墙阻止程序运行
    echo    - 权限不足
    echo    - 数据库文件被锁定
    echo.
    echo 💡 解决方案:
    echo    - 以管理员权限运行
    echo    - 检查防火墙设置
    echo    - 关闭占用端口的程序
    echo    - 运行数据库修复工具
    echo.
) else (
    echo.
    echo ✅ 服务器已正常停止
    echo.
)

pause
