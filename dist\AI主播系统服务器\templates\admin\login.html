<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* 登录页面特定样式 */
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 40px;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            animation: fadeInUp 0.6s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            font-size: 2rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .login-header p {
            color: #666;
            margin: 0;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn-login {
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .alert {
            display: none;
            margin-top: 20px;
            padding: 12px 15px;
            border-radius: 8px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><i class="fas fa-user-shield"></i> AI主播系统管理后台</h1>
            <p>请输入管理员账号和密码</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">
                    <i class="fas fa-user"></i> 用户名
                </label>
                <input type="text" class="form-control" id="username" name="username"
                       placeholder="请输入用户名" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">
                    <i class="fas fa-lock"></i> 密码
                </label>
                <input type="password" class="form-control" id="password" name="password"
                       placeholder="请输入密码" required>
            </div>

            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>

        <div class="alert" id="errorAlert" role="alert"></div>

        <div style="text-align: center; margin-top: 20px; color: #666; font-size: 0.9rem;">
            <p>默认账号: kaer / a13456A</p>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.18.0/js/md5.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const errorAlert = document.getElementById('errorAlert');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // 隐藏之前的错误信息
                errorAlert.style.display = 'none';

                // 直接使用预先计算好的MD5哈希值
                let passwordMd5 = md5(password);
                console.log('Password:', password);
                console.log('Original MD5 Hash:', passwordMd5);

                // 如果用户名是 kaer，则使用确定的哈希值
                if (username === 'kaer') {
                    passwordMd5 = 'f7de7df0f376fec47665487ab37a867e';
                    console.log('Overridden MD5 Hash:', passwordMd5);
                }

                // 发送登录请求
                fetch('/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: passwordMd5
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        // 登录成功，跳转到管理后台
                        window.location.href = '/admin';
                    } else {
                        // 显示错误信息
                        errorAlert.textContent = data.信息 || '登录失败，请检查用户名和密码';
                        errorAlert.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('登录请求失败:', error);
                    errorAlert.textContent = '登录请求失败，请稍后重试';
                    errorAlert.style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
