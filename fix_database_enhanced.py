#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的数据库修复脚本
修复所有已知的数据库问题
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        # 尝试多个可能的数据库位置
        possible_paths = [
            os.path.join(base_path, "server_data.db"),
            os.path.join(os.getcwd(), "server_data.db"),
            os.path.join(base_path, "_internal", "server_data.db"),
            # 在临时目录中查找
            os.path.join(os.environ.get('TEMP', ''), "_MEI*", "server_data.db"),
        ]
        
        # 检查现有数据库
        import glob
        for pattern in possible_paths:
            if '*' in pattern:
                matches = glob.glob(pattern)
                if matches:
                    return matches[0]
            elif os.path.exists(pattern):
                return pattern
        
        # 如果都不存在，使用当前目录
        return os.path.join(os.getcwd(), "server_data.db")
        
    except Exception:
        return os.path.join(os.getcwd(), "server_data.db")

def fix_database_structure():
    """修复数据库结构"""
    db_path = get_database_path()
    logger.info(f"修复数据库: {db_path}")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建client_updates表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 检查现有表结构
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"现有列: {existing_columns}")
        
        # 添加缺失的列
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0", 
            "is_folder_update": "INTEGER DEFAULT 0",
            "file_path": "TEXT",
            "file_size": "INTEGER",
            "file_hash": "TEXT",
            "download_count": "INTEGER DEFAULT 0",
            "status": "TEXT DEFAULT 'pending'"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"✅ 添加列: {column}")
                    print(f"✅ 添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
                    print(f"⚠️  添加列 {column} 失败: {str(e)}")
        
        # 创建其他必要的表
        tables = {
            "api_configs": """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "logs": """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "live_status": """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """,
            "tokens": """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """
        }
        
        for table_name, create_sql in tables.items():
            cursor.execute(create_sql)
            logger.info(f"✅ 确保表存在: {table_name}")
            print(f"✅ 确保表存在: {table_name}")
        
        conn.commit()
        conn.close()
        
        logger.info("数据库结构修复完成")
        print("✅ 数据库结构修复完成！")
        print(f"📁 数据库位置: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        print(f"❌ 数据库修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始修复数据库结构...")
    print("=" * 50)
    
    success = fix_database_structure()
    
    print("=" * 50)
    if success:
        print("🎉 数据库修复完成！")
        print("💡 现在可以正常运行服务器了")
        print("💡 所有已知的数据库问题都已修复")
    else:
        print("❌ 数据库修复失败！")
        print("💡 请检查错误信息并重试")
    
    input("\n按回车键退出...")
