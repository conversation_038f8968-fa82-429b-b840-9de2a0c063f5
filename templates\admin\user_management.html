<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management" class="active"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>用户列表</h2>
                <div style="display: flex; gap: 10px;">
                    <form id="username-search-form" style="display: flex; gap: 10px;">
                        <input type="text" id="username-search" class="form-control" placeholder="按用户名搜索">
                        <button type="submit" class="btn btn-primary">搜索</button>
                    </form>
                    <button id="clear-search-btn" class="btn btn-danger">清除搜索</button>
                </div>
            </div>
            <div class="card-body">
                <table class="table" id="users-table">
                    <thead>
                        <tr>
                            <th>用户名(kfm)</th>
                            <th>手机号(tel)</th>
                            <th>机器码(jqm)</th>
                            <th>注册时间(regtime)</th>
                            <th>最后登录(logintime)</th>
                            <th>到期时间(dqtime)</th>
                            <th>备注(bz)</th>
                            <th>状态(status)</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="9" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- 分页将由JavaScript动态生成 -->
                </div>

                <h3>数据库字段说明</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>kfm</td><td>用户名</td></tr>
                        <tr><td>pwd</td><td>密码</td></tr>
                        <tr><td>tel</td><td>手机号</td></tr>
                        <tr><td>jqm</td><td>机器码</td></tr>
                        <tr><td>regtime</td><td>注册时间</td></tr>
                        <tr><td>logintime</td><td>登录时间</td></tr>
                        <tr><td>dqtime</td><td>到期时间</td></tr>
                        <tr><td>bz</td><td>备注</td></tr>
                        <tr><td>status</td><td>状态</td></tr>
                        <tr><td>cz</td><td>充值金额</td></tr>
                        <tr><td>ip</td><td>IP地址</td></tr>
                        <tr><td>sqcs</td><td>授权次数</td></tr>
                        <tr><td>tjcs</td><td>推荐次数</td></tr>
                        <tr><td>drtjcs</td><td>当日推荐次数</td></tr>
                        <tr><td>sqfx</td><td>授权分享</td></tr>
                        <tr><td>fxcs</td><td>分享次数</td></tr>
                        <tr><td>drfxcs</td><td>当日分享次数</td></tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="edit-user-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑用户</h3>
                <span class="modal-close">&times;</span>
            </div>
            <form id="edit-user-form">
                <input type="hidden" id="edit-user-id">
                <div class="form-group">
                    <label for="edit-username">用户名</label>
                    <input type="text" id="edit-username" class="form-control" readonly>
                </div>
                <div class="form-group">
                    <label for="edit-phone">手机号</label>
                    <input type="text" id="edit-phone" class="form-control">
                </div>
                <div class="form-group">
                    <label for="edit-machine-code">机器码</label>
                    <input type="text" id="edit-machine-code" class="form-control">
                </div>
                <div class="form-group">
                    <label for="edit-expire-time">到期时间 (格式: YYYY-MM-DD HH:MM:SS)</label>
                    <input type="text" id="edit-expire-time" class="form-control" placeholder="例如: 2025-04-20 16:30:00">
                    <small class="form-text text-muted">请使用24小时制时间格式</small>
                </div>
                <div class="form-group">
                    <label for="add-days">增加天数</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="number" id="add-days" class="form-control" min="1" value="30">
                        <button type="button" id="add-days-btn" class="btn btn-success">增加天数</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit-status">状态</label>
                    <select id="edit-status" class="form-control">
                        <option value="1">正常</option>
                        <option value="0">封禁</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit-remark">备注</label>
                    <textarea id="edit-remark" class="form-control" rows="3"></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" onclick="closeModal('edit-user-modal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页码和搜索参数
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = parseInt(urlParams.get('page')) || 1;
            const usernameSearch = urlParams.get('username') || '';

            // 设置搜索框的值
            if (usernameSearch) {
                document.getElementById('username-search').value = usernameSearch;
            }

            // 获取用户列表
            fetchUsers(currentPage, usernameSearch);

            // 初始化用户管理功能
            initUserManagement();

            // 用户名搜索表单提交
            const usernameSearchForm = document.getElementById('username-search-form');
            if (usernameSearchForm) {
                usernameSearchForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const username = document.getElementById('username-search').value.trim();
                    const currentUrl = new URL(window.location.href);

                    if (username) {
                        currentUrl.searchParams.set('username', username);
                    } else {
                        currentUrl.searchParams.delete('username');
                    }

                    currentUrl.searchParams.set('page', '1');
                    window.location.href = currentUrl.toString();
                });
            }

            // 清除搜索
            const clearSearchBtn = document.getElementById('clear-search-btn');
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', function() {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.delete('username');
                    currentUrl.searchParams.set('page', '1');
                    window.location.href = currentUrl.toString();
                });
            }

            // 增加天数按钮
            const addDaysBtn = document.getElementById('add-days-btn');
            if (addDaysBtn) {
                addDaysBtn.addEventListener('click', function() {
                    const days = parseInt(document.getElementById('add-days').value);
                    const currentExpireTime = document.getElementById('edit-expire-time').value;

                    if (days <= 0) {
                        alert('增加的天数必须大于0');
                        return;
                    }

                    let newExpireTime;
                    if (currentExpireTime) {
                        // 如果已有到期时间，则基于当前到期时间增加
                        try {
                            // 解析当前到期时间
                            const expireDate = new Date(currentExpireTime.replace(/-/g, '/'));
                            if (isNaN(expireDate.getTime())) {
                                throw new Error('无效的日期格式');
                            }

                            // 增加天数
                            expireDate.setDate(expireDate.getDate() + days);

                            // 格式化为24小时制
                            const year = expireDate.getFullYear();
                            const month = String(expireDate.getMonth() + 1).padStart(2, '0');
                            const day = String(expireDate.getDate()).padStart(2, '0');
                            const hours = String(expireDate.getHours()).padStart(2, '0');
                            const minutes = String(expireDate.getMinutes()).padStart(2, '0');
                            const seconds = String(expireDate.getSeconds()).padStart(2, '0');

                            newExpireTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                        } catch (error) {
                            console.error('处理到期时间出错:', error);
                            // 如果日期格式不正确，则基于当前时间增加
                            const now = new Date();
                            now.setDate(now.getDate() + days);

                            // 格式化为24小时制
                            const year = now.getFullYear();
                            const month = String(now.getMonth() + 1).padStart(2, '0');
                            const day = String(now.getDate()).padStart(2, '0');
                            const hours = String(now.getHours()).padStart(2, '0');
                            const minutes = String(now.getMinutes()).padStart(2, '0');
                            const seconds = String(now.getSeconds()).padStart(2, '0');

                            newExpireTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                        }
                    } else {
                        // 如果没有到期时间，则基于当前时间增加
                        const now = new Date();
                        now.setDate(now.getDate() + days);

                        // 格式化为24小时制
                        const year = now.getFullYear();
                        const month = String(now.getMonth() + 1).padStart(2, '0');
                        const day = String(now.getDate()).padStart(2, '0');
                        const hours = String(now.getHours()).padStart(2, '0');
                        const minutes = String(now.getMinutes()).padStart(2, '0');
                        const seconds = String(now.getSeconds()).padStart(2, '0');

                        newExpireTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    }

                    document.getElementById('edit-expire-time').value = newExpireTime;
                });
            }
        });

        function fetchUsers(page, username = '') {
            const pageSize = 10;

            let url = `/admin/users?page=${page}&page_size=${pageSize}`;
            if (username) {
                url += `&username=${encodeURIComponent(username)}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const users = data.数据.用户列表;
                        const tbody = document.querySelector('#users-table tbody');

                        if (users.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        users.forEach(user => {
                            const statusText = user.status === 1 ? '正常' : '封禁';
                            const statusClass = user.status === 1 ? 'success' : 'danger';

                            tbody.innerHTML += `
                                <tr>
                                    <td>${user.username}</td>
                                    <td>${user.phone || '--'}</td>
                                    <td>${user.machine_code || '--'}</td>
                                    <td>${user.register_time || '--'}</td>
                                    <td>${user.last_login_time || '--'}</td>
                                    <td>${user.expire_time || '--'}</td>
                                    <td>${user.remark || '--'}</td>
                                    <td><span class="btn btn-sm btn-${statusClass}">${statusText}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary edit-user-btn"
                                            data-id="${user.username}"
                                            data-username="${user.username}"
                                            data-phone="${user.phone || ''}"
                                            data-machine-code="${user.machine_code || ''}"
                                            data-expire-time="${user.expire_time || ''}"
                                            data-status="${user.status}"
                                            data-remark="${user.remark || ''}">
                                            编辑
                                        </button>
                                        <button class="btn btn-sm btn-danger delete-user-btn"
                                            data-id="${user.username}"
                                            data-username="${user.username}">
                                            删除
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });

                        // 生成分页
                        generatePagination(data.数据.当前页, data.数据.总页数);

                        // 重新初始化按钮事件
                        initUserManagement();
                    }
                })
                .catch(error => {
                    console.error('获取用户列表出错:', error);
                    const tbody = document.querySelector('#users-table tbody');
                    tbody.innerHTML = '<tr><td colspan="9" style="text-align: center;">加载失败</td></tr>';
                });
        }

        function generatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                return;
            }

            // 上一页
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.textContent = '上一页';
            prevLink.setAttribute('data-page', currentPage - 1);
            if (currentPage === 1) {
                prevLink.classList.add('disabled');
            }
            pagination.appendChild(prevLink);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.setAttribute('data-page', i);
                if (i === currentPage) {
                    pageLink.classList.add('active');
                }
                pagination.appendChild(pageLink);
            }

            // 下一页
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.textContent = '下一页';
            nextLink.setAttribute('data-page', currentPage + 1);
            if (currentPage === totalPages) {
                nextLink.classList.add('disabled');
            }
            pagination.appendChild(nextLink);

            // 添加点击事件
            const pageLinks = pagination.querySelectorAll('a:not(.disabled)');
            pageLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page) {
                        const currentUrl = new URL(window.location.href);
                        currentUrl.searchParams.set('page', page);
                        window.location.href = currentUrl.toString();
                    }
                });
            });
        }

        function initUserManagement() {
            // 初始化编辑按钮事件
            const editButtons = document.querySelectorAll('.edit-user-btn');
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const userId = this.getAttribute('data-id');
                    const username = this.getAttribute('data-username');
                    const phone = this.getAttribute('data-phone');
                    const machineCode = this.getAttribute('data-machine-code');
                    const expireTime = this.getAttribute('data-expire-time');
                    const status = this.getAttribute('data-status');
                    const remark = this.getAttribute('data-remark');

                    // 填充编辑表单
                    document.getElementById('edit-user-id').value = userId;
                    document.getElementById('edit-username').value = username;
                    document.getElementById('edit-phone').value = phone;
                    document.getElementById('edit-machine-code').value = machineCode;
                    document.getElementById('edit-expire-time').value = expireTime;
                    document.getElementById('edit-status').value = status;
                    document.getElementById('edit-remark').value = remark;

                    // 显示模态框
                    showModal('edit-user-modal');
                });
            });

            // 初始化删除按钮事件
            const deleteButtons = document.querySelectorAll('.delete-user-btn');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const userId = this.getAttribute('data-id');
                    const username = this.getAttribute('data-username');

                    if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
                        deleteUser(userId);
                    }
                });
            });
        }

        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        }

        function deleteUser(userId) {
            fetch(`/admin/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('用户删除成功');
                    location.reload(); // 重新加载页面
                } else {
                    alert('删除失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('删除用户出错:', error);
                alert('删除失败，请稍后重试');
            });
        }

        // 初始化模态框事件
        document.addEventListener('DOMContentLoaded', function() {
            // 模态框关闭按钮事件
            const modalCloses = document.querySelectorAll('.modal-close');
            modalCloses.forEach(closeBtn => {
                closeBtn.addEventListener('click', function() {
                    const modal = this.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            });

            // 点击模态框外部关闭
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.style.display = 'none';
                    }
                });
            });

            // 编辑用户表单提交
            const editUserForm = document.getElementById('edit-user-form');
            if (editUserForm) {
                editUserForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const userId = document.getElementById('edit-user-id').value;
                    const formData = {
                        phone: document.getElementById('edit-phone').value,
                        machine_code: document.getElementById('edit-machine-code').value,
                        expire_time: document.getElementById('edit-expire-time').value,
                        status: parseInt(document.getElementById('edit-status').value),
                        remark: document.getElementById('edit-remark').value
                    };

                    fetch(`/admin/users/${userId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(formData)
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.状态 === '成功') {
                            alert('用户信息更新成功');
                            closeModal('edit-user-modal');
                            location.reload(); // 重新加载页面
                        } else {
                            alert('更新失败: ' + data.信息);
                        }
                    })
                    .catch(error => {
                        console.error('更新用户信息出错:', error);
                        alert('更新失败，请稍后重试');
                    });
                });
            }
        });
    </script>
</body>
</html>
