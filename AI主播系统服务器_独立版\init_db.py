#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本 - 确保所有表都存在
"""

import os
import sys
import sqlite3

def get_db_path():
    """获取数据库路径"""
    if hasattr(sys, '_MEIPASS'):
        # 打包环境
        base_path = sys._MEIPASS
    else:
        # 开发环境
        base_path = os.path.dirname(__file__)
    
    return os.path.join(base_path, "server_data.db")

def init_database():
    """初始化数据库"""
    db_path = get_db_path()
    print(f"初始化数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    init_database()
