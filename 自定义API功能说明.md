# 自定义API功能说明

## 功能概述

现在您可以直接在后台管理页面新增自定义API接口，完全自定义接口名称、路径、标题、描述和返回内容等信息。无需修改代码，无需重启服务器，所有配置实时生效。

## 🎯 核心功能

### ✅ 已实现的功能

1. **自定义新增API接口**
   - 可以自定义API名称（用于内部标识）
   - 可以自定义API路径（如：/api/my_custom_api）
   - 可以自定义API标题和描述
   - 可以自定义返回内容（支持复杂JSON结构）
   - 可以设置启用/禁用状态

2. **API配置管理**
   - 查看所有API配置列表
   - 编辑已有API配置
   - 删除API配置
   - 实时测试API接口

3. **动态生效**
   - 新增API后立即可用，无需重启服务器
   - 更新API配置立即生效
   - 删除API后立即停止服务

4. **完整的管理界面**
   - 响应式设计，支持各种屏幕尺寸
   - JSON格式验证
   - 一键测试功能
   - 直观的操作界面

## 🚀 使用方法

### 1. 访问管理页面

打开浏览器访问：`http://localhost:12456/admin/api_management`

### 2. 新增自定义API

1. 点击"新增API"按钮
2. 填写API信息：
   - **API名称**：只能包含字母、数字和下划线，用于内部标识
   - **API路径**：API的访问路径，如：/api/my_custom_api
   - **API标题**：API的显示名称
   - **API描述**：API的功能描述（可选）
   - **启用状态**：是否启用此API接口
   - **返回内容**：JSON格式的返回数据

3. 点击"创建API"按钮

### 3. 编辑API配置

1. 在API列表中找到要编辑的API
2. 点击"编辑"按钮
3. 修改相关信息
4. 点击"保存配置"

### 4. 测试API接口

1. 点击API列表中的"测试"按钮
2. 系统会在新窗口中打开API接口
3. 查看返回结果

### 5. 删除API配置

1. 点击API列表中的"删除"按钮
2. 确认删除操作
3. API配置和接口立即停止服务

## 📝 返回内容格式

推荐使用统一的JSON格式：

```json
{
    "status": "success",
    "message": "操作成功的消息",
    "data": {
        // 具体的数据内容
        "title": "标题",
        "content": "内容",
        "custom_field": "自定义字段"
    }
}
```

### 示例配置

**简单示例：**
```json
{
    "status": "success",
    "message": "获取用户信息成功",
    "data": {
        "user_id": 12345,
        "username": "张三",
        "email": "<EMAIL>"
    }
}
```

**复杂示例：**
```json
{
    "status": "success",
    "message": "获取产品列表成功",
    "data": {
        "total": 100,
        "page": 1,
        "page_size": 10,
        "products": [
            {
                "id": 1,
                "name": "产品A",
                "price": 99.99,
                "category": "电子产品",
                "tags": ["热销", "推荐"]
            },
            {
                "id": 2,
                "name": "产品B", 
                "price": 199.99,
                "category": "家居用品",
                "tags": ["新品"]
            }
        ]
    }
}
```

## 🔧 技术实现

### 数据库设计

使用 `api_configs` 表存储API配置：

```sql
CREATE TABLE api_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    api_name TEXT UNIQUE NOT NULL,          -- API名称
    api_path TEXT NOT NULL,                 -- API路径  
    api_title TEXT NOT NULL,                -- API标题
    api_description TEXT,                   -- API描述
    response_content TEXT NOT NULL,         -- 返回内容(JSON)
    is_enabled INTEGER DEFAULT 1,          -- 是否启用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 动态路由处理

使用通用路由处理函数 `handle_dynamic_api()` 来处理所有自定义API请求：

- 捕获所有 `/api/<path>` 格式的请求
- 从数据库查询对应的API配置
- 返回配置的JSON内容
- 如果API不存在或已禁用，返回404错误

### 管理接口

- `GET /admin/api/configs` - 获取所有API配置
- `POST /admin/api/configs` - 新增API配置
- `PUT /admin/api/configs/<id>` - 更新API配置
- `DELETE /admin/api/configs/<id>` - 删除API配置

## 🎨 界面特性

- **响应式设计**：支持桌面和移动设备
- **实时验证**：JSON格式实时验证
- **一键测试**：直接在新窗口测试API
- **状态显示**：清晰显示API启用/禁用状态
- **操作确认**：删除操作需要确认，防止误操作

## 🔒 安全性

- **权限控制**：需要管理员登录才能访问
- **数据验证**：严格验证输入数据格式
- **SQL注入防护**：使用参数化查询
- **XSS防护**：前端输入过滤和转义

## 📊 使用场景

1. **API原型开发**：快速创建API原型进行测试
2. **数据模拟**：为前端开发提供模拟数据
3. **配置管理**：动态配置系统参数和设置
4. **内容管理**：管理应用中的文本内容和配置
5. **A/B测试**：快速切换不同的API返回内容

## 🚨 注意事项

1. **API名称唯一性**：API名称在系统中必须唯一
2. **路径唯一性**：API路径在系统中必须唯一
3. **JSON格式**：返回内容必须是有效的JSON格式
4. **性能考虑**：大量API配置可能影响系统性能
5. **备份重要**：建议定期备份API配置数据

## 🎉 总结

通过这个自定义API功能，您可以：

- ✅ 无需编程知识即可创建API接口
- ✅ 实时配置和测试API
- ✅ 灵活管理API的生命周期
- ✅ 支持复杂的数据结构
- ✅ 提供完整的管理界面

这大大提高了系统的灵活性和可扩展性，让您能够快速响应业务需求变化。
