#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包问题终极解决方案
专门解决server.py打包后运行出错的问题
"""

import os
import sys
import shutil
import subprocess

def create_fixed_server():
    """创建修复后的server.py"""
    print("🔧 创建修复后的server.py...")
    
    # 读取原始server.py
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # PyInstaller兼容性修复代码
    fix_code = '''
# ==================== PyInstaller兼容性修复 ====================
import os
import sys
import shutil

def get_executable_dir():
    """获取可执行文件所在目录"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

def get_resource_path(relative_path):
    """获取资源文件路径"""
    if getattr(sys, 'frozen', False):
        # 打包后的程序，资源在临时目录
        base_path = sys._MEIPASS
        return os.path.join(base_path, relative_path)
    else:
        # 开发环境
        return os.path.join(os.path.dirname(__file__), relative_path)

def get_persistent_path(relative_path):
    """获取持久化文件路径（数据库等）"""
    exe_dir = get_executable_dir()
    return os.path.join(exe_dir, relative_path)

def ensure_persistent_database():
    """确保数据库文件在可执行文件目录"""
    exe_dir = get_executable_dir()
    
    # 数据库文件列表
    db_files = ['server_data.db', 'local.db']
    
    for db_file in db_files:
        persistent_path = os.path.join(exe_dir, db_file)
        
        if not os.path.exists(persistent_path):
            print(f"创建持久化数据库: {persistent_path}")
            
            # 尝试从打包的资源中复制
            if getattr(sys, 'frozen', False):
                resource_path = os.path.join(sys._MEIPASS, db_file)
                if os.path.exists(resource_path):
                    shutil.copy2(resource_path, persistent_path)
                    print(f"从资源复制数据库: {db_file}")
                else:
                    # 创建新的数据库
                    create_database_if_not_exists(persistent_path)
            else:
                # 开发环境，直接创建
                create_database_if_not_exists(persistent_path)

def create_database_if_not_exists(db_path):
    """如果数据库不存在则创建"""
    import sqlite3
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute('''
CREATE TABLE IF NOT EXISTS client_updates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    version TEXT NOT NULL,
    release_date TEXT NOT NULL,
    description TEXT,
    download_url TEXT,
    fast_download_url TEXT,
    force_update INTEGER DEFAULT 0,
    is_exe INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)''')
        
        # 插入示例数据
        cursor.execute('''
INSERT OR REPLACE INTO client_updates (id, version, release_date, description, download_url, fast_download_url, force_update, is_exe)
VALUES (1, ?, ?, ?, ?, ?, ?, ?)''', (
            '1.8',
            '2025-06-14',
            '1. 修复了登录系统问题\\n2. 优化了更新功能\\n3. 增加了新特性\\n4. 增强了系统稳定性',
            'http://localhost:12456/static/downloads/AI主播系统.zip',
            'http://localhost:12456/api/fast-download/AI主播系统.zip',
            0,
            0
        ))
        
        # 创建logs表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                module TEXT,
                function TEXT
            )
        ''')
        
        # 创建api_configs表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                endpoint TEXT NOT NULL,
                method TEXT DEFAULT 'GET',
                description TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"数据库创建成功: {db_path}")
        
    except Exception as e:
        print(f"数据库创建失败: {e}")

# 初始化PyInstaller兼容性
if getattr(sys, 'frozen', False):
    print("检测到打包环境，初始化兼容性修复...")
    ensure_persistent_database()
    
    # 设置Flask应用路径
    TEMPLATE_FOLDER = get_resource_path('templates')
    STATIC_FOLDER = get_resource_path('static')
    
    print(f"模板目录: {TEMPLATE_FOLDER}")
    print(f"静态目录: {STATIC_FOLDER}")
else:
    # 开发环境
    TEMPLATE_FOLDER = 'templates'
    STATIC_FOLDER = 'static'

# 重写数据库连接函数
def get_db_connection(db_name='server_data.db'):
    """获取数据库连接"""
    import sqlite3
    
    if getattr(sys, 'frozen', False):
        # 打包环境，使用持久化路径
        db_path = get_persistent_path(db_name)
    else:
        # 开发环境
        db_path = db_name
    
    return sqlite3.connect(db_path)

# ==================== PyInstaller兼容性修复结束 ====================

'''
    
    # 在文件开头插入修复代码
    modified_content = fix_code + '\n' + content
    
    # 修复Flask应用创建
    if "app = Flask(__name__)" in modified_content:
        modified_content = modified_content.replace(
            "app = Flask(__name__)",
            "app = Flask(__name__, template_folder=TEMPLATE_FOLDER, static_folder=STATIC_FOLDER)"
        )
    
    # 修复数据库连接
    db_patterns = [
        ("sqlite3.connect('server_data.db')", "get_db_connection('server_data.db')"),
        ('sqlite3.connect("server_data.db")', 'get_db_connection("server_data.db")'),
        ("sqlite3.connect('local.db')", "get_db_connection('local.db')"),
        ('sqlite3.connect("local.db")', 'get_db_connection("local.db")'),
    ]
    
    for old_pattern, new_pattern in db_patterns:
        modified_content = modified_content.replace(old_pattern, new_pattern)
    
    # 保存修复后的文件
    with open('server_pyinstaller_fixed.py', 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print("✅ 修复后的server.py已保存为: server_pyinstaller_fixed.py")

def create_optimized_spec():
    """创建优化的PyInstaller配置"""
    print("📝 创建优化的PyInstaller配置...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os

block_cipher = None

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

a = Analysis(
    ['server_pyinstaller_fixed.py'],
    pathex=[current_dir],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('gift_id_map.json', '.'),
        ('keyword_response_pairs.json', '.'),
        ('user_manager.py', '.'),
        ('server_data.db', '.'),
        ('local.db', '.'),
    ],
    hiddenimports=[
        'waitress',
        'flask',
        'flask_cors',
        'flask_socketio',
        'pymysql',
        'sqlite3',
        'json',
        'logging',
        'datetime',
        'os',
        'sys',
        'threading',
        'time',
        'requests',
        'urllib.parse',
        'hashlib',
        'base64',
        'uuid',
        'psutil',
        'gevent',
        'eventlet',
        'socketio',
        'engineio',
        'jinja2',
        'werkzeug',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'cryptography',
        'cffi',
        'pycparser',
        'six',
        'greenlet',
        'zope.interface',
        'zope.event',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
        'jupyter',
        'notebook',
        'IPython',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统服务器_终极修复版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('AI主播系统服务器_终极修复版.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 优化配置创建完成")

def prepare_database_files():
    """准备数据库文件"""
    print("🗄️ 准备数据库文件...")
    
    # 确保数据库文件存在
    db_files = ['server_data.db', 'local.db']
    
    for db_file in db_files:
        if not os.path.exists(db_file):
            print(f"创建数据库文件: {db_file}")
            
            import sqlite3
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 创建基本表结构
            if db_file == 'server_data.db':
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS client_updates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        version TEXT NOT NULL,
                        release_date TEXT NOT NULL,
                        description TEXT,
                        download_url TEXT,
                        fast_download_url TEXT,
                        force_update INTEGER DEFAULT 0,
                        is_exe INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                cursor.execute('''
                    INSERT OR REPLACE INTO client_updates (id, version, release_date, description, download_url, fast_download_url, force_update, is_exe)
                    VALUES (1, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    '1.8',
                    '2025-06-14',
                    '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
                    'http://localhost:12456/static/downloads/AI主播系统.zip',
                    'http://localhost:12456/api/fast-download/AI主播系统.zip',
                    0,
                    0
                ))
                
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS api_configs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        endpoint TEXT NOT NULL,
                        method TEXT DEFAULT 'GET',
                        description TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
            
            elif db_file == 'local.db':
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        module TEXT,
                        function TEXT
                    )
                ''')
            
            conn.commit()
            conn.close()
            print(f"✅ {db_file} 创建完成")
        else:
            print(f"✅ {db_file} 已存在")

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    bat_content = '''@echo off
chcp 65001 > nul
title AI主播系统服务器 - 终极修复版
echo.
echo ========================================
echo    AI主播系统服务器 - 终极修复版
echo ========================================
echo.
echo 正在启动服务器...
echo 服务器地址: http://localhost:12456
echo 管理后台: http://localhost:12456/admin
echo 默认账号: kaer / a13456A
echo.
echo 修复内容:
echo - 解决了PyInstaller打包路径问题
echo - 修复了数据库文件丢失问题
echo - 修复了更新界面错误问题
echo - 确保了资源文件正确加载
echo.

"AI主播系统服务器_终极修复版.exe"

echo.
echo 服务器已停止运行
pause
'''
    
    with open('启动终极修复版.bat', 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    print("✅ 启动脚本创建完成")

def run_packaging():
    """执行打包"""
    print("🚀 开始打包终极修复版...")
    
    # 清理旧的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理旧的build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的dist目录")
    
    # 执行打包命令
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'AI主播系统服务器_终极修复版.spec'
    ]
    
    print(f"📦 执行打包命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    print("🚀 PyInstaller打包问题终极解决方案")
    print("=" * 70)
    
    # 创建修复后的server.py
    create_fixed_server()
    
    # 准备数据库文件
    prepare_database_files()
    
    # 创建优化配置
    create_optimized_spec()
    
    # 创建启动脚本
    create_startup_script()
    
    # 执行打包
    if run_packaging():
        # 复制必要文件到dist目录
        files_to_copy = [
            'server_data.db',
            'local.db',
            '启动终极修复版.bat'
        ]
        
        for file_name in files_to_copy:
            if os.path.exists(file_name):
                shutil.copy2(file_name, f'dist/{file_name}')
                print(f"✅ 复制 {file_name} 到 dist 目录")
        
        print("=" * 70)
        print("🎉 PyInstaller打包问题终极修复完成！")
        print("📁 输出目录: dist/")
        print("📦 可执行文件: AI主播系统服务器_终极修复版.exe")
        print("🚀 启动脚本: 启动终极修复版.bat")
        print("=" * 70)
        print("💡 修复内容:")
        print("✅ 解决了PyInstaller路径问题")
        print("✅ 修复了数据库文件丢失问题")
        print("✅ 修复了资源文件访问问题")
        print("✅ 确保了更新界面正常工作")
        print("✅ 数据库文件持久化存储")
        print("=" * 70)
        print("🎯 现在打包后的程序应该和python server.py一样正常工作！")
        return True
    else:
        print("❌ 打包失败")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 终极修复完成！现在打包后的程序应该完全正常工作了！")
    else:
        print("💥 修复失败，请检查错误信息")
    
    input("\n按回车键退出...")
