<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直播状态管理</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        .container {
            max-width: 1200px;
            padding: 0;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .status-card {
            transition: all 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .online-badge {
            font-size: 0.9rem;
            padding: 5px 10px;
        }
        .danmaku-container {
            max-height: 150px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
        }
        .danmaku-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .danmaku-item:last-child {
            border-bottom: none;
        }
        .last-update {
            font-size: 0.8rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status" class="active"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">直播状态管理</h1>

            <div class="d-flex justify-content-between align-items-center mb-4">
                <button id="refreshBtn" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise"></i> 刷新数据
                </button>
            </div>

        <div class="row" id="statusContainer">
            <!-- 直播状态卡片将在这里动态生成 -->
            <div class="col-12 text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2">正在加载数据...</p>
            </div>
        </div>
    </div>

    <!-- 踢下线确认模态框 -->
    <div class="modal fade" id="kickModal" tabindex="-1" aria-labelledby="kickModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="kickModalLabel">确认踢下线</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要将用户 <span id="kickUsername" class="fw-bold"></span> 踢下线吗？
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmKickBtn">确认踢下线</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin.js"></script>
    <script>
        // 退出登录功能
        document.getElementById('logout-btn').addEventListener('click', function() {
            fetch('/admin/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    window.location.href = '/admin';
                } else {
                    alert('退出登录失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('退出登录出错:', error);
                alert('退出登录出错，请重试');
            });
        });

        // 格式化时间函数
        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '未知';
            const date = new Date(dateTimeStr);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 格式化时间间隔
        function formatTimeAgo(dateTimeStr) {
            if (!dateTimeStr) return '';

            const date = new Date(dateTimeStr);
            const now = new Date();
            const diffMs = now - date;

            // 转换为秒
            const diffSec = Math.floor(diffMs / 1000);

            if (diffSec < 60) {
                return `${diffSec}秒前`;
            } else if (diffSec < 3600) {
                return `${Math.floor(diffSec / 60)}分钟前`;
            } else if (diffSec < 86400) {
                return `${Math.floor(diffSec / 3600)}小时前`;
            } else {
                return `${Math.floor(diffSec / 86400)}天前`;
            }
        }

        // 格式化播放时间
        function formatPlayTime(seconds) {
            if (!seconds && seconds !== 0) return '未知';

            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const remainingSeconds = seconds % 60;

            let result = '';
            if (hours > 0) {
                result += `${hours}小时`;
            }
            if (minutes > 0 || hours > 0) {
                result += `${minutes}分钟`;
            }
            result += `${remainingSeconds}秒`;

            return result;
        }

        // 加载直播状态数据
        function loadLiveStatus() {
            fetch('/live/status')
                .then(response => response.json())
                .then(data => {
                    const statusContainer = document.getElementById('statusContainer');
                    statusContainer.innerHTML = '';

                    if (data.状态 === '成功' && data.数据 && data.数据.length > 0) {
                        // 按在线状态对用户进行排序，在线用户在前
                        const sortedData = data.数据.sort((a, b) => {
                            // 先按在线状态排序（在线在前）
                            const aOnline = a.is_online === 1 || a.is_online === true;
                            const bOnline = b.is_online === 1 || b.is_online === true;
                            if (aOnline !== bOnline) {
                                return aOnline ? -1 : 1;
                            }
                            // 如果在线状态相同，则按最后更新时间排序（近期更新的在前）
                            return new Date(b.last_update_time) - new Date(a.last_update_time);
                        });

                        sortedData.forEach(status => {
                            // 解析弹幕数据
                            let danmakuHtml = '<p class="text-muted">无弹幕数据</p>';
                            if (status.danmaku) {
                                try {
                                    const danmakuList = JSON.parse(status.danmaku);
                                    if (danmakuList && danmakuList.length > 0) {
                                        danmakuHtml = '<div class="danmaku-container">';
                                        danmakuList.forEach(item => {
                                            danmakuHtml += `<div class="danmaku-item">${item}</div>`;
                                        });
                                        danmakuHtml += '</div>';
                                    }
                                } catch (e) {
                                    danmakuHtml = `<p class="text-danger">弹幕数据解析错误: ${e.message}</p>`;
                                }
                            }

                            // 解析弹幕历史数据
                            let danmakuHistoryHtml = '<p class="text-muted">无弹幕历史数据</p>';
                            if (status.danmaku_history) {
                                try {
                                    const danmakuHistory = JSON.parse(status.danmaku_history);
                                    if (danmakuHistory && danmakuHistory.length > 0) {
                                        danmakuHistoryHtml = '<div class="danmaku-history-container" style="max-height: 200px; overflow-y: auto;">';
                                        danmakuHistory.slice(-10).reverse().forEach(item => {
                                            danmakuHistoryHtml += `<div class="danmaku-history-item">
                                                <small class="text-muted">${item.time}</small>: ${item.content}
                                            </div>`;
                                        });
                                        danmakuHistoryHtml += '</div>';
                                    }
                                } catch (e) {
                                    danmakuHistoryHtml = `<p class="text-danger">弹幕历史数据解析错误: ${e.message}</p>`;
                                }
                            }

                            // 解析语音历史数据
                            let voiceHistoryHtml = '<p class="text-muted">无语音历史数据</p>';
                            if (status.voice_history) {
                                try {
                                    const voiceHistory = JSON.parse(status.voice_history);
                                    if (voiceHistory && voiceHistory.length > 0) {
                                        voiceHistoryHtml = '<div class="voice-history-container" style="max-height: 200px; overflow-y: auto;">';
                                        voiceHistory.slice(-10).reverse().forEach(item => {
                                            voiceHistoryHtml += `<div class="voice-history-item">
                                                <small class="text-muted">${item.time}</small>: ${item.script}
                                            </div>`;
                                        });
                                        voiceHistoryHtml += '</div>';
                                    }
                                } catch (e) {
                                    voiceHistoryHtml = `<p class="text-danger">语音历史数据解析错误: ${e.message}</p>`;
                                }
                            }

                            // 创建状态卡片
                            const card = document.createElement('div');
                            card.className = 'col-md-6 col-lg-4 mb-4';

                            // 判断在线状态
                            let isOnline = status.is_online === 1 || status.is_online === true;

                            // 检查最后更新时间，如果超过5分钟没有更新，则设置为离线
                            if (isOnline && status.last_update_time) {
                                const lastUpdate = new Date(status.last_update_time);
                                const now = new Date();
                                const diffMinutes = (now - lastUpdate) / (1000 * 60);

                                if (diffMinutes > 5) {
                                    isOnline = false;
                                    console.log(`用户 ${status.username} 超过5分钟没有更新状态，显示为离线`);
                                }
                            }

                            const statusBadge = isOnline ?
                                `<span class="badge bg-success online-badge">${status.online_count ? status.online_count + '人在线' : '在线'}</span>` :
                                `<span class="badge bg-secondary offline-badge">离线</span>`;

                            card.innerHTML = `
                                <div class="card status-card h-100 ${isOnline ? 'border-success' : 'border-secondary'}">
                                    <div class="card-header d-flex justify-content-between align-items-center ${isOnline ? 'bg-success text-white' : 'bg-secondary text-white'}">
                                        <h5 class="mb-0">${status.username}</h5>
                                        ${statusBadge}
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <strong>登录时间:</strong> ${formatDateTime(status.login_time)}
                                        </div>
                                        <div class="mb-3">
                                            <strong>播放时间:</strong> ${formatPlayTime(status.voice_play_time)}
                                        </div>
                                        <div class="mb-3">
                                            <strong>当前话术:</strong> ${status.current_script || '无'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>OBS源:</strong> ${status.obs_source || '未知'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>当前弹幕:</strong>
                                            ${danmakuHtml}
                                        </div>
                                        <div class="mb-3">
                                            <strong>IP地址:</strong> ${status.ip || '未知'}
                                        </div>
                                        <div class="mb-3">
                                            <strong>机器码:</strong> ${status.machine_code || '未知'}
                                        </div>
                                        <p class="last-update mb-0 text-end">
                                            最后更新: ${formatTimeAgo(status.last_update_time)}
                                        </p>
                                    </div>
                                    <div class="card-footer">
                                        <div class="accordion" id="accordionUser${status.id}">
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseDanmaku${status.id}" aria-expanded="false">
                                                        弹幕历史
                                                    </button>
                                                </h2>
                                                <div id="collapseDanmaku${status.id}" class="accordion-collapse collapse" data-bs-parent="#accordionUser${status.id}">
                                                    <div class="accordion-body">
                                                        ${danmakuHistoryHtml}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseVoice${status.id}" aria-expanded="false">
                                                        语音历史
                                                    </button>
                                                </h2>
                                                <div id="collapseVoice${status.id}" class="accordion-collapse collapse" data-bs-parent="#accordionUser${status.id}">
                                                    <div class="accordion-body">
                                                        ${voiceHistoryHtml}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-item">
                                                <h2 class="accordion-header">
                                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseLogs${status.id}" aria-expanded="false">
                                                        操作日志
                                                    </button>
                                                </h2>
                                                <div id="collapseLogs${status.id}" class="accordion-collapse collapse" data-bs-parent="#accordionUser${status.id}">
                                                    <div class="accordion-body">
                                                        <div class="client-logs-container" id="clientLogs${status.id}">
                                                            <div class="mb-3">
                                                                <div class="d-flex justify-content-center mb-2">
                                                                    <div class="btn-group" role="group">
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn active" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="">全部</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="system">系统</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="voice">语音</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="danmaku">弹幕</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="obs">OBS</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-primary log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="network">网络</button>
                                                                        <button type="button" class="btn btn-sm btn-outline-danger log-type-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="error">错误</button>
                                                                    </div>
                                                                </div>
                                                                <div class="text-center">
                                                                    <button class="btn btn-sm btn-primary load-logs-btn" data-username="${status.username}" data-target="clientLogs${status.id}" data-type="">
                                                                        加载最近日志
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <div class="logs-content" id="logsContent${status.id}">
                                                                <p class="text-muted text-center">点击上方按钮加载日志</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <button class="btn btn-danger w-100 kick-btn mt-3" data-username="${status.username}">
                                            踢下线
                                        </button>
                                    </div>
                                </div>
                            `;
                            statusContainer.appendChild(card);
                        });

                        // 添加踢下线按钮事件
                        document.querySelectorAll('.kick-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const username = this.getAttribute('data-username');
                                document.getElementById('kickUsername').textContent = username;

                                // 存储用户名到确认按钮
                                document.getElementById('confirmKickBtn').setAttribute('data-username', username);

                                // 显示模态框
                                const kickModal = new bootstrap.Modal(document.getElementById('kickModal'));
                                kickModal.show();
                            });
                        });
                    } else {
                        statusContainer.innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-info">
                                    当前没有直播状态数据
                                </div>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('获取直播状态失败:', error);
                    document.getElementById('statusContainer').innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-danger">
                                获取直播状态失败: ${error.message}
                            </div>
                        </div>
                    `;
                });
        }

        // 踢下线用户
        function kickUser(username) {
            fetch(`/admin/users/${username}/kick`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert(`用户 ${username} 已成功踢下线`);
                    // 重新加载状态
                    loadLiveStatus();
                } else {
                    alert(`踢下线失败: ${data.信息}`);
                }
            })
            .catch(error => {
                console.error('踢下线请求失败:', error);
                alert(`踢下线请求失败: ${error.message}`);
            });
        }

        // 加载客户端日志
        function loadClientLogs(username, targetId, logType = '') {
            const container = document.getElementById(targetId);
            if (!container) return;

            // 找到日志内容区域
            const contentId = 'logsContent' + targetId.replace('clientLogs', '');
            const contentContainer = document.getElementById(contentId);
            if (!contentContainer) return;

            contentContainer.innerHTML = `
                <div class="text-center py-3">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2">正在加载日志...</span>
                </div>
            `;

            // 构建查询URL
            let url = `/admin/api/client_logs?username=${encodeURIComponent(username)}&limit=50`;
            if (logType) {
                url += `&log_type=${encodeURIComponent(logType)}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功' && data.数据 && data.数据.日志) {
                        const logs = data.数据.日志;

                        if (logs.length === 0) {
                            contentContainer.innerHTML = `<p class="text-muted text-center">没有找到日志记录</p>`;
                            return;
                        }

                        let html = `
                            <div class="table-responsive">
                                <table class="table table-sm table-hover">
                                    <thead>
                                        <tr>
                                            <th>时间</th>
                                            <th>类型</th>
                                            <th>日志内容</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;

                        // 日志类型样式映射
                        const typeClasses = {
                            'system': 'text-secondary',
                            'voice': 'text-primary',
                            'danmaku': 'text-info',
                            'obs': 'text-success',
                            'network': 'text-warning',
                            'error': 'text-danger'
                        };

                        logs.forEach(log => {
                            const logTypeText = log.log_type || 'system';
                            const typeClass = typeClasses[logTypeText] || 'text-secondary';

                            html += `
                                <tr>
                                    <td><small>${log.timestamp || ''}</small></td>
                                    <td><span class="badge ${typeClass}">${logTypeText}</span></td>
                                    <td>${log.message || ''}</td>
                                </tr>
                            `;
                        });

                        html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center mt-2">
                                <small class="text-muted">共 ${data.数据.总数} 条日志记录，显示最新 ${logs.length} 条</small>
                            </div>
                        `;

                        contentContainer.innerHTML = html;
                    } else {
                        contentContainer.innerHTML = `<p class="text-danger text-center">加载日志失败: ${data.信息 || '未知错误'}</p>`;
                        console.error('加载日志失败:', data);

                        // 如果是表不存在的错误，显示更友好的提示
                        if (data.信息 && data.信息.includes('no such table')) {
                            contentContainer.innerHTML = `
                                <div class="alert alert-warning" role="alert">
                                    <h5 class="alert-heading">日志表尚未创建</h5>
                                    <p>系统尚未收到任何客户端日志。请确保客户端已启动并正常运行。</p>
                                    <hr>
                                    <p class="mb-0">当客户端发送第一条日志时，系统将自动创建日志表。</p>
                                </div>
                            `;
                        }
                    }
                })
                .catch(error => {
                    console.error('加载客户端日志出错:', error);
                    contentContainer.innerHTML = `<p class="text-danger text-center">加载日志出错: ${error.message}</p>`;
                });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 加载初始数据
            loadLiveStatus();

            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', loadLiveStatus);

            // 确认踢下线按钮点击事件
            document.getElementById('confirmKickBtn').addEventListener('click', function() {
                const username = this.getAttribute('data-username');
                if (username) {
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('kickModal')).hide();
                    // 执行踢下线
                    kickUser(username);
                }
            });

            // 为日志相关按钮添加事件委托
            document.addEventListener('click', function(event) {
                // 加载日志按钮
                if (event.target.classList.contains('load-logs-btn')) {
                    const username = event.target.getAttribute('data-username');
                    const targetId = event.target.getAttribute('data-target');
                    const logType = event.target.getAttribute('data-type') || '';
                    if (username && targetId) {
                        loadClientLogs(username, targetId, logType);
                    }
                }

                // 日志类型按钮
                if (event.target.classList.contains('log-type-btn')) {
                    // 移除同组所有按钮的active类
                    const btnGroup = event.target.closest('.btn-group');
                    if (btnGroup) {
                        btnGroup.querySelectorAll('.log-type-btn').forEach(btn => {
                            btn.classList.remove('active');
                        });
                    }

                    // 添加active类到当前按钮
                    event.target.classList.add('active');

                    // 加载对应类型的日志
                    const username = event.target.getAttribute('data-username');
                    const targetId = event.target.getAttribute('data-target');
                    const logType = event.target.getAttribute('data-type') || '';
                    if (username && targetId) {
                        loadClientLogs(username, targetId, logType);
                    }
                }
            });

            // 设置定时刷新 (每30秒)
            setInterval(loadLiveStatus, 30000);
        });
    </script>
</body>
</html>
