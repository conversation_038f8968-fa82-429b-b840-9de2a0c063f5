@echo off
chcp 65001 > nul
title AI主播系统服务器 - 修复版
cls

echo.
echo ==========================================
echo    AI主播系统服务器 - 独立版 (修复版)
echo ==========================================
echo.
echo 正在启动服务器...
echo.
echo 管理后台: http://localhost:12456/admin
echo 默认账号: kaer / a13456A
echo.
echo 提示: 
echo - 首次运行会自动创建和初始化数据库
echo - 如果遇到数据库问题，请运行 fix_database.py
echo.

"AI主播系统服务器.exe"

if errorlevel 1 (
    echo.
    echo 服务器启动失败！
    echo.
    echo 可能的原因:
    echo    - 端口12456被占用
    echo    - 防火墙阻止
    echo    - 权限不足
    echo    - 数据库问题
    echo.
    echo 解决方案:
    echo    - 以管理员权限运行
    echo    - 检查防火墙设置
    echo    - 关闭占用端口的程序
    echo    - 运行 fix_database.py 修复数据库
    echo.
) else (
    echo.
    echo 服务器已正常停止
    echo.
)

pause
