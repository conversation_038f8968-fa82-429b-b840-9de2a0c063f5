#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复打包后静态文件路径问题
解决CSS和JS文件无法加载的问题
"""

import os
import re

def fix_static_paths():
    """修复静态文件路径配置"""
    print("🔧 修复静态文件路径配置...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('server.py.static_backup', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已备份原文件")
        
        # 修复Flask应用的静态文件配置
        old_static_config = '''# 创建 Flask 应用，指定模板和静态文件目录
# Flask 不支持多个模板目录，所以我们使用当前目录下的 templates 目录
template_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), 'templates'))
static_dir = os.path.abspath(STATIC_DIR)
app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)'''
        
        new_static_config = '''# 创建 Flask 应用，指定模板和静态文件目录
# 支持打包后的环境
def get_resource_path(relative_path):
    """获取资源文件路径，兼容打包环境"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        return os.path.join(base_path, relative_path)
    except Exception:
        return os.path.join(os.path.dirname(__file__), relative_path)

template_dir = get_resource_path('templates')
static_dir = get_resource_path('static')
app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)'''
        
        if old_static_config in content:
            content = content.replace(old_static_config, new_static_config)
            print("✅ 已更新Flask静态文件配置")
        else:
            print("⚠️  未找到Flask静态文件配置，手动添加...")
            # 确保导入了sys
            if 'import sys' not in content:
                content = content.replace('import os', 'import os\nimport sys')
            
            # 在Flask应用创建前添加辅助函数
            flask_pattern = r'(app = Flask\(__name__.*?\))'
            if re.search(flask_pattern, content):
                helper_function = '''
def get_resource_path(relative_path):
    """获取资源文件路径，兼容打包环境"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        return os.path.join(base_path, relative_path)
    except Exception:
        return os.path.join(os.path.dirname(__file__), relative_path)

'''
                content = re.sub(flask_pattern, helper_function + r'\1', content)
                print("✅ 已添加资源路径辅助函数")
        
        # 修复静态目录配置
        content = re.sub(
            r'template_dir = os\.path\.abspath\(os\.path\.join\(os\.path\.dirname\(__file__\), \'templates\'\)\)',
            'template_dir = get_resource_path(\'templates\')',
            content
        )
        
        content = re.sub(
            r'static_dir = os\.path\.abspath\(STATIC_DIR\)',
            'static_dir = get_resource_path(\'static\')',
            content
        )
        
        # 确保sys导入存在
        if 'import sys' not in content:
            content = content.replace('import os', 'import os\nimport sys')
            print("✅ 已添加sys导入")
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 静态文件路径修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复静态文件路径失败: {str(e)}")
        return False

def add_static_file_routes():
    """添加额外的静态文件路由"""
    print("\n🔧 添加额外的静态文件路由...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有额外的静态文件路由
        if '@app.route(\'/css/<path:filename>\')' in content:
            print("ℹ️  额外的静态文件路由已存在")
            return True
        
        # 添加额外的静态文件路由
        additional_routes = '''
# 额外的静态文件路由（用于打包后的环境）
@app.route('/css/<path:filename>')
def serve_css(filename):
    """提供CSS文件"""
    try:
        css_dir = get_resource_path('static/css')
        return send_from_directory(css_dir, filename, mimetype='text/css')
    except Exception as e:
        logger.error(f"提供CSS文件出错: {str(e)}")
        return "/* CSS file not found */", 404, {'Content-Type': 'text/css'}

@app.route('/js/<path:filename>')
def serve_js(filename):
    """提供JavaScript文件"""
    try:
        js_dir = get_resource_path('static/js')
        return send_from_directory(js_dir, filename, mimetype='application/javascript')
    except Exception as e:
        logger.error(f"提供JS文件出错: {str(e)}")
        return "/* JS file not found */", 404, {'Content-Type': 'application/javascript'}

@app.route('/images/<path:filename>')
def serve_images(filename):
    """提供图片文件"""
    try:
        images_dir = get_resource_path('static/images')
        return send_from_directory(images_dir, filename)
    except Exception as e:
        logger.error(f"提供图片文件出错: {str(e)}")
        return "", 404

@app.route('/fonts/<path:filename>')
def serve_fonts(filename):
    """提供字体文件"""
    try:
        fonts_dir = get_resource_path('static/fonts')
        return send_from_directory(fonts_dir, filename)
    except Exception as e:
        logger.error(f"提供字体文件出错: {str(e)}")
        return "", 404
'''
        
        # 在现有的静态文件路由后添加
        static_route_pattern = r'(@app\.route\(\'/static/downloads/<path:filename>\'\).*?return jsonify\({.*?}\), 500)'
        if re.search(static_route_pattern, content, re.DOTALL):
            content = re.sub(
                static_route_pattern,
                r'\1' + additional_routes,
                content,
                flags=re.DOTALL
            )
            print("✅ 已添加额外的静态文件路由")
        else:
            # 如果找不到现有路由，在文件末尾添加
            content += additional_routes
            print("✅ 已在文件末尾添加额外的静态文件路由")
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 添加静态文件路由失败: {str(e)}")
        return False

def create_static_fix_script():
    """创建静态文件修复脚本"""
    print("\n🔧 创建静态文件修复脚本...")
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态文件修复脚本
用于修复打包后静态文件无法加载的问题
"""

import os
import sys
import shutil

def fix_static_files():
    """修复静态文件问题"""
    print("🚀 开始修复静态文件问题...")
    
    try:
        # 获取当前目录
        current_dir = os.getcwd()
        print(f"当前目录: {current_dir}")
        
        # 检查是否在打包后的环境中
        if hasattr(sys, '_MEIPASS'):
            print("✅ 检测到打包环境")
            base_path = sys._MEIPASS
            print(f"打包基础路径: {base_path}")
            
            # 检查静态文件是否存在
            static_source = os.path.join(base_path, 'static')
            static_target = os.path.join(current_dir, 'static')
            
            if os.path.exists(static_source) and not os.path.exists(static_target):
                print("📁 复制静态文件到当前目录...")
                shutil.copytree(static_source, static_target)
                print("✅ 静态文件复制完成")
            elif os.path.exists(static_target):
                print("ℹ️  静态文件已存在")
            else:
                print("⚠️  未找到静态文件源")
            
            # 检查模板文件
            templates_source = os.path.join(base_path, 'templates')
            templates_target = os.path.join(current_dir, 'templates')
            
            if os.path.exists(templates_source) and not os.path.exists(templates_target):
                print("📁 复制模板文件到当前目录...")
                shutil.copytree(templates_source, templates_target)
                print("✅ 模板文件复制完成")
            elif os.path.exists(templates_target):
                print("ℹ️  模板文件已存在")
            else:
                print("⚠️  未找到模板文件源")
        else:
            print("ℹ️  开发环境，无需修复")
        
        # 创建必要的目录
        required_dirs = [
            'static/css',
            'static/js', 
            'static/images',
            'static/fonts',
            'static/downloads',
            'server_data/static/downloads'
        ]
        
        for dir_path in required_dirs:
            full_path = os.path.join(current_dir, dir_path)
            if not os.path.exists(full_path):
                os.makedirs(full_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
        
        print("🎉 静态文件修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_static_files()
    if success:
        print("✅ 静态文件修复成功！")
        print("💡 现在可以正常访问网页了")
    else:
        print("❌ 静态文件修复失败！")
    
    input("按回车键退出...")
'''
    
    with open('fix_static_files.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 已创建静态文件修复脚本: fix_static_files.py")
    return True

def main():
    """主函数"""
    print("🚀 修复静态文件路径问题")
    print("=" * 50)
    
    try:
        # 1. 修复静态文件路径配置
        if not fix_static_paths():
            return False
        
        # 2. 添加额外的静态文件路由
        if not add_static_file_routes():
            return False
        
        # 3. 创建静态文件修复脚本
        if not create_static_fix_script():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 静态文件路径修复完成！")
        print("\n📝 修复内容:")
        print("✅ 更新了Flask静态文件配置")
        print("✅ 添加了资源路径辅助函数")
        print("✅ 添加了额外的静态文件路由")
        print("✅ 创建了静态文件修复脚本")
        
        print("\n💡 下一步:")
        print("1. 重新打包程序")
        print("2. 如果CSS仍无法加载，运行 fix_static_files.py")
        print("3. 测试网页界面是否正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
