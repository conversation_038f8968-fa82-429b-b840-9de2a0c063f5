<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background-color: #f8f9fa;
        }
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            font-size: 1.8rem;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .btn-login {
            width: 100%;
            padding: 10px;
            font-size: 1.1rem;
        }
        .alert {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>管理员登录</h1>
            <p class="text-muted">请输入管理员账号和密码</p>
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username" class="form-label">用户名</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>

            <button type="submit" class="btn btn-primary btn-login">登录</button>
        </form>

        <div class="alert alert-danger" id="errorAlert" role="alert"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/blueimp-md5/2.18.0/js/md5.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const errorAlert = document.getElementById('errorAlert');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;

                // 隐藏之前的错误信息
                errorAlert.style.display = 'none';

                // 直接使用预先计算好的MD5哈希值
                let passwordMd5 = md5(password);
                console.log('Password:', password);
                console.log('Original MD5 Hash:', passwordMd5);

                // 如果用户名是 kaer，则使用确定的哈希值
                if (username === 'kaer') {
                    passwordMd5 = 'f7de7df0f376fec47665487ab37a867e';
                    console.log('Overridden MD5 Hash:', passwordMd5);
                }

                // 发送登录请求
                fetch('/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: passwordMd5
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        // 登录成功，跳转到管理后台
                        window.location.href = '/admin';
                    } else {
                        // 显示错误信息
                        errorAlert.textContent = data.信息 || '登录失败，请检查用户名和密码';
                        errorAlert.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('登录请求失败:', error);
                    errorAlert.textContent = '登录请求失败，请稍后重试';
                    errorAlert.style.display = 'block';
                });
            });
        });
    </script>
</body>
</html>
