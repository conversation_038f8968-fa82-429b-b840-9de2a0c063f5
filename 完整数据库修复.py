#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数据库修复脚本
解决所有数据库相关问题
"""

import os
import sys
import sqlite3
import glob

def find_all_databases():
    """查找所有数据库文件"""
    db_files = []
    
    # 当前目录
    for db_name in ["local.db", "server_data.db"]:
        if os.path.exists(db_name):
            db_files.append(db_name)
    
    # 临时目录
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        patterns = [
            os.path.join(temp_dir, "_MEI*", "server_data.db"),
            os.path.join(temp_dir, "_MEI*", "local.db")
        ]
        for pattern in patterns:
            matches = glob.glob(pattern)
            db_files.extend(matches)
    
    return list(set(db_files))  # 去重

def fix_database_schema(db_path):
    """修复数据库表结构"""
    print(f"修复数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. 检查并修复client_updates表
        print("  检查client_updates表...")
        
        # 先检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_updates'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            # 检查列是否存在
            cursor.execute("PRAGMA table_info(client_updates)")
            columns = [row[1] for row in cursor.fetchall()]
            
            missing_columns = []
            required_columns = {
                'fast_download_url': 'TEXT',
                'is_exe': 'INTEGER DEFAULT 0',
                'is_folder_update': 'INTEGER DEFAULT 0',
                'file_path': 'TEXT',
                'file_size': 'INTEGER',
                'file_hash': 'TEXT',
                'download_count': 'INTEGER DEFAULT 0',
                'status': 'TEXT DEFAULT "pending"'
            }
            
            for col_name, col_type in required_columns.items():
                if col_name not in columns:
                    missing_columns.append((col_name, col_type))
            
            # 添加缺失的列
            for col_name, col_type in missing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {col_name} {col_type}")
                    print(f"    ✅ 添加列: {col_name}")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" not in str(e):
                        print(f"    ⚠️ 添加列失败: {col_name} - {str(e)}")
        else:
            # 创建完整的client_updates表
            cursor.execute("""
            CREATE TABLE client_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL,
                release_date TEXT NOT NULL,
                description TEXT,
                download_url TEXT NOT NULL,
                force_update INTEGER DEFAULT 0,
                is_current INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_path TEXT,
                file_size INTEGER,
                file_hash TEXT,
                download_count INTEGER DEFAULT 0,
                status TEXT DEFAULT "pending",
                fast_download_url TEXT,
                is_exe INTEGER DEFAULT 0,
                is_folder_update INTEGER DEFAULT 0
            )
            """)
            print("    ✅ 创建client_updates表")
        
        # 2. 创建其他必要的表
        tables_to_create = [
            ("logs", """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """),
            ("live_status", """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """),
            ("api_configs", """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """),
            ("danmaku_records", """
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                sender TEXT,
                content TEXT NOT NULL,
                timestamp TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """),
            ("tokens", """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """)
        ]
        
        for table_name, create_sql in tables_to_create:
            cursor.execute(create_sql)
            print(f"    ✅ 检查/创建{table_name}表")
        
        # 3. 插入示例数据
        cursor.execute("SELECT COUNT(*) FROM client_updates")
        count = cursor.fetchone()[0]
        
        if count == 0:
            cursor.execute("""
            INSERT INTO client_updates (
                version, release_date, description, download_url, 
                force_update, is_current, status, fast_download_url,
                file_size, download_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "1.8",
                "2025-06-14",
                "1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性",
                "http://localhost:12456/static/downloads/AI主播系统.zip",
                0,
                1,
                "published",
                "http://localhost:12456/api/fast-download/AI主播系统.zip",
                25600000,  # 25.6MB
                0
            ))
            print("    ✅ 插入示例更新记录")
        
        conn.commit()
        conn.close()
        print(f"  ✅ 数据库修复完成: {db_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 开始完整数据库修复...")
    print("=" * 60)
    
    db_files = find_all_databases()
    
    if not db_files:
        print("⚠️ 未找到数据库文件")
        return
    
    success_count = 0
    for db_file in db_files:
        if fix_database_schema(db_file):
            success_count += 1
    
    print()
    print("=" * 60)
    print(f"🎉 数据库修复完成！")
    print(f"📊 成功修复: {success_count}/{len(db_files)} 个数据库")
    print()
    print("💡 修复内容:")
    print("- ✅ client_updates表结构完整")
    print("- ✅ fast_download_url列已添加")
    print("- ✅ 所有必要的表已创建")
    print("- ✅ 示例数据已插入")
    print()
    print("🔄 请重新启动服务器以应用修复")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
