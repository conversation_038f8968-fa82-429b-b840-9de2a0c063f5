import os
import json
import time
import hashlib
import logging
import pymysql
import jwt
import uuid
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 远程 MySQL 数据库配置
DB_CONFIG = {
    "host": "**************",
    "user": "pclovelily",
    "password": "pc576879lily",
    "database": "reg",
    "port": 3306,
    "charset": "utf8mb4"
}

# 本地 SQLite 数据库路径
LOCAL_DB_PATH = os.path.join(os.path.dirname(__file__), "server_data.db")

# Token相关配置
TOKEN_SECRET_KEY = "ai_virtual_anchor_token_secret_key"
TOKEN_ALGORITHM = "HS256"
TOKEN_EXPIRY_DAYS = 1  # Token有效期1天

def get_db_connection():
    """获取远程 MySQL 数据库连接"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        logger.info("远程 MySQL 数据库连接成功")
        return conn
    except Exception as e:
        logger.error(f"远程 MySQL 数据库连接失败: {str(e)}")
        return None

def get_sqlite_connection():
    """获取本地 SQLite 数据库连接"""
    try:
        import sqlite3
        # 设置超时参数，避免"database is locked"错误
        conn = sqlite3.connect(LOCAL_DB_PATH, timeout=30.0, check_same_thread=False)
        # 设置更长的忙等待超时
        conn.execute("PRAGMA busy_timeout = 30000")
        # 启用外键约束
        conn.execute("PRAGMA foreign_keys = ON")
        # 设置日志模式为WAL（Write-Ahead Logging），提高并发性能
        conn.execute("PRAGMA journal_mode = WAL")
        logger.info(f"本地 SQLite 数据库连接成功: {LOCAL_DB_PATH}")
        return conn
    except Exception as e:
        logger.error(f"本地 SQLite 数据库连接失败: {str(e)}")
        return None

def init_database():
    """初始化远程 MySQL 数据库表"""
    conn = get_db_connection()
    if not conn:
        return False

    cursor = conn.cursor()

    try:
        # 不创建用户表，因为已经存在
        # list表的字段已经存在：
        # kfm(用户名), pwd(密码), tel(电话), jqm(机器码), regtime(注册时间),
        # logintime(登录时间), bz(备注), dqtime(到期时间), status(状态)

        # 不创建卡密表，因为已经存在
        # cards表的字段已经存在：
        # id, card_no(卡号), days(天数), status(状态), create_time(创建时间),
        # use_time(使用时间), use_by(使用者), copy_from(复制自)

        # 创建日志表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50),
            ip VARCHAR(50),
            action VARCHAR(50) NOT NULL,
            details TEXT,
            time DATETIME
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ''')

        conn.commit()
        logger.info("远程 MySQL 数据库表初始化成功")
        return True
    except Exception as e:
        logger.error(f"远程 MySQL 数据库表初始化失败: {str(e)}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def init_sqlite_database():
    """初始化本地 SQLite 数据库表"""
    conn = get_sqlite_connection()
    if not conn:
        return False

    cursor = conn.cursor()

    try:
        # 创建日志表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 检查live_status表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='live_status'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            # 如果表已存在，检查并添加缺失的列
            logger.info("直播状态表已存在，检查并添加缺失的列")

            # 获取现有列
            cursor.execute("PRAGMA table_info(live_status)")
            existing_columns = [row[1] for row in cursor.fetchall()]
            logger.info(f"现有列: {existing_columns}")

            # 检查并添加缺失的列
            required_columns = {
                "danmaku_history": "TEXT",
                "voice_history": "TEXT",
                "obs_source": "TEXT",
                "token": "TEXT",
                "ip": "TEXT",
                "machine_code": "TEXT",
                "is_online": "INTEGER DEFAULT 1"
            }

            for column, column_type in required_columns.items():
                if column not in existing_columns:
                    logger.warning(f"添加缺失的列: {column}")
                    try:
                        cursor.execute(f"ALTER TABLE live_status ADD COLUMN {column} {column_type}")
                    except Exception as e:
                        logger.error(f"添加列 {column} 失败: {str(e)}")
        else:
            # 如果表不存在，创建新表
            logger.info("创建直播状态表")
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            ''')

        # 创建token表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        ''')

        # 创建客户端更新表（如果不存在）
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        conn.commit()
        logger.info("本地 SQLite 数据库表初始化成功")
        return True
    except Exception as e:
        logger.error(f"本地 SQLite 数据库表初始化失败: {str(e)}")
        try:
            conn.rollback()
        except:
            pass
        return False
    finally:
        cursor.close()
        conn.close()

def generate_token(username, ip, machine_code):
    """生成JWT token"""
    try:
        # 设置token过期时间
        now = datetime.now()
        expiry = now + timedelta(days=TOKEN_EXPIRY_DAYS)

        # 创建token payload - 使用时间戳而不是datetime对象
        payload = {
            "sub": username,
            "ip": ip,
            "machine_code": machine_code,
            "jti": str(uuid.uuid4()),  # 唯一标识符
            "iat": int(now.timestamp()),  # 发行时间（时间戳）
            "exp": int(expiry.timestamp())  # 过期时间（时间戳）
        }

        # 生成token
        token = jwt.encode(payload, TOKEN_SECRET_KEY, algorithm=TOKEN_ALGORITHM)

        # 保存token到数据库
        conn = get_sqlite_connection()
        if conn:
            cursor = conn.cursor()
            try:
                # 将之前的token设为无效
                cursor.execute(
                    "UPDATE tokens SET is_active = 0 WHERE username = ? AND is_active = 1",
                    (username,)
                )

                # 插入新token
                cursor.execute(
                    "INSERT INTO tokens (username, token, expires_at, ip, machine_code) VALUES (?, ?, ?, ?, ?)",
                    (username, token, expiry.strftime("%Y-%m-%d %H:%M:%S"), ip, machine_code)
                )
                conn.commit()
                logger.info(f"已为用户 {username} 生成新token并保存到数据库")
            except Exception as e:
                logger.error(f"保存token到数据库失败: {str(e)}")
                conn.rollback()
            finally:
                cursor.close()
                conn.close()

        return token
    except Exception as e:
        logger.error(f"生成token失败: {str(e)}")
        return None

def verify_token(token):
    """验证JWT token"""
    try:
        logger.info(f"开始验证token: {token[:10]}...")

        # 先尝试直接解码token，不依赖数据库
        try:
            # 解码token
            payload = jwt.decode(token, TOKEN_SECRET_KEY, algorithms=[TOKEN_ALGORITHM])
            logger.info(f"JWT解码成功: {payload}")

            # 从数据库检查token是否有效
            conn = get_sqlite_connection()
            if conn:
                cursor = conn.cursor()
                try:
                    cursor.execute(
                        "SELECT username, expires_at, is_active FROM tokens WHERE token = ?",
                        (token,)
                    )
                    result = cursor.fetchone()

                    if not result:
                        logger.warning(f"Token不存在于数据库中，但JWT解码成功")
                        # 如果JWT解码成功但数据库中没有记录，仍然返回payload
                        return payload

                    username, expires_at, is_active = result
                    logger.info(f"数据库中找到token记录: 用户={username}, 过期时间={expires_at}, 是否有效={is_active}")

                    if not is_active:
                        logger.warning(f"Token已被禁用: {username}")
                        return None

                    # 检查是否过期
                    expires_dt = datetime.strptime(expires_at, "%Y-%m-%d %H:%M:%S")
                    if datetime.now() > expires_dt:
                        logger.warning(f"Token已过期: {username}")
                        return None

                    return payload
                except Exception as e:
                    logger.error(f"数据库查询token时出错: {str(e)}")
                    # 如果数据库查询出错，但JWT解码成功，仍然返回payload
                    return payload
                finally:
                    cursor.close()
                    conn.close()
            else:
                logger.warning("无法连接到数据库，但JWT解码成功")
                # 如果无法连接到数据库，但JWT解码成功，仍然返回payload
                return payload
        except jwt.ExpiredSignatureError:
            logger.warning("JWT解码失败: Token已过期")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"JWT解码失败: 无效的Token: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"JWT解码失败: {str(e)}")
            # JWT解码失败，尝试从数据库查询
            pass

        # 如果JWT解码失败，尝试从数据库查询
        conn = get_sqlite_connection()
        if conn:
            cursor = conn.cursor()
            try:
                cursor.execute(
                    "SELECT username, expires_at, is_active FROM tokens WHERE token = ?",
                    (token,)
                )
                result = cursor.fetchone()

                if not result:
                    logger.warning(f"Token不存在于数据库中")
                    return None

                username, expires_at, is_active = result

                if not is_active:
                    logger.warning(f"Token已被禁用: {username}")
                    return None

                # 检查是否过期
                expires_dt = datetime.strptime(expires_at, "%Y-%m-%d %H:%M:%S")
                if datetime.now() > expires_dt:
                    logger.warning(f"Token已过期: {username}")
                    return None

                # 尝试再次解码token
                try:
                    payload = jwt.decode(token, TOKEN_SECRET_KEY, algorithms=[TOKEN_ALGORITHM])
                    return payload
                except Exception as e:
                    logger.error(f"再次解码token失败: {str(e)}")
                    # 如果解码失败，创建一个基本的payload
                    return {"sub": username}
            except Exception as e:
                logger.error(f"验证token时出错: {str(e)}")
                return None
            finally:
                cursor.close()
                conn.close()
        return None
    except Exception as e:
        logger.error(f"验证token失败: {str(e)}")
        return None

def invalidate_token(username):
    """使指定用户的所有token失效（踢下线）并更新直播状态"""
    try:
        conn = get_sqlite_connection()
        if not conn:
            return False

        cursor = conn.cursor()
        try:
            # 将用户的所有token设为无效
            cursor.execute(
                "UPDATE tokens SET is_active = 0 WHERE username = ? AND is_active = 1",
                (username,)
            )

            # 更新直播状态表中该用户的状态为下线
            cursor.execute(
                "UPDATE live_status SET is_online = 0, last_update_time = datetime('now', 'localtime') WHERE username = ?",
                (username,)
            )

            conn.commit()
            logger.info(f"已使用户 {username} 的所有token失效并更新直播状态为下线")
            return True
        except Exception as e:
            logger.error(f"使token失效失败: {str(e)}")
            conn.rollback()
            return False
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        logger.error(f"使token失效失败: {str(e)}")
        return False

def update_live_status(username, data):
    """更新用户直播状态"""
    # 检查用户名是否为空
    if not username:
        logger.error("更新直播状态失败: 用户名不能为空")
        return False

    # 确保不会将kaera转换为kaer
    if username == "kaera":
        logger.warning(f"检测到用户名为 kaera，保持原样")
        # 不再强制转换为 kaer

    # 检查数据是否为空
    if not data or not isinstance(data, dict):
        logger.error(f"更新用户 {username} 的直播状态失败: 数据不能为空或非字典类型")
        return False

    try:
        conn = get_sqlite_connection()
        if not conn:
            logger.error(f"更新用户 {username} 的直播状态失败: 无法连接到数据库")
            return False

        cursor = conn.cursor()
        try:
            # 确保is_online字段存在并正确设置
            if "is_online" not in data:
                data["is_online"] = True
            else:
                # 确保是布尔值或整数
                if isinstance(data["is_online"], str):
                    data["is_online"] = data["is_online"].lower() in ["true", "1", "yes"]
                data["is_online"] = 1 if data["is_online"] else 0

            logger.info(f"更新用户 {username} 的直播状态，在线状态为: {data['is_online']}")

            # 检查是否已存在记录
            try:
                cursor.execute("SELECT id FROM live_status WHERE username = ?", (username,))
                result = cursor.fetchone()
            except Exception as e:
                logger.warning(f"查询用户记录时出错: {str(e)}，尝试简化查询")
                cursor.execute("SELECT id FROM live_status WHERE username = ?", (username,))
                result = cursor.fetchone()

            # 简化处理，不处理历史记录，避免表结构问题
            danmaku_history = []
            voice_history = []

            if result:
                # 更新现有记录
                update_fields = []
                params = []

                if "voice_play_time" in data:
                    update_fields.append("voice_play_time = ?")
                    params.append(data["voice_play_time"])

                if "current_script" in data:
                    update_fields.append("current_script = ?")
                    params.append(data["current_script"])

                if "online_count" in data:
                    update_fields.append("online_count = ?")
                    params.append(data["online_count"])

                if "danmaku" in data:
                    update_fields.append("danmaku = ?")
                    params.append(data["danmaku"])

                # 简化处理，不更新历史记录，避免表结构问题

                if "obs_source" in data:
                    update_fields.append("obs_source = ?")
                    params.append(data["obs_source"])

                # 设置在线状态
                if "is_online" in data:
                    update_fields.append("is_online = ?")
                    params.append(1 if data["is_online"] else 0)
                else:
                    # 默认为在线
                    update_fields.append("is_online = 1")

                # 始终更新最后更新时间
                update_fields.append("last_update_time = datetime('now', 'localtime')")

                if update_fields:
                    query = f"UPDATE live_status SET {', '.join(update_fields)} WHERE username = ?"
                    params.append(username)
                    cursor.execute(query, params)
            else:
                # 创建新记录
                fields = ["username", "login_time", "is_online"]
                placeholders = ["?", "datetime('now', 'localtime')", "1"]
                params = [username]

                for field in ["voice_play_time", "current_script", "online_count", "danmaku", "obs_source", "token", "ip", "machine_code"]:
                    if field in data:
                        fields.append(field)
                        placeholders.append("?")
                        params.append(data[field])

                # 简化处理，不添加历史记录，避免表结构问题

                query = f"INSERT INTO live_status ({', '.join(fields)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(query, params)

            conn.commit()
            logger.info(f"已更新用户 {username} 的直播状态")
            return True
        except Exception as e:
            error_msg = str(e)
            logger.error(f"更新用户 {username} 的直播状态失败: {error_msg}")

            # 检查是否是表结构问题
            if "no such column" in error_msg.lower() or "no such table" in error_msg.lower():
                logger.warning(f"检测到数据库表结构问题，尝试初始化数据库")

                # 如果是特定列缺失问题，尝试处理
                if "no such column: danmaku_history" in error_msg.lower() or "no such column: voice_history" in error_msg.lower():
                    missing_column = "danmaku_history" if "no such column: danmaku_history" in error_msg.lower() else "voice_history"
                    logger.warning(f"特定列 {missing_column} 缺失，尝试从数据中移除")
                    # 从数据中移除该字段
                    if missing_column in data:
                        del data[missing_column]

                    # 尝试使用简化的数据再次更新
                    simplified_data = {
                        "is_online": data.get("is_online", True),
                        "last_update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # 关闭当前连接
                    cursor.close()
                    conn.close()

                    # 初始化数据库
                    if init_sqlite_database():
                        logger.info("数据库初始化成功，使用简化数据重试")
                        return update_live_status(username, simplified_data)
                else:
                    # 尝试初始化数据库
                    cursor.close()
                    conn.close()

                    if init_sqlite_database():
                        # 如果初始化成功，重新尝试更新
                        logger.info(f"数据库初始化成功，重新尝试更新直播状态")
                        return update_live_status(username, data)

            try:
                conn.rollback()
            except Exception as rollback_error:
                logger.error(f"回滚事务失败: {str(rollback_error)}")

            return False
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        error_msg = str(e)
        logger.error(f"更新用户 {username} 的直播状态失败(外层异常): {error_msg}")

        # 检查是否是数据库连接问题
        if "database is locked" in error_msg.lower() or "unable to open database file" in error_msg.lower():
            logger.warning(f"检测到数据库连接问题，尝试重新连接")
            # 等待一秒后重试
            import time
            time.sleep(1)
            try:
                # 重新初始化数据库连接
                if init_sqlite_database():
                    logger.info(f"数据库重新初始化成功，重新尝试更新直播状态")
                    return update_live_status(username, data)
            except Exception as retry_error:
                logger.error(f"重试更新直播状态失败: {str(retry_error)}")

        return False

def get_live_status(username=None):
    """获取用户直播状态"""
    try:
        conn = get_sqlite_connection()
        if not conn:
            return {"状态": "失败", "信息": "数据库连接失败"}

        cursor = conn.cursor()
        try:
            if username:
                # 获取指定用户的直播状态
                cursor.execute(
                    "SELECT * FROM live_status WHERE username = ? ORDER BY last_update_time DESC LIMIT 1",
                    (username,)
                )
                columns = [column[0] for column in cursor.description]
                result = cursor.fetchone()

                if result:
                    # 将结果转换为字典
                    status = dict(zip(columns, result))
                    return {"状态": "成功", "信息": "获取直播状态成功", "数据": status}
                else:
                    return {"状态": "失败", "信息": "未找到该用户的直播状态"}
            else:
                # 获取所有用户的直播状态
                cursor.execute(
                    "SELECT * FROM live_status ORDER BY is_online DESC, last_update_time DESC"
                )
                columns = [column[0] for column in cursor.description]
                results = cursor.fetchall()

                status_list = []
                for result in results:
                    status = dict(zip(columns, result))
                    # 确保 is_online 字段是布尔值，方便前端处理
                    status["is_online"] = bool(status.get("is_online", 0))

                    # 检查最后更新时间，如果超过5分钟没有更新，则设置为离线
                    if status.get("last_update_time"):
                        try:
                            from datetime import datetime, timedelta
                            # 尝试解析时间字符串
                            last_update = datetime.strptime(status["last_update_time"], "%Y-%m-%d %H:%M:%S")
                            # 如果超过30秒没有更新，则设置为离线
                            if datetime.now() - last_update > timedelta(seconds=30):
                                status["is_online"] = False
                                logger.info(f"用户 {status.get('username')} 超过30秒没有更新状态，自动设置为离线")
                        except Exception as e:
                            logger.warning(f"检查用户在线状态时间出错: {str(e)}")

                    status_list.append(status)

                return {"状态": "成功", "信息": "获取直播状态列表成功", "数据": status_list}
        except Exception as e:
            logger.error(f"获取直播状态失败: {str(e)}")
            return {"状态": "失败", "信息": f"获取直播状态失败: {str(e)}"}
        finally:
            cursor.close()
            conn.close()
    except Exception as e:
        logger.error(f"获取直播状态失败: {str(e)}")
        return {"状态": "失败", "信息": f"获取直播状态失败: {str(e)}"}

def add_log(user_id, username, ip, action, details=""):
    """添加操作日志到本地SQLite数据库"""
    # 确保使用登录账号而不是机器码作为用户名
    # 如果username是机器码格式（通常是32位或64位的十六进制字符串），则尝试使用user_id作为用户名
    if username and (len(username) == 32 or len(username) == 64) and all(c in '0123456789abcdefABCDEF' for c in username):
        logger.info(f"检测到username可能是机器码，使用user_id作为用户名: {user_id}")
        username = user_id

    # 使用重试机制
    max_retries = 5
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 获取SQLite连接，设置超时参数
            import sqlite3
            conn = sqlite3.connect(LOCAL_DB_PATH, timeout=30.0, check_same_thread=False)
            # 设置更长的忙等待超时
            conn.execute("PRAGMA busy_timeout = 30000")
            cursor = conn.cursor()

            # 创建日志表（如果不存在）
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # 开始事务
            conn.execute("BEGIN IMMEDIATE TRANSACTION")

            # 插入日志
            cursor.execute(
                "INSERT INTO logs (username, ip, action, details, time) VALUES (?, ?, ?, ?, datetime('now', 'localtime'))",
                (username, ip, action, details)
            )
            conn.commit()
            logger.info(f"添加日志成功（SQLite）: {username} - {action}")
            cursor.close()
            conn.close()
            return True
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e) and retry_count < max_retries - 1:
                retry_count += 1
                logger.warning(f"数据库被锁定，正在重试添加日志 ({retry_count}/{max_retries})...")
                # 关闭连接并等待一段时间后重试
                try:
                    if 'conn' in locals() and conn:
                        conn.close()
                except:
                    pass
                import time
                time.sleep(1.0 * retry_count)  # 逐渐增加等待时间
            else:
                # 达到最大重试次数或其他错误
                logger.error(f"添加日志到SQLite失败: {str(e)}")
                # 如果SQLite失败，尝试使用MySQL
                return add_log_mysql(user_id, username, ip, action, details)
        except Exception as e:
            logger.error(f"添加日志到SQLite失败: {str(e)}")
            try:
                if 'conn' in locals() and conn:
                    conn.rollback()
                    conn.close()
            except:
                pass
            # 如果SQLite失败，尝试使用MySQL
            return add_log_mysql(user_id, username, ip, action, details)

    # 如果所有重试都失败，尝试使用MySQL
    logger.warning(f"添加日志到SQLite失败，达到最大重试次数 ({max_retries})，尝试使用MySQL")
    return add_log_mysql(user_id, username, ip, action, details)

def add_log_mysql(user_id, username, ip, action, details=""):
    """添加操作日志到远程 MySQL数据库"""
    # 确保使用登录账号而不是机器码作为用户名
    # 如果username是机器码格式（通常是32位或64位的十六进制字符串），则尝试使用user_id作为用户名
    if username and (len(username) == 32 or len(username) == 64) and all(c in '0123456789abcdefABCDEF' for c in username):
        logger.info(f"检测到username可能是机器码，使用user_id作为用户名: {user_id}")
        username = user_id

    # 使用重试机制
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            conn = get_db_connection()
            if not conn:
                logger.error("无法连接到远程 MySQL数据库，日志添加失败")
                return False

            cursor = conn.cursor()

            # 不再使用user_id字段，只使用username
            cursor.execute(
                "INSERT INTO logs (username, ip, action, details, time) VALUES (%s, %s, %s, %s, NOW())",
                (username, ip, action, details)
            )
            conn.commit()
            logger.info(f"添加日志成功（MySQL）: {username} - {action}")
            cursor.close()
            conn.close()
            return True
        except pymysql.OperationalError as e:
            # 处理连接错误，如连接超时、服务器已关闭连接等
            retry_count += 1
            logger.warning(f"MySQL连接错误，正在重试添加日志 ({retry_count}/{max_retries}): {str(e)}")
            # 关闭连接并等待一段时间后重试
            try:
                if 'conn' in locals() and conn:
                    conn.close()
            except:
                pass
            import time
            time.sleep(1.0 * retry_count)  # 逐渐增加等待时间
        except Exception as e:
            logger.error(f"添加日志到MySQL失败: {str(e)}")
            try:
                if 'conn' in locals() and conn:
                    conn.rollback()
                    conn.close()
            except:
                pass
            # 对于非连接错误，不再重试
            return False

    # 如果所有重试都失败
    logger.error(f"添加日志到MySQL失败，达到最大重试次数 ({max_retries})")
    return False

def register_user(username, password, phone, machine_code, ip):
    """注册新用户"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 检查用户名是否已存在
        cursor.execute("SELECT kfm FROM list WHERE kfm = %s", (username,))
        if cursor.fetchone():
            return {"状态": "失败", "信息": "用户名已存在"}

        # 设置默认过期时间为当前时间加7天（给新用户7天试用期）
        now = datetime.now()
        default_expire_time = now + timedelta(days=7)  # 给新用户7天试用期
        formatted_expire_time = default_expire_time.strftime("%Y-%m-%d %H:%M:%S")

        logger.info(f"新用户 {username} 的默认过期时间设置为: {formatted_expire_time}")

        # 插入新用户，使用标准格式的日期时间字符串
        cursor.execute(
            "INSERT INTO list (kfm, pwd, tel, jqm, dqtime, regtime, status) VALUES (%s, %s, %s, %s, %s, NOW(), 1)",
            (username, password, phone, machine_code, formatted_expire_time)
        )
        user_id = conn.insert_id()
        conn.commit()

        # 添加注册日志
        add_log(user_id, username, ip, "注册", f"新用户注册，手机号: {phone}, 机器码: {machine_code}")

        logger.info(f"用户注册成功: {username}")
        return {"状态": "成功", "信息": "注册成功", "用户ID": user_id}
    except Exception as e:
        logger.error(f"用户注册失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"注册失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def login_user(username, password, machine_code, ip):
    """用户登录"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 查询用户
        cursor.execute("SELECT kfm, pwd, jqm, dqtime, status FROM list WHERE kfm = %s", (username,))
        user = cursor.fetchone()

        if not user:
            return {"状态": "失败", "信息": "用户不存在"}

        db_username, db_password, db_machine_code, expire_time, status = user
        # 使用用户名作为用户ID
        user_id = db_username

        # 检查账号状态
        if status == 0:
            return {"状态": "失败", "信息": "账号已被封禁"}

        # 检查密码
        if password != db_password:
            # 添加失败日志
            add_log(user_id, username, ip, "登录失败", "密码错误")
            return {"状态": "失败", "信息": "密码错误"}

        # 检查是否过期
        is_expired = False
        try:
            if expire_time:
                # 如果是Unix时间戳（字符串形式）
                if isinstance(expire_time, str) and expire_time.isdigit():
                    expire_dt = datetime.fromtimestamp(int(expire_time))
                # 如果是已格式化的时间字符串
                elif isinstance(expire_time, str):
                    expire_dt = datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
                # 如果是datetime对象
                else:
                    expire_dt = expire_time

                if datetime.now() > expire_dt:
                    is_expired = True
                    logger.warning(f"用户 {username} 账号已过期，过期时间: {expire_time}")
                else:
                    logger.info(f"用户 {username} 账号未过期，过期时间: {expire_time}")
            else:
                logger.warning(f"用户 {username} 没有过期时间记录")
                # 如果没有过期时间，则设置为过期
                is_expired = True
        except Exception as e:
            logger.warning(f"检查过期时间失败: {str(e)}")
            # 如果无法解析时间，则假设已过期
            is_expired = True

        if is_expired:
            # 添加失败日志
            add_log(user_id, username, ip, "登录失败", "账号已过期")
            return {"状态": "失败", "信息": "账号已过期，请充值"}

        # 严格检查机器码，确保一个账号只能在一台电脑上使用
        if db_machine_code and db_machine_code != machine_code:
            # 添加失败日志
            add_log(user_id, username, ip, "登录失败", f"机器码不匹配，当前: {machine_code}, 注册: {db_machine_code}")
            return {"状态": "失败", "信息": "机器码不匹配，一个账号只能在一台电脑上使用，请联系管理员"}

        # 更新最后登录时间和机器码（如果为空）
        if not db_machine_code:
            cursor.execute(
                "UPDATE list SET logintime = NOW(), jqm = %s WHERE kfm = %s",
                (machine_code, username)
            )
            logger.info(f"用户 {username} 首次登录，已记录机器码: {machine_code}")
        else:
            cursor.execute(
                "UPDATE list SET logintime = NOW() WHERE kfm = %s",
                (username,)
            )
            logger.info(f"用户 {username} 登录成功，机器码验证通过: {machine_code}")
        conn.commit()

        # 生成token
        token = generate_token(username, ip, machine_code)
        if not token:
            logger.error(f"生成token失败: {username}")
            token = ""

        # 添加登录日志
        add_log(user_id, username, ip, "登录成功", f"机器码: {machine_code}")

        # 初始化直播状态，设置为在线
        # 先检查是否已存在记录，如果存在则更新，否则创建新记录
        conn_sqlite = get_sqlite_connection()
        if conn_sqlite:
            cursor_sqlite = conn_sqlite.cursor()
            try:
                # 检查是否已存在记录
                cursor_sqlite.execute("SELECT id FROM live_status WHERE username = ?", (username,))
                result = cursor_sqlite.fetchone()

                if result:
                    # 如果已存在记录，更新为在线状态
                    cursor_sqlite.execute(
                        "UPDATE live_status SET is_online = 1, login_time = datetime('now', 'localtime'), last_update_time = datetime('now', 'localtime'), token = ?, ip = ?, machine_code = ? WHERE username = ?",
                        (token, ip, machine_code, username)
                    )
                else:
                    # 如果不存在记录，创建新记录
                    cursor_sqlite.execute(
                        "INSERT INTO live_status (username, login_time, is_online, token, ip, machine_code) VALUES (?, datetime('now', 'localtime'), 1, ?, ?, ?)",
                        (username, token, ip, machine_code)
                    )
                conn_sqlite.commit()
                logger.info(f"用户 {username} 直播状态已设置为在线")
            except Exception as e:
                logger.error(f"更新直播状态失败: {str(e)}")
                conn_sqlite.rollback()
            finally:
                cursor_sqlite.close()
                conn_sqlite.close()

        # 使用原有函数也更新一次，确保兼容性
        try:
            # 先检查数据库表是否存在
            conn_check = get_sqlite_connection()
            if conn_check:
                cursor_check = conn_check.cursor()
                try:
                    # 检查live_status表是否存在
                    cursor_check.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='live_status'")
                    if not cursor_check.fetchone():
                        logger.warning("live_status表不存在，尝试初始化数据库")
                        init_sqlite_database()
                finally:
                    cursor_check.close()
                    conn_check.close()

            # 更新直播状态
            update_data = {
                "token": token,
                "ip": ip,
                "machine_code": machine_code,
                "is_online": True,
                "last_update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            update_result = update_live_status(username, update_data)

            if update_result:
                logger.info(f"用户 {username} 直播状态更新成功，已设置为在线")
            else:
                logger.warning(f"用户 {username} 直播状态更新失败，尝试再次更新")
                # 再次尝试更新，使用更简单的数据
                update_result = update_live_status(username, {"is_online": True})
                if update_result:
                    logger.info(f"用户 {username} 直播状态再次更新成功")
                else:
                    logger.warning(f"用户 {username} 直播状态再次更新仍然失败")
        except Exception as e:
            logger.error(f"更新用户 {username} 直播状态时出错: {str(e)}")

        # 格式化过期时间以确保它是可读的格式
        formatted_expire_time = ""
        try:
            if expire_time:
                logger.info(f"原始过期时间值: {expire_time}, 类型: {type(expire_time)}")

                # 如果是Unix时间戳（字符串形式）
                if isinstance(expire_time, str) and expire_time.isdigit():
                    try:
                        timestamp = int(expire_time)
                        expire_dt = datetime.fromtimestamp(timestamp)
                        logger.info(f"成功将时间戳 {timestamp} 转换为日期时间: {expire_dt}")
                    except Exception as e:
                        logger.error(f"时间戳转换失败: {str(e)}")
                        # 如果时间戳转换失败，使用当前时间加上默认天数
                        expire_dt = datetime.now() + timedelta(days=7)  # 默认给7天
                        logger.info(f"使用默认过期时间: {expire_dt}")

                # 如果是已格式化的时间字符串
                elif isinstance(expire_time, str):
                    try:
                        expire_dt = datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
                        logger.info(f"成功将字符串 {expire_time} 转换为日期时间: {expire_dt}")
                    except ValueError:
                        # 尝试其他常见日期格式
                        try:
                            expire_dt = datetime.strptime(expire_time, "%Y/%m/%d %H:%M:%S")
                        except ValueError:
                            # 如果所有格式都失败，使用当前时间加上默认天数
                            expire_dt = datetime.now() + timedelta(days=7)  # 默认给7天
                            logger.info(f"日期字符串格式不匹配，使用默认过期时间: {expire_dt}")

                # 如果是datetime对象
                elif isinstance(expire_time, datetime):
                    expire_dt = expire_time
                    logger.info(f"过期时间已经是datetime对象: {expire_dt}")

                # 其他情况
                else:
                    logger.warning(f"未知的过期时间格式: {type(expire_time)}")
                    # 使用当前时间加上默认天数
                    expire_dt = datetime.now() + timedelta(days=7)  # 默认给7天
                    logger.info(f"使用默认过期时间: {expire_dt}")

                # 格式化为标准格式
                formatted_expire_time = expire_dt.strftime("%Y-%m-%d %H:%M:%S")
                logger.info(f"最终格式化的过期时间: {formatted_expire_time}")

                # 更新数据库中的过期时间为标准格式
                try:
                    # 将格式化后的时间更新到数据库，确保下次读取时格式一致
                    cursor.execute(
                        "UPDATE list SET dqtime = %s WHERE kfm = %s",
                        (formatted_expire_time, username)
                    )
                    conn.commit()
                    logger.info(f"已更新用户 {username} 的过期时间格式为标准格式")
                except Exception as db_error:
                    logger.error(f"更新过期时间格式失败: {str(db_error)}")
                    # 失败不影响登录流程，继续执行
            else:
                # 如果没有过期时间，使用token过期时间
                expire_dt = datetime.now() + timedelta(days=TOKEN_EXPIRY_DAYS)
                formatted_expire_time = expire_dt.strftime("%Y-%m-%d %H:%M:%S")
                logger.warning(f"用户 {username} 没有过期时间记录，使用token过期时间: {formatted_expire_time}")

                # 将新的过期时间更新到数据库
                try:
                    cursor.execute(
                        "UPDATE list SET dqtime = %s WHERE kfm = %s",
                        (formatted_expire_time, username)
                    )
                    conn.commit()
                    logger.info(f"已为用户 {username} 设置默认过期时间")
                except Exception as db_error:
                    logger.error(f"设置默认过期时间失败: {str(db_error)}")
        except Exception as e:
            logger.warning(f"格式化过期时间失败: {str(e)}")
            # 如果格式化失败，使用token过期时间
            expire_dt = datetime.now() + timedelta(days=TOKEN_EXPIRY_DAYS)
            formatted_expire_time = expire_dt.strftime("%Y-%m-%d %H:%M:%S")
            logger.warning(f"使用默认token过期时间: {formatted_expire_time}")

        logger.info(f"用户登录成功: {username}, 过期时间: {formatted_expire_time}")
        return {
            "状态": "成功",
            "信息": "登录成功",
            "用户ID": user_id,
            "token": token,
            "过期时间": formatted_expire_time
        }
    except Exception as e:
        logger.error(f"用户登录失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"登录失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def recharge_user(username, password, card_code, machine_code, ip):
    """用户充值"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 查询用户
        cursor.execute("SELECT kfm, pwd, status FROM list WHERE kfm = %s", (username,))
        user = cursor.fetchone()

        if not user:
            return {"状态": "失败", "信息": "用户不存在"}

        db_username, db_password, status = user
        # 使用用户名作为用户ID
        user_id = db_username

        # 检查账号状态
        if status == 0:
            return {"状态": "失败", "信息": "账号已被封禁"}

        # 检查密码
        if password != db_password:
            # 添加失败日志
            add_log(user_id, username, ip, "充值失败", "密码错误")
            return {"状态": "失败", "信息": "密码错误"}

        # 查询卡密
        cursor.execute("SELECT id, days, status FROM cards WHERE card_no = %s", (card_code,))
        card = cursor.fetchone()

        if not card:
            # 添加失败日志
            add_log(user_id, username, ip, "充值失败", f"卡密不存在: {card_code}")
            return {"状态": "失败", "信息": "卡密不存在"}

        card_id, days, card_status = card

        # 检查卡密状态
        if card_status == 0:
            # 添加失败日志
            add_log(user_id, username, ip, "充值失败", f"卡密已使用: {card_code}")
            return {"状态": "失败", "信息": "卡密已被使用"}

        # 获取当前过期时间
        cursor.execute("SELECT dqtime FROM list WHERE kfm = %s", (username,))
        current_expire_time = cursor.fetchone()[0]

        # 计算新的过期时间
        from datetime import datetime, timedelta

        try:
            # 尝试解析当前到期时间
            if current_expire_time and isinstance(current_expire_time, str):
                current_dt = datetime.strptime(current_expire_time, "%Y-%m-%d %H:%M:%S")
                if current_dt > datetime.now():
                    # 如果当前未过期，则在当前过期时间基础上增加天数
                    new_expire_time = current_dt + timedelta(days=days)
                else:
                    # 如果已过期，则从当前时间开始计算
                    new_expire_time = datetime.now() + timedelta(days=days)
            else:
                # 如果没有到期时间，则从当前时间开始计算
                new_expire_time = datetime.now() + timedelta(days=days)
        except Exception as e:
            logger.warning(f"解析到期时间失败: {str(e)}")
            # 如果解析失败，则从当前时间开始计算
            new_expire_time = datetime.now() + timedelta(days=days)

        # 保存格式化后的24小时制时间字符串
        formatted_expire_time = new_expire_time.strftime("%Y-%m-%d %H:%M:%S")

        # 更新用户过期时间（使用标准格式的日期时间字符串）
        cursor.execute(
            "UPDATE list SET dqtime = %s WHERE kfm = %s",
            (formatted_expire_time, username)
        )

        logger.info(f"用户 {username} 充值后的新过期时间: {formatted_expire_time}")

        # 更新卡密状态
        cursor.execute(
            "UPDATE cards SET status = 0, use_time = UNIX_TIMESTAMP(), use_by = %s WHERE id = %s",
            (username, card_id)
        )

        conn.commit()

        # 添加充值日志
        add_log(user_id, username, ip, "充值成功", f"卡密: {card_code}, 增加天数: {days}")

        logger.info(f"用户充值成功: {username}, 卡密: {card_code}, 天数: {days}")
        return {
            "状态": "成功",
            "信息": "充值成功",
            "天数": days,
            "到期时间": formatted_expire_time
        }
    except Exception as e:
        logger.error(f"用户充值失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"充值失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def generate_card(days, count=1):
    """生成卡密"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        cards = []
        current_timestamp = int(time.time())  # 使用Python获取当前时间戳

        for i in range(count):
            # 生成卡密
            card_code = hashlib.md5(f"{time.time()}_{days}_{i}".encode()).hexdigest()

            # 插入卡密
            cursor.execute(
                "INSERT INTO cards (card_no, days, create_time, status) VALUES (%s, %s, %s, 1)",
                (card_code, days, current_timestamp)
            )
            cards.append(card_code)

        conn.commit()

        logger.info(f"生成卡密成功: {count}个, 天数: {days}")
        return {"状态": "成功", "信息": f"生成{count}个卡密成功", "卡密列表": cards}
    except Exception as e:
        logger.error(f"生成卡密失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"生成卡密失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def get_user_list(page=1, page_size=10, username=""):
    """获取用户列表"""
    logger.info(f"开始获取用户列表: page={page}, page_size={page_size}, username={username}")

    conn = get_db_connection()
    if not conn:
        logger.error("数据库连接失败")
        return {"状态": "失败", "信息": "数据库连接失败"}

    try:
        import pymysql
        cursor = conn.cursor(pymysql.cursors.DictCursor)
        logger.info("MySQL cursor创建成功")
    except Exception as e:
        logger.error(f"创建MySQL cursor失败: {str(e)}")
        conn.close()
        return {"状态": "失败", "信息": f"创建数据库游标失败: {str(e)}"}

    try:
        # 构建查询条件
        where_clause = ""
        params = []

        if username:
            where_clause = "WHERE kfm LIKE %s"
            params.append(f"%{username}%")

        # 计算总数
        count_query = f"SELECT COUNT(*) as total FROM list {where_clause}"
        logger.info(f"执行计数查询: {count_query}, 参数: {params}")
        cursor.execute(count_query, params)
        total = cursor.fetchone()["total"]
        logger.info(f"查询到总用户数: {total}")

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        # 获取分页数据
        offset = (page - 1) * page_size
        query_params = params + [offset, page_size]

        # 查询所有字段，并且保留原始字段名和别名
        user_query = f"SELECT kfm, kfm as username, tel as phone, jqm as machine_code, regtime as register_time, logintime as last_login_time, dqtime as expire_time, status, bz as remark, cz, ip, sqcs, tjcs, drtjcs, sqfx, fxcs, drfxcs FROM list {where_clause} ORDER BY regtime DESC LIMIT %s, %s"
        logger.info(f"执行用户查询: {user_query}, 参数: {query_params}")

        cursor.execute(user_query, query_params)
        users = cursor.fetchall()
        logger.info(f"查询到用户数据: {len(users)} 条记录")

        # 确保时间字段使用24小时制格式
        for user in users:
            # 处理注册时间
            if user["register_time"]:
                try:
                    from datetime import datetime
                    # 如果是Unix时间戳，先转换为整数
                    if isinstance(user["register_time"], str) and user["register_time"].isdigit():
                        timestamp = int(user["register_time"])
                        dt = datetime.fromtimestamp(timestamp)
                    # 如果是已格式化的时间字符串
                    elif isinstance(user["register_time"], str):
                        dt = datetime.strptime(user["register_time"], "%Y-%m-%d %H:%M:%S")
                    # 如果是datetime对象
                    else:
                        dt = user["register_time"]
                    # 重新格式化为24小时制
                    user["register_time"] = dt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception as e:
                    logger.warning(f"格式化注册时间失败: {str(e)}")

            # 处理登录时间
            if user["last_login_time"]:
                try:
                    from datetime import datetime
                    # 如果是Unix时间戳，先转换为整数
                    if isinstance(user["last_login_time"], str) and user["last_login_time"].isdigit():
                        timestamp = int(user["last_login_time"])
                        dt = datetime.fromtimestamp(timestamp)
                    # 如果是已格式化的时间字符串
                    elif isinstance(user["last_login_time"], str):
                        dt = datetime.strptime(user["last_login_time"], "%Y-%m-%d %H:%M:%S")
                    # 如果是datetime对象
                    else:
                        dt = user["last_login_time"]
                    # 重新格式化为24小时制
                    user["last_login_time"] = dt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception as e:
                    logger.warning(f"格式化登录时间失败: {str(e)}")

            # 处理到期时间
            if user["expire_time"]:
                try:
                    from datetime import datetime
                    # 如果是Unix时间戳，先转换为整数
                    if isinstance(user["expire_time"], str) and user["expire_time"].isdigit():
                        timestamp = int(user["expire_time"])
                        dt = datetime.fromtimestamp(timestamp)
                    # 如果是已格式化的时间字符串
                    elif isinstance(user["expire_time"], str):
                        dt = datetime.strptime(user["expire_time"], "%Y-%m-%d %H:%M:%S")
                    # 如果是datetime对象
                    else:
                        dt = user["expire_time"]
                    # 重新格式化为24小时制
                    user["expire_time"] = dt.strftime("%Y-%m-%d %H:%M:%S")
                except Exception as e:
                    logger.warning(f"格式化到期时间失败: {str(e)}")

        logger.info(f"获取用户列表成功: 第{page}页, 每页{page_size}条, 共{total}条")
        return {
            "状态": "成功",
            "信息": "获取用户列表成功",
            "数据": {
                "用户列表": users,
                "总数": total,
                "当前页": page,
                "每页条数": page_size,
                "总页数": total_pages
            }
        }
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return {"状态": "失败", "信息": f"获取用户列表失败: {str(e)}"}
    finally:
        try:
            cursor.close()
            conn.close()
            logger.info("数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {str(e)}")

def get_card_list(page=1, page_size=10, status=None):
    """获取卡密列表"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor(pymysql.cursors.DictCursor)

    try:
        # 构建查询条件
        where_clause = ""
        params = []

        if status is not None:
            where_clause = "WHERE status = %s"
            params.append(status)

        # 计算总数
        cursor.execute(f"SELECT COUNT(*) as total FROM cards {where_clause}", params)
        total = cursor.fetchone()["total"]

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        # 获取分页数据
        offset = (page - 1) * page_size
        query_params = params + [offset, page_size]
        cursor.execute(
            f"""
            SELECT c.id, c.card_no as card_code, c.days, c.status, c.create_time, c.use_time, c.use_by as user_id, c.use_by as username
            FROM cards c
            {where_clause}
            ORDER BY c.id DESC
            LIMIT %s, %s
            """,
            query_params
        )
        cards = cursor.fetchall()

        # 日期时间已经是字符串形式，不需要格式化

        logger.info(f"获取卡密列表成功: 第{page}页, 每页{page_size}条, 共{total}条")
        return {
            "状态": "成功",
            "信息": "获取卡密列表成功",
            "数据": {
                "卡密列表": cards,
                "总数": total,
                "当前页": page,
                "每页条数": page_size,
                "总页数": total_pages
            }
        }
    except Exception as e:
        logger.error(f"获取卡密列表失败: {str(e)}")
        return {"状态": "失败", "信息": f"获取卡密列表失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def get_log_list(page=1, page_size=10, username=None):
    """获取日志列表，优先从本地SQLite数据库获取，如果失败则从远程MySQL数据库获取"""
    # 先尝试从本地SQLite数据库获取日志
    sqlite_logs = get_logs_from_sqlite(page, page_size, username)
    if sqlite_logs.get("状态") == "成功":
        return sqlite_logs

    # 如果从本地SQLite数据库获取失败，则从远程MySQL数据库获取
    logger.warning("从本地SQLite数据库获取日志失败，尝试从远程MySQL数据库获取")
    return get_logs_from_mysql(page, page_size, username)

def get_logs_from_sqlite(page=1, page_size=10, username=None):
    """从本地SQLite数据库获取日志列表"""
    conn = get_sqlite_connection()
    if not conn:
        return {"状态": "失败", "信息": "本地SQLite数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 构建查询条件
        where_clause = ""
        params = []

        if username:
            where_clause = "WHERE username = ?"
            params.append(username)

        # 计算总数
        cursor.execute(f"SELECT COUNT(*) as total FROM logs {where_clause}", params)
        total = cursor.fetchone()[0]  # SQLite不支持DictCursor

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        # 获取分页数据
        offset = (page - 1) * page_size
        query = f"""
            SELECT id, username, ip, action, details, time
            FROM logs
            {where_clause}
            ORDER BY id DESC
            LIMIT ? OFFSET ?
            """
        cursor.execute(query, params + [page_size, offset])

        # 获取列名
        column_names = [description[0] for description in cursor.description]

        # 将结果转换为字典列表
        logs = []
        for row in cursor.fetchall():
            log_dict = {}
            for i, column in enumerate(column_names):
                # 确保值是字符串或数字，避免复杂对象
                value = row[i]
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                log_dict[column] = value
            logs.append(log_dict)

        logger.info(f"从本地SQLite数据库获取日志列表成功: 第{page}页, 每页{page_size}条, 共{total}条")
        return {
            "状态": "成功",
            "信息": "获取日志列表成功",
            "数据": {
                "日志列表": logs,
                "总数": total,
                "当前页": page,
                "每页条数": page_size,
                "总页数": total_pages
            }
        }
    except Exception as e:
        logger.error(f"从本地SQLite数据库获取日志列表失败: {str(e)}")
        return {"状态": "失败", "信息": f"从本地SQLite数据库获取日志列表失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def get_logs_from_mysql(page=1, page_size=10, username=None):
    """从远程MySQL数据库获取日志列表"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "远程MySQL数据库连接失败"}

    cursor = conn.cursor(pymysql.cursors.DictCursor)

    try:
        # 构建查询条件
        where_clause = ""
        params = []

        if username:
            where_clause = "WHERE username = %s"
            params.append(username)

        # 计算总数
        cursor.execute(f"SELECT COUNT(*) as total FROM logs {where_clause}", params)
        total = cursor.fetchone()["total"]

        # 计算总页数
        total_pages = (total + page_size - 1) // page_size

        # 获取分页数据
        offset = (page - 1) * page_size
        query_params = params + [offset, page_size]
        cursor.execute(
            f"""
            SELECT id, username, ip, action, details, time
            FROM logs
            {where_clause}
            ORDER BY id DESC
            LIMIT %s, %s
            """,
            query_params
        )
        raw_logs = cursor.fetchall()

        # 将结果转换为字典列表，确保值是简单类型
        logs = []
        for row in raw_logs:
            log_dict = {}
            for key, value in row.items():
                # 确保值是字符串或数字，避免复杂对象
                if isinstance(value, (dict, list)):
                    value = json.dumps(value, ensure_ascii=False)
                log_dict[key] = value
            logs.append(log_dict)

        logger.info(f"从远程MySQL数据库获取日志列表成功: 第{page}页, 每页{page_size}条, 共{total}条")
        return {
            "状态": "成功",
            "信息": "获取日志列表成功",
            "数据": {
                "日志列表": logs,
                "总数": total,
                "当前页": page,
                "每页条数": page_size,
                "总页数": total_pages
            }
        }
    except Exception as e:
        logger.error(f"从远程MySQL数据库获取日志列表失败: {str(e)}")
        return {"状态": "失败", "信息": f"从远程MySQL数据库获取日志列表失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def update_user(user_id, data):
    """更新用户信息"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 构建更新语句
        update_fields = []
        params = []

        if "password" in data:
            update_fields.append("pwd = %s")
            params.append(data["password"])

        if "phone" in data:
            update_fields.append("tel = %s")
            params.append(data["phone"])

        if "machine_code" in data:
            update_fields.append("jqm = %s")
            params.append(data["machine_code"])

        if "expire_time" in data:
            # 确保到期时间使用24小时制格式
            expire_time = data["expire_time"]
            try:
                from datetime import datetime
                # 尝试将时间字符串转换为Unix时间戳
                dt = datetime.strptime(expire_time, "%Y-%m-%d %H:%M:%S")
                # 转换为Unix时间戳
                timestamp = int(dt.timestamp())
                update_fields.append("dqtime = %s")
                params.append(str(timestamp))
            except Exception as e:
                logger.warning(f"格式化到期时间失败: {str(e)}")
                # 如果格式化失败，仍然使用原始值
                update_fields.append("dqtime = %s")
                params.append(expire_time)

        if "status" in data:
            update_fields.append("status = %s")
            params.append(data["status"])

        if "remark" in data:
            update_fields.append("bz = %s")
            params.append(data["remark"])

        if not update_fields:
            return {"状态": "失败", "信息": "没有要更新的字段"}

        # 执行更新
        params.append(user_id)
        cursor.execute(
            f"UPDATE list SET {', '.join(update_fields)} WHERE kfm = %s",
            params
        )
        conn.commit()

        logger.info(f"更新用户信息成功: ID={user_id}")
        return {"状态": "成功", "信息": "更新用户信息成功"}
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"更新用户信息失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

def delete_user(user_id):
    """删除用户"""
    conn = get_db_connection()
    if not conn:
        return {"状态": "失败", "信息": "数据库连接失败"}

    cursor = conn.cursor()

    try:
        # 先获取用户名，用于日志记录
        cursor.execute("SELECT kfm FROM list WHERE kfm = %s", (user_id,))
        result = cursor.fetchone()
        if not result:
            return {"状态": "失败", "信息": "用户不存在"}

        username = result[0]

        # 删除用户
        cursor.execute("DELETE FROM list WHERE kfm = %s", (user_id,))
        conn.commit()

        logger.info(f"删除用户成功: ID={user_id}, 用户名={username}")
        return {"状态": "成功", "信息": "删除用户成功"}
    except Exception as e:
        logger.error(f"删除用户失败: {str(e)}")
        conn.rollback()
        return {"状态": "失败", "信息": f"删除用户失败: {str(e)}"}
    finally:
        cursor.close()
        conn.close()

# 初始化数据库
if __name__ == "__main__":
    init_database()
