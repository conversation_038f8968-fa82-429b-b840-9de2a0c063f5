# created by tools/loadICU.tcl -- do not edit
namespace eval ::tcl::clock {
    ::msgcat::mcset gv DAYS_OF_WEEK_ABBREV [list \
        "Jed"\
        "Jel"\
        "Je<PERSON>"\
        "Je<PERSON>"\
        "Jerd"\
        "Jeh"\
        "Je<PERSON>"]
    ::msgcat::mcset gv DAYS_OF_WEEK_FULL [list \
        "Jedoon<PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "Je<PERSON><PERSON>"\
        "<PERSON><PERSON><PERSON>"\
        "Jesarn"]
    ::msgcat::mcset gv MONTHS_ABBREV [list \
        "J-guer"\
        "T-arree"\
        "Mayrnt"\
        "Avrril"\
        "<PERSON><PERSON>yn"\
        "M-souree"\
        "J-souree"\
        "Luanistyn"\
        "M-fouyir"\
        "J-fouyir"\
        "M.<PERSON>y"\
        "M.<PERSON>ck"\
        ""]
    ::msgcat::mcset gv MONTHS_FULL [list \
        "Jerrey-geuree"\
        "Toshiaght-arree"\
        "Mayrnt"\
        "<PERSON>ril"\
        "<PERSON><PERSON>yn"\
        "Mean-souree"\
        "Jerrey-souree"\
        "Luanistyn"\
        "Mean-fouyir"\
        "Jerrey-fouyir"\
        "Mee Houney"\
        "Mee ny Nollick"\
        ""]
}
