<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
            text-align: center;
        }
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }
        .log-level {
            font-weight: bold;
        }
        .log-level-info {
            color: #17a2b8;
        }
        .log-level-warning {
            color: #ffc107;
        }
        .log-level-error {
            color: #dc3545;
        }
        .log-level-debug {
            color: #6c757d;
        }
        .log-details {
            max-width: 250px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-align: left;
        }
        .log-filter-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .log-filter-form .form-control {
            max-width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>日志查看</h1>
            <div>
                <button id="refreshBtn" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <a href="/admin" class="btn btn-secondary">返回管理面板</a>
            </div>
        </div>

        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">系统日志</h5>
                <div class="d-flex">
                    <form id="logFilterForm" class="log-filter-form">
                        <input type="text" id="usernameFilter" class="form-control" placeholder="登录账号">
                        <select id="levelFilter" class="form-select">
                            <option value="all">所有级别</option>
                            <option value="info">信息</option>
                            <option value="warning">警告</option>
                            <option value="error">错误</option>
                            <option value="debug">调试</option>
                        </select>
                        <input type="date" id="dateFilter" class="form-control">
                        <button type="submit" class="btn btn-primary">筛选</button>
                        <button type="button" id="clearFilterBtn" class="btn btn-secondary">清除</button>
                    </form>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped table-bordered">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>登录账号</th>
                                <th>IP地址</th>
                                <th>操作</th>
                                <th>详情</th>
                                <th>时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="logsTable">
                            <tr>
                                <td colspan="7" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <nav aria-label="日志分页">
                    <ul class="pagination" id="pagination">
                        <!-- 分页将由JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">日志统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                总日志数量
                                <span class="badge bg-primary rounded-pill" id="totalLogs">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                信息日志
                                <span class="badge bg-info rounded-pill" id="infoLogs">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                警告日志
                                <span class="badge bg-warning rounded-pill" id="warningLogs">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                错误日志
                                <span class="badge bg-danger rounded-pill" id="errorLogs">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                今日日志
                                <span class="badge bg-secondary rounded-pill" id="todayLogs">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">日志操作</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="exportLogsBtn" class="btn btn-outline-primary">
                                <i class="bi bi-download"></i> 导出日志
                            </button>
                            <button id="clearOldLogsBtn" class="btn btn-outline-warning">
                                <i class="bi bi-trash"></i> 清理旧日志
                            </button>
                            <button id="clearAllLogsBtn" class="btn btn-outline-danger">
                                <i class="bi bi-trash-fill"></i> 清空所有日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1" aria-labelledby="logDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logDetailModalLabel">日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">ID</label>
                        <input type="text" class="form-control" id="detailId" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">登录账号</label>
                        <input type="text" class="form-control" id="detailUsername" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="detailIp" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">操作</label>
                        <input type="text" class="form-control" id="detailAction" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">详情</label>
                        <textarea class="form-control" id="detailDetails" rows="5" readonly></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">时间</label>
                        <input type="text" class="form-control" id="detailTime" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        const pageSize = 10;
        let totalPages = 1;
        let filters = {
            username: '',
            level: 'all',
            date: ''
        };

        // 模态框实例
        let logDetailModal;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化模态框
            logDetailModal = new bootstrap.Modal(document.getElementById('logDetailModal'));

            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            currentPage = parseInt(urlParams.get('page')) || 1;

            if (urlParams.get('username')) {
                filters.username = urlParams.get('username');
                document.getElementById('usernameFilter').value = filters.username;
            }

            if (urlParams.get('level')) {
                filters.level = urlParams.get('level');
                document.getElementById('levelFilter').value = filters.level;
            }

            if (urlParams.get('date')) {
                filters.date = urlParams.get('date');
                document.getElementById('dateFilter').value = filters.date;
            }

            // 加载日志
            loadLogs();

            // 加载日志统计
            loadLogStats();

            // 绑定事件
            document.getElementById('logFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });

            document.getElementById('clearFilterBtn').addEventListener('click', clearFilters);
            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadLogs();
                loadLogStats();
            });

            document.getElementById('exportLogsBtn').addEventListener('click', exportLogs);
            document.getElementById('clearOldLogsBtn').addEventListener('click', clearOldLogs);
            document.getElementById('clearAllLogsBtn').addEventListener('click', clearAllLogs);
        });

        function loadLogs() {
            // 构建请求URL
            let url = `/admin/logs?page=${currentPage}&page_size=${pageSize}`;

            if (filters.username) {
                url += `&username=${encodeURIComponent(filters.username)}`;
            }

            if (filters.level !== 'all') {
                url += `&level=${filters.level}`;
            }

            if (filters.date) {
                url += `&date=${filters.date}`;
            }

            // 显示加载中
            document.getElementById('logsTable').innerHTML = '<tr><td colspan="7" class="text-center">加载中...</td></tr>';

            fetch(url)
                .then(response => response.json())
                .then(response => {
                    console.log('原始 API响应:', response);

                    // 尝试将响应解析为JSON
                    let data;
                    if (typeof response === 'string') {
                        try {
                            data = JSON.parse(response);
                        } catch (e) {
                            console.error('响应解析失败:', e);
                            document.getElementById('logsTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">响应解析失败</td></tr>`;
                            return;
                        }
                    } else {
                        data = response;
                    }

                    console.log('API返回数据:', data);

                    if (data.状态 === '成功') {
                        // 检查数据结构
                        if (data.数据 && data.数据.日志列表) {
                            let logs = data.数据.日志列表;
                            console.log('日志数据类型:', typeof logs);

                            // 如果日志列表是字符串，尝试解析
                            if (typeof logs === 'string') {
                                try {
                                    logs = JSON.parse(logs);
                                } catch (e) {
                                    console.error('日志列表解析失败:', e);
                                }
                            }

                            console.log('处理后的日志数据:', logs);
                            totalPages = Math.ceil(data.数据.总数 / pageSize);

                            // 更新表格
                            updateLogsTable(logs);

                            // 更新分页
                            updatePagination();
                        } else {
                            console.error('日志数据结构不正确:', data);
                            document.getElementById('logsTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">日志数据格式不正确</td></tr>`;
                        }
                    } else {
                        document.getElementById('logsTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">${data.信息 || '加载失败'}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('加载日志出错:', error);
                    document.getElementById('logsTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">加载出错: ${error.message}</td></tr>`;
                });
        }

        function updateLogsTable(logs) {
            const tableBody = document.getElementById('logsTable');

            if (!logs || logs.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
                return;
            }

            console.log('原始日志数据:', JSON.stringify(logs, null, 2));

            let html = '';
            logs.forEach(log => {
                // 如果是字符串形式，尝试解析为JSON
                let logData = log;
                if (typeof log === 'string') {
                    try {
                        logData = JSON.parse(log);
                        console.log('解析后的日志数据:', logData);
                    } catch (e) {
                        console.error('日志数据解析失败:', e);
                        // 如果解析失败，尝试将其显示为纯文本
                        logData = {
                            id: '-',
                            username: '-',
                            ip: '-',
                            action: '-',
                            details: log,
                            time: '-'
                        };
                    }
                }

                // 处理日志级别样式
                let levelClass = '';
                if (logData.level === 'info') levelClass = 'log-level-info';
                else if (logData.level === 'warning') levelClass = 'log-level-warning';
                else if (logData.level === 'error') levelClass = 'log-level-error';
                else if (logData.level === 'debug') levelClass = 'log-level-debug';

                // 确保所有字段都有值
                const id = logData.id || '-';
                let username = logData.username || '-';

                // 如果用户名是机器码格式，显示为“未知用户”
                if (username !== '-' && (username.length === 32 || username.length === 64) && /^[0-9a-fA-F]+$/.test(username)) {
                    username = '未知用户';
                }

                const ip = logData.ip || '-';
                const action = logData.action || '-';
                const details = logData.details || '-';
                const time = logData.time || '-';

                html += `
                <tr>
                    <td>${id}</td>
                    <td>${username}</td>
                    <td>${ip}</td>
                    <td class="${levelClass}">${action}</td>
                    <td class="log-details">${details}</td>
                    <td>${time}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-log-btn" data-log-id="${id}">
                            <i class="bi bi-eye"></i>
                        </button>
                    </td>
                </tr>
                `;
            });

            tableBody.innerHTML = html;

            // 绑定查看按钮事件
            document.querySelectorAll('.view-log-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const logId = this.getAttribute('data-log-id');
                    viewLogDetail(logId);
                });
            });
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>`;
            prevLi.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    updateUrlParams();
                    loadLogs();
                }
            });
            pagination.appendChild(prevLi);

            // 页码
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
                pageLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = i;
                    updateUrlParams();
                    loadLogs();
                });
                pagination.appendChild(pageLi);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>`;
            nextLi.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    updateUrlParams();
                    loadLogs();
                }
            });
            pagination.appendChild(nextLi);
        }

        function loadLogStats() {
            fetch('/admin/logs/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const stats = data.数据;
                        document.getElementById('totalLogs').textContent = stats.total || 0;
                        document.getElementById('infoLogs').textContent = stats.info || 0;
                        document.getElementById('warningLogs').textContent = stats.warning || 0;
                        document.getElementById('errorLogs').textContent = stats.error || 0;
                        document.getElementById('todayLogs').textContent = stats.today || 0;
                    }
                })
                .catch(error => {
                    console.error('加载日志统计出错:', error);
                });
        }

        function applyFilters() {
            filters.username = document.getElementById('usernameFilter').value.trim();
            filters.level = document.getElementById('levelFilter').value;
            filters.date = document.getElementById('dateFilter').value;

            currentPage = 1;
            updateUrlParams();
            loadLogs();
        }

        function clearFilters() {
            document.getElementById('usernameFilter').value = '';
            document.getElementById('levelFilter').value = 'all';
            document.getElementById('dateFilter').value = '';

            filters.username = '';
            filters.level = 'all';
            filters.date = '';

            currentPage = 1;
            updateUrlParams();
            loadLogs();
        }

        function updateUrlParams() {
            const url = new URL(window.location.href);
            url.searchParams.set('page', currentPage);

            if (filters.username) {
                url.searchParams.set('username', filters.username);
            } else {
                url.searchParams.delete('username');
            }

            if (filters.level !== 'all') {
                url.searchParams.set('level', filters.level);
            } else {
                url.searchParams.delete('level');
            }

            if (filters.date) {
                url.searchParams.set('date', filters.date);
            } else {
                url.searchParams.delete('date');
            }

            window.history.replaceState({}, '', url);
        }

        function viewLogDetail(logId) {
            fetch(`/admin/logs/${logId}`)
                .then(response => response.json())
                .then(data => {
                    console.log('日志详情数据:', data);

                    if (data.状态 === '成功') {
                        let log = data.数据;

                        // 如果是字符串形式，尝试解析为JSON
                        if (typeof log === 'string') {
                            try {
                                log = JSON.parse(log);
                            } catch (e) {
                                console.error('日志详情数据解析失败:', e);
                            }
                        }

                        document.getElementById('detailId').value = log.id || '';

                        // 处理用户名，如果是机器码格式则显示为“未知用户”
                        let username = log.username || '';
                        if (username && (username.length === 32 || username.length === 64) && /^[0-9a-fA-F]+$/.test(username)) {
                            username = '未知用户';
                        }
                        document.getElementById('detailUsername').value = username;

                        document.getElementById('detailIp').value = log.ip || '';
                        document.getElementById('detailAction').value = log.action || '';
                        document.getElementById('detailDetails').value = log.details || '';
                        document.getElementById('detailTime').value = log.time || '';
                        logDetailModal.show();
                    } else {
                        alert('获取日志详情失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('获取日志详情出错:', error);
                    alert('获取日志详情出错，请重试');
                });
        }

        function exportLogs() {
            // 构建请求URL
            let url = '/admin/logs/export';

            if (filters.username || filters.level !== 'all' || filters.date) {
                url += '?';
                const params = [];

                if (filters.username) {
                    params.push(`username=${encodeURIComponent(filters.username)}`);
                }

                if (filters.level !== 'all') {
                    params.push(`level=${filters.level}`);
                }

                if (filters.date) {
                    params.push(`date=${filters.date}`);
                }

                url += params.join('&');
            }

            // 下载文件
            window.location.href = url;
        }

        function clearOldLogs() {
            if (confirm('确定要清理30天前的日志吗？此操作不可恢复！')) {
                fetch('/admin/logs/clear-old', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert(`成功清理了 ${data.数据.count} 条旧日志`);
                        loadLogs();
                        loadLogStats();
                    } else {
                        alert('清理旧日志失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('清理旧日志出错:', error);
                    alert('清理旧日志出错，请重试');
                });
            }
        }

        function clearAllLogs() {
            if (confirm('确定要清空所有日志吗？此操作不可恢复！')) {
                if (confirm('再次确认：此操作将删除所有日志记录且无法恢复，是否继续？')) {
                    fetch('/admin/logs/clear-all', {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.状态 === '成功') {
                            alert('所有日志已清空');
                            loadLogs();
                            loadLogStats();
                        } else {
                            alert('清空日志失败: ' + data.信息);
                        }
                    })
                    .catch(error => {
                        console.error('清空日志出错:', error);
                        alert('清空日志出错，请重试');
                    });
                }
            }
        }
    </script>
</body>
</html>
