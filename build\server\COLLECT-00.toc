([('AI主播系统服务器.exe',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\server\\AI主播系统服务器.exe',
   'EXECUTABLE'),
  ('python312.dll',
   'D:\\Program Files (x86)\\python32\\python312.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_c.cp312-win32.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_c.cp312-win32.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict.cp312-win32.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_writer.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_parser.cp312-win32.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\_frozenlist.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('gevent\\resolver\\cares.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\cares.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\libuv\\_corecffi.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi.pyd',
   'EXTENSION'),
  ('gevent\\libev\\corecext.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecext.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cqueue.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cqueue.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_clocal.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_clocal.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cevent.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cevent.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_imap.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_imap.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_ident.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_ident.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files (x86)\\python32\\VCRUNTIME140.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files (x86)\\python32\\python3.dll', 'BINARY'),
  ('MSVCP140.dll', 'C:\\windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('LICENSE.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\LICENSE.txt',
   'DATA'),
  ('Lorem ipsum.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\Lorem ipsum.txt',
   'DATA'),
  ('README_使用说明.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\README_使用说明.txt',
   'DATA'),
  ('ai_dialogue_data.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\ai_dialogue_data.json',
   'DATA'),
  ('ai_dialogue_response.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\ai_dialogue_response.txt',
   'DATA'),
  ('config - 副本.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\config - 副本.json',
   'DATA'),
  ('file_version_info.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\file_version_info.txt',
   'DATA'),
  ('gift_id_map.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\gift_id_map.json',
   'DATA'),
  ('keyword_response_pairs.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\keyword_response_pairs.json',
   'DATA'),
  ('readme.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\readme.txt',
   'DATA'),
  ('requirements.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\requirements.txt',
   'DATA'),
  ('saved_password.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\saved_password.txt',
   'DATA'),
  ('saved_username.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\saved_username.txt',
   'DATA'),
  ('server_data\\database\\server.db',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\database\\server.db',
   'DATA'),
  ('server_data\\dialogues\\1.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\dialogues\\1.json',
   'DATA'),
  ('server_data\\dialogues\\kaer.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\dialogues\\kaer.json',
   'DATA'),
  ('server_data\\dialogues\\kaer1.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\dialogues\\kaer1.json',
   'DATA'),
  ('server_data\\dialogues\\测试对话.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\dialogues\\测试对话.json',
   'DATA'),
  ('server_data\\dialogues\\示例对话.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\dialogues\\示例对话.json',
   'DATA'),
  ('server_data\\downloads\\readme.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\downloads\\readme.txt',
   'DATA'),
  ('server_data\\downloads\\test_update.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\downloads\\test_update.py',
   'DATA'),
  ('server_data\\downloads\\version_info.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\downloads\\version_info.txt',
   'DATA'),
  ('server_data\\scripts\\1.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\scripts\\1.txt',
   'DATA'),
  ('server_data\\scripts\\2.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\scripts\\2.txt',
   'DATA'),
  ('server_data\\scripts\\[时间段数据]示例话术.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\scripts\\[时间段数据]示例话术.txt',
   'DATA'),
  ('server_data\\scripts\\示例话术.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\scripts\\示例话术.txt',
   'DATA'),
  ('server_data\\scripts\\随机话术.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\scripts\\随机话术.txt',
   'DATA'),
  ('server_data\\static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\static\\css\\admin.css',
   'DATA'),
  ('server_data\\static\\downloads\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\static\\downloads\\version.json',
   'DATA'),
  ('server_data\\static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\static\\js\\admin.js',
   'DATA'),
  ('server_data\\templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\card_management.html',
   'DATA'),
  ('server_data\\templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\dashboard.html',
   'DATA'),
  ('server_data\\templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\index.html',
   'DATA'),
  ('server_data\\templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\live_status.html',
   'DATA'),
  ('server_data\\templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\log_management.html',
   'DATA'),
  ('server_data\\templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('server_data\\templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\update_management.html',
   'DATA'),
  ('server_data\\templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\templates\\admin\\user_management.html',
   'DATA'),
  ('server_data\\updates\\updates.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_data\\updates\\updates.json',
   'DATA'),
  ('server_update_config.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_update_config.json',
   'DATA'),
  ('server_update_config_修复版.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_update_config_修复版.json',
   'DATA'),
  ('server_update_config_修复版2.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_update_config_修复版2.json',
   'DATA'),
  ('server_update_config_最终版.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_update_config_最终版.json',
   'DATA'),
  ('static\\admin\\api\\updates\\current',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\admin\\api\\updates\\current',
   'DATA'),
  ('static\\api\\check-update',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\api\\check-update',
   'DATA'),
  ('static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\css\\admin.css',
   'DATA'),
  ('static\\downloads\\fast_updates\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\downloads\\fast_updates\\version.json',
   'DATA'),
  ('static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\js\\admin.js',
   'DATA'),
  ('templates\\admin\\api_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\api_management.html',
   'DATA'),
  ('templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\card_management.html',
   'DATA'),
  ('templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\dashboard.html',
   'DATA'),
  ('templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\index.html',
   'DATA'),
  ('templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745245087',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745245087',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745246592',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745246592',
   'DATA'),
  ('templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745243487',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745243487',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745244551',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745244551',
   'DATA'),
  ('templates\\admin\\login.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\login.html',
   'DATA'),
  ('templates\\admin\\logs.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\logs.html',
   'DATA'),
  ('templates\\admin\\recharge.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\recharge.html',
   'DATA'),
  ('templates\\admin\\settings.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\stats.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\stats.html',
   'DATA'),
  ('templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\update_management.html',
   'DATA'),
  ('templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\user_management.html',
   'DATA'),
  ('templates\\update.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\update.html',
   'DATA'),
  ('test.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\test.txt',
   'DATA'),
  ('test_file.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\test_file.txt',
   'DATA'),
  ('update_config.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\update_config.json',
   'DATA'),
  ('update_info.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\update_info.txt',
   'DATA'),
  ('updates - 副本.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\updates - 副本.json',
   'DATA'),
  ('updates.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\updates.json',
   'DATA'),
  ('version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\version.json',
   'DATA'),
  ('version.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\version.txt',
   'DATA'),
  ('version_info.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\version_info.txt',
   'DATA'),
  ('video_match_system_data.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\video_match_system_data.json',
   'DATA'),
  ('更新包说明.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\更新包说明.txt',
   'DATA'),
  ('更新包说明_最终版.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\更新包说明_最终版.txt',
   'DATA'),
  ('服务器更新指南.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\服务器更新指南.txt',
   'DATA'),
  ('服务器更新指南_修复版.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\服务器更新指南_修复版.txt',
   'DATA'),
  ('服务器更新指南_修复版2.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\服务器更新指南_修复版2.txt',
   'DATA'),
  ('服务器更新指南_最终版.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\服务器更新指南_最终版.txt',
   'DATA'),
  ('测试AI对话.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\测试AI对话.json',
   'DATA'),
  ('礼物ID转礼物名称.txt',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\礼物ID转礼物名称.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('gevent-25.5.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\INSTALLER',
   'DATA'),
  ('zope.event-5.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\WHEEL',
   'DATA'),
  ('gevent-25.5.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\RECORD',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\entry_points.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\entry_points.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\LICENSE',
   'DATA'),
  ('cffi-1.17.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\RECORD',
   'DATA'),
  ('zope.interface-7.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\METADATA',
   'DATA'),
  ('zope.event-5.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\WHEEL',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\namespace_packages.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\top_level.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\METADATA',
   'DATA'),
  ('gevent-25.5.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\WHEEL',
   'DATA'),
  ('pycparser-2.22.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\RECORD',
   'DATA'),
  ('zope.event-5.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\RECORD',
   'DATA'),
  ('pycparser-2.22.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\INSTALLER',
   'DATA'),
  ('zope.event-5.0.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\namespace_packages.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\WHEEL',
   'DATA'),
  ('gevent-25.5.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('pycparser-2.22.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('cffi-1.17.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\WHEEL',
   'DATA'),
  ('gevent-25.5.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\INSTALLER',
   'DATA'),
  ('zope.interface-7.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\top_level.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\LICENSE',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\RECORD',
   'DATA'),
  ('zope.event-5.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\METADATA',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\REQUESTED',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\server\\base_library.zip',
   'DATA')],)
