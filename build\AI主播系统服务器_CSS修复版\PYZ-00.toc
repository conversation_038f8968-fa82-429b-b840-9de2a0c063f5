('C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_CSS修复版\\PYZ-00.pyz',
 [('__future__',
   'D:\\Program Files (x86)\\python32\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files (x86)\\python32\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files (x86)\\python32\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Program Files (x86)\\python32\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files (x86)\\python32\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files (x86)\\python32\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyio', 'D:\\Program Files (x86)\\python32\\Lib\\_pyio.py', 'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files (x86)\\python32\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Program Files (x86)\\python32\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files (x86)\\python32\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('aiohappyeyeballs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\__init__.py',
   'PYMODULE'),
  ('aiohappyeyeballs._staggered',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\_staggered.py',
   'PYMODULE'),
  ('aiohappyeyeballs.impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\impl.py',
   'PYMODULE'),
  ('aiohappyeyeballs.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\types.py',
   'PYMODULE'),
  ('aiohappyeyeballs.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohappyeyeballs\\utils.py',
   'PYMODULE'),
  ('aiohttp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\__init__.py',
   'PYMODULE'),
  ('aiohttp._websocket.helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\helpers.py',
   'PYMODULE'),
  ('aiohttp._websocket.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\models.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader.py',
   'PYMODULE'),
  ('aiohttp._websocket.reader_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_py.py',
   'PYMODULE'),
  ('aiohttp._websocket.writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\writer.py',
   'PYMODULE'),
  ('aiohttp.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\abc.py',
   'PYMODULE'),
  ('aiohttp.base_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\base_protocol.py',
   'PYMODULE'),
  ('aiohttp.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\client.py',
   'PYMODULE'),
  ('aiohttp.client_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_exceptions.py',
   'PYMODULE'),
  ('aiohttp.client_middleware_digest_auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middleware_digest_auth.py',
   'PYMODULE'),
  ('aiohttp.client_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_middlewares.py',
   'PYMODULE'),
  ('aiohttp.client_proto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_proto.py',
   'PYMODULE'),
  ('aiohttp.client_reqrep',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_reqrep.py',
   'PYMODULE'),
  ('aiohttp.client_ws',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\client_ws.py',
   'PYMODULE'),
  ('aiohttp.compression_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\compression_utils.py',
   'PYMODULE'),
  ('aiohttp.connector',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\connector.py',
   'PYMODULE'),
  ('aiohttp.cookiejar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\cookiejar.py',
   'PYMODULE'),
  ('aiohttp.formdata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\formdata.py',
   'PYMODULE'),
  ('aiohttp.hdrs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\hdrs.py',
   'PYMODULE'),
  ('aiohttp.helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\helpers.py',
   'PYMODULE'),
  ('aiohttp.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\http.py',
   'PYMODULE'),
  ('aiohttp.http_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_exceptions.py',
   'PYMODULE'),
  ('aiohttp.http_parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_parser.py',
   'PYMODULE'),
  ('aiohttp.http_websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_websocket.py',
   'PYMODULE'),
  ('aiohttp.http_writer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\http_writer.py',
   'PYMODULE'),
  ('aiohttp.log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\log.py',
   'PYMODULE'),
  ('aiohttp.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\multipart.py',
   'PYMODULE'),
  ('aiohttp.payload',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\payload.py',
   'PYMODULE'),
  ('aiohttp.payload_streamer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\payload_streamer.py',
   'PYMODULE'),
  ('aiohttp.resolver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\resolver.py',
   'PYMODULE'),
  ('aiohttp.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\streams.py',
   'PYMODULE'),
  ('aiohttp.tcp_helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\tcp_helpers.py',
   'PYMODULE'),
  ('aiohttp.tracing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\tracing.py',
   'PYMODULE'),
  ('aiohttp.typedefs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\typedefs.py',
   'PYMODULE'),
  ('aiohttp.web',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web.py',
   'PYMODULE'),
  ('aiohttp.web_app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_app.py',
   'PYMODULE'),
  ('aiohttp.web_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_exceptions.py',
   'PYMODULE'),
  ('aiohttp.web_fileresponse',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_fileresponse.py',
   'PYMODULE'),
  ('aiohttp.web_log',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_log.py',
   'PYMODULE'),
  ('aiohttp.web_middlewares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_middlewares.py',
   'PYMODULE'),
  ('aiohttp.web_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_protocol.py',
   'PYMODULE'),
  ('aiohttp.web_request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_request.py',
   'PYMODULE'),
  ('aiohttp.web_response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_response.py',
   'PYMODULE'),
  ('aiohttp.web_routedef',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_routedef.py',
   'PYMODULE'),
  ('aiohttp.web_runner',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_runner.py',
   'PYMODULE'),
  ('aiohttp.web_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_server.py',
   'PYMODULE'),
  ('aiohttp.web_urldispatcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\web_urldispatcher.py',
   'PYMODULE'),
  ('aiohttp.web_ws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\web_ws.py',
   'PYMODULE'),
  ('aiohttp.worker',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\aiohttp\\worker.py',
   'PYMODULE'),
  ('aiosignal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiosignal\\__init__.py',
   'PYMODULE'),
  ('argparse',
   'D:\\Program Files (x86)\\python32\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\Program Files (x86)\\python32\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files (x86)\\python32\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('attr',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\__init__.py',
   'PYMODULE'),
  ('attr._cmp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_cmp.py',
   'PYMODULE'),
  ('attr._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_compat.py',
   'PYMODULE'),
  ('attr._config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_config.py',
   'PYMODULE'),
  ('attr._funcs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_funcs.py',
   'PYMODULE'),
  ('attr._make',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_make.py',
   'PYMODULE'),
  ('attr._next_gen',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\_next_gen.py',
   'PYMODULE'),
  ('attr._version_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attr\\_version_info.py',
   'PYMODULE'),
  ('attr.converters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\converters.py',
   'PYMODULE'),
  ('attr.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\exceptions.py',
   'PYMODULE'),
  ('attr.filters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\filters.py',
   'PYMODULE'),
  ('attr.setters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\setters.py',
   'PYMODULE'),
  ('attr.validators',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\attr\\validators.py',
   'PYMODULE'),
  ('backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files (x86)\\python32\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files (x86)\\python32\\Lib\\bdb.py', 'PYMODULE'),
  ('bidict',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\__init__.py',
   'PYMODULE'),
  ('bidict._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_abc.py',
   'PYMODULE'),
  ('bidict._base',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_base.py',
   'PYMODULE'),
  ('bidict._bidict',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_bidict.py',
   'PYMODULE'),
  ('bidict._dup',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_dup.py',
   'PYMODULE'),
  ('bidict._exc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_exc.py',
   'PYMODULE'),
  ('bidict._frozen',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_frozen.py',
   'PYMODULE'),
  ('bidict._iter',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_iter.py',
   'PYMODULE'),
  ('bidict._orderedbase',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\bidict\\_orderedbase.py',
   'PYMODULE'),
  ('bidict._orderedbidict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\bidict\\_orderedbidict.py',
   'PYMODULE'),
  ('bidict._typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\_typing.py',
   'PYMODULE'),
  ('bidict.metadata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\bidict\\metadata.py',
   'PYMODULE'),
  ('bisect', 'D:\\Program Files (x86)\\python32\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bz2', 'D:\\Program Files (x86)\\python32\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\Program Files (x86)\\python32\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('cmd', 'D:\\Program Files (x86)\\python32\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Program Files (x86)\\python32\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files (x86)\\python32\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files (x86)\\python32\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files (x86)\\python32\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Program Files (x86)\\python32\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files (x86)\\python32\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files (x86)\\python32\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files (x86)\\python32\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files (x86)\\python32\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files (x86)\\python32\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\Program Files (x86)\\python32\\Lib\\datetime.py',
   'PYMODULE'),
  ('db_logger',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\db_logger.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files (x86)\\python32\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files (x86)\\python32\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files (x86)\\python32\\Lib\\dis.py', 'PYMODULE'),
  ('dns',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('doctest', 'D:\\Program Files (x86)\\python32\\Lib\\doctest.py', 'PYMODULE'),
  ('email',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('engineio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\__init__.py',
   'PYMODULE'),
  ('engineio.async_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_client.py',
   'PYMODULE'),
  ('engineio.async_drivers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\__init__.py',
   'PYMODULE'),
  ('engineio.async_drivers.asgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\asgi.py',
   'PYMODULE'),
  ('engineio.async_drivers.tornado',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_drivers\\tornado.py',
   'PYMODULE'),
  ('engineio.async_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_server.py',
   'PYMODULE'),
  ('engineio.async_socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\async_socket.py',
   'PYMODULE'),
  ('engineio.base_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_client.py',
   'PYMODULE'),
  ('engineio.base_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_server.py',
   'PYMODULE'),
  ('engineio.base_socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\base_socket.py',
   'PYMODULE'),
  ('engineio.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\client.py',
   'PYMODULE'),
  ('engineio.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\exceptions.py',
   'PYMODULE'),
  ('engineio.json',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\json.py',
   'PYMODULE'),
  ('engineio.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\middleware.py',
   'PYMODULE'),
  ('engineio.packet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\packet.py',
   'PYMODULE'),
  ('engineio.payload',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\payload.py',
   'PYMODULE'),
  ('engineio.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\server.py',
   'PYMODULE'),
  ('engineio.socket',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\engineio\\socket.py',
   'PYMODULE'),
  ('engineio.static_files',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\engineio\\static_files.py',
   'PYMODULE'),
  ('eventlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\__init__.py',
   'PYMODULE'),
  ('eventlet._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\_version.py',
   'PYMODULE'),
  ('eventlet.convenience',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\convenience.py',
   'PYMODULE'),
  ('eventlet.corolocal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\corolocal.py',
   'PYMODULE'),
  ('eventlet.event',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\event.py',
   'PYMODULE'),
  ('eventlet.green',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.BaseHTTPServer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\BaseHTTPServer.py',
   'PYMODULE'),
  ('eventlet.green.MySQLdb',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\MySQLdb.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.SSL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.crypto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.tsafe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\tsafe.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\OpenSSL\\version.py',
   'PYMODULE'),
  ('eventlet.green.Queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\Queue.py',
   'PYMODULE'),
  ('eventlet.green.SocketServer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\SocketServer.py',
   'PYMODULE'),
  ('eventlet.green._socket_nodns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\_socket_nodns.py',
   'PYMODULE'),
  ('eventlet.green.builtin',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\builtin.py',
   'PYMODULE'),
  ('eventlet.green.os',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\os.py',
   'PYMODULE'),
  ('eventlet.green.select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\select.py',
   'PYMODULE'),
  ('eventlet.green.selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\selectors.py',
   'PYMODULE'),
  ('eventlet.green.socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\socket.py',
   'PYMODULE'),
  ('eventlet.green.ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\ssl.py',
   'PYMODULE'),
  ('eventlet.green.subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\subprocess.py',
   'PYMODULE'),
  ('eventlet.green.thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\thread.py',
   'PYMODULE'),
  ('eventlet.green.threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\threading.py',
   'PYMODULE'),
  ('eventlet.green.time',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\time.py',
   'PYMODULE'),
  ('eventlet.green.zmq',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\green\\zmq.py',
   'PYMODULE'),
  ('eventlet.greenio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\__init__.py',
   'PYMODULE'),
  ('eventlet.greenio.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\base.py',
   'PYMODULE'),
  ('eventlet.greenio.py3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenio\\py3.py',
   'PYMODULE'),
  ('eventlet.greenpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenpool.py',
   'PYMODULE'),
  ('eventlet.greenthread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\greenthread.py',
   'PYMODULE'),
  ('eventlet.hubs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\__init__.py',
   'PYMODULE'),
  ('eventlet.hubs.asyncio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\asyncio.py',
   'PYMODULE'),
  ('eventlet.hubs.hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\hub.py',
   'PYMODULE'),
  ('eventlet.hubs.timer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\hubs\\timer.py',
   'PYMODULE'),
  ('eventlet.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\lock.py',
   'PYMODULE'),
  ('eventlet.patcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\patcher.py',
   'PYMODULE'),
  ('eventlet.queue',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\queue.py',
   'PYMODULE'),
  ('eventlet.semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\semaphore.py',
   'PYMODULE'),
  ('eventlet.support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\__init__.py',
   'PYMODULE'),
  ('eventlet.support.greendns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\greendns.py',
   'PYMODULE'),
  ('eventlet.support.greenlets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\greenlets.py',
   'PYMODULE'),
  ('eventlet.support.psycopg2_patcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\support\\psycopg2_patcher.py',
   'PYMODULE'),
  ('eventlet.timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\eventlet\\timeout.py',
   'PYMODULE'),
  ('eventlet.tpool',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\tpool.py',
   'PYMODULE'),
  ('eventlet.wsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\eventlet\\wsgi.py',
   'PYMODULE'),
  ('fast_download_server',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\fast_download_server.py',
   'PYMODULE'),
  ('fast_update_server',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\fast_update_server.py',
   'PYMODULE'),
  ('flask',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_cors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('flask_socketio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\__init__.py',
   'PYMODULE'),
  ('flask_socketio.namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\namespace.py',
   'PYMODULE'),
  ('flask_socketio.test_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask_socketio\\test_client.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files (x86)\\python32\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\Program Files (x86)\\python32\\Lib\\fractions.py',
   'PYMODULE'),
  ('frozenlist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\__init__.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Program Files (x86)\\python32\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files (x86)\\python32\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files (x86)\\python32\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files (x86)\\python32\\Lib\\gettext.py', 'PYMODULE'),
  ('gevent',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\__init__.py',
   'PYMODULE'),
  ('gevent._abstract_linkable',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_abstract_linkable.py',
   'PYMODULE'),
  ('gevent._compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_compat.py',
   'PYMODULE'),
  ('gevent._config',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_config.py',
   'PYMODULE'),
  ('gevent._ffi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\__init__.py',
   'PYMODULE'),
  ('gevent._ffi.callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\callback.py',
   'PYMODULE'),
  ('gevent._ffi.loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\loop.py',
   'PYMODULE'),
  ('gevent._ffi.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_ffi\\watcher.py',
   'PYMODULE'),
  ('gevent._fileobjectcommon',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_fileobjectcommon.py',
   'PYMODULE'),
  ('gevent._fileobjectposix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_fileobjectposix.py',
   'PYMODULE'),
  ('gevent._greenlet_primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_greenlet_primitives.py',
   'PYMODULE'),
  ('gevent._hub_local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_hub_local.py',
   'PYMODULE'),
  ('gevent._hub_primitives',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_hub_primitives.py',
   'PYMODULE'),
  ('gevent._ident',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_ident.py',
   'PYMODULE'),
  ('gevent._imap',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_imap.py',
   'PYMODULE'),
  ('gevent._interfaces',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_interfaces.py',
   'PYMODULE'),
  ('gevent._monitor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_monitor.py',
   'PYMODULE'),
  ('gevent._patcher',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_patcher.py',
   'PYMODULE'),
  ('gevent._semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_semaphore.py',
   'PYMODULE'),
  ('gevent._socket3',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_socket3.py',
   'PYMODULE'),
  ('gevent._socketcommon',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_socketcommon.py',
   'PYMODULE'),
  ('gevent._tblib',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_tblib.py',
   'PYMODULE'),
  ('gevent._threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_threading.py',
   'PYMODULE'),
  ('gevent._tracer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_tracer.py',
   'PYMODULE'),
  ('gevent._util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_util.py',
   'PYMODULE'),
  ('gevent._waiter',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\_waiter.py',
   'PYMODULE'),
  ('gevent.ares',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\ares.py',
   'PYMODULE'),
  ('gevent.backdoor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\backdoor.py',
   'PYMODULE'),
  ('gevent.baseserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\baseserver.py',
   'PYMODULE'),
  ('gevent.builtins',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\builtins.py',
   'PYMODULE'),
  ('gevent.contextvars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\contextvars.py',
   'PYMODULE'),
  ('gevent.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\core.py',
   'PYMODULE'),
  ('gevent.event',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\event.py',
   'PYMODULE'),
  ('gevent.events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\events.py',
   'PYMODULE'),
  ('gevent.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\exceptions.py',
   'PYMODULE'),
  ('gevent.fileobject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\fileobject.py',
   'PYMODULE'),
  ('gevent.greenlet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\greenlet.py',
   'PYMODULE'),
  ('gevent.hub',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\hub.py',
   'PYMODULE'),
  ('gevent.libev',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\__init__.py',
   'PYMODULE'),
  ('gevent.libev._corecffi_build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libev.corecffi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecffi.py',
   'PYMODULE'),
  ('gevent.libev.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\watcher.py',
   'PYMODULE'),
  ('gevent.libuv',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\__init__.py',
   'PYMODULE'),
  ('gevent.libuv._corecffi_build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libuv.loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\loop.py',
   'PYMODULE'),
  ('gevent.libuv.watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\watcher.py',
   'PYMODULE'),
  ('gevent.local',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\local.py',
   'PYMODULE'),
  ('gevent.lock',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\lock.py',
   'PYMODULE'),
  ('gevent.monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\__init__.py',
   'PYMODULE'),
  ('gevent.monkey.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\__main__.py',
   'PYMODULE'),
  ('gevent.monkey._errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_errors.py',
   'PYMODULE'),
  ('gevent.monkey._main',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_main.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_common',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_common.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_gte313',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_gte313.py',
   'PYMODULE'),
  ('gevent.monkey._patch_thread_lt313',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_patch_thread_lt313.py',
   'PYMODULE'),
  ('gevent.monkey._state',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_state.py',
   'PYMODULE'),
  ('gevent.monkey._util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\_util.py',
   'PYMODULE'),
  ('gevent.monkey.api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\monkey\\api.py',
   'PYMODULE'),
  ('gevent.os',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\os.py',
   'PYMODULE'),
  ('gevent.pool',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\pool.py',
   'PYMODULE'),
  ('gevent.pywsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\pywsgi.py',
   'PYMODULE'),
  ('gevent.queue',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\queue.py',
   'PYMODULE'),
  ('gevent.resolver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\__init__.py',
   'PYMODULE'),
  ('gevent.resolver._addresses',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\_addresses.py',
   'PYMODULE'),
  ('gevent.resolver._hostsfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\_hostsfile.py',
   'PYMODULE'),
  ('gevent.resolver.ares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\ares.py',
   'PYMODULE'),
  ('gevent.resolver.blocking',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\blocking.py',
   'PYMODULE'),
  ('gevent.resolver.dnspython',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\dnspython.py',
   'PYMODULE'),
  ('gevent.resolver.thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\thread.py',
   'PYMODULE'),
  ('gevent.resolver_ares',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver_ares.py',
   'PYMODULE'),
  ('gevent.resolver_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver_thread.py',
   'PYMODULE'),
  ('gevent.select',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\select.py',
   'PYMODULE'),
  ('gevent.selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\selectors.py',
   'PYMODULE'),
  ('gevent.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\server.py',
   'PYMODULE'),
  ('gevent.signal',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\signal.py',
   'PYMODULE'),
  ('gevent.socket',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\socket.py',
   'PYMODULE'),
  ('gevent.ssl',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\ssl.py',
   'PYMODULE'),
  ('gevent.subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\subprocess.py',
   'PYMODULE'),
  ('gevent.testing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.errorhandler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\errorhandler.py',
   'PYMODULE'),
  ('gevent.testing.exception',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\exception.py',
   'PYMODULE'),
  ('gevent.testing.flaky',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\flaky.py',
   'PYMODULE'),
  ('gevent.testing.hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\hub.py',
   'PYMODULE'),
  ('gevent.testing.leakcheck',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\leakcheck.py',
   'PYMODULE'),
  ('gevent.testing.modules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\modules.py',
   'PYMODULE'),
  ('gevent.testing.monkey_test',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\monkey_test.py',
   'PYMODULE'),
  ('gevent.testing.openfiles',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\openfiles.py',
   'PYMODULE'),
  ('gevent.testing.params',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\params.py',
   'PYMODULE'),
  ('gevent.testing.patched_tests_setup',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\patched_tests_setup.py',
   'PYMODULE'),
  ('gevent.testing.resources',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\resources.py',
   'PYMODULE'),
  ('gevent.testing.six',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\six.py',
   'PYMODULE'),
  ('gevent.testing.skipping',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\skipping.py',
   'PYMODULE'),
  ('gevent.testing.sockets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\sockets.py',
   'PYMODULE'),
  ('gevent.testing.support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\support.py',
   'PYMODULE'),
  ('gevent.testing.switching',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\switching.py',
   'PYMODULE'),
  ('gevent.testing.sysinfo',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\sysinfo.py',
   'PYMODULE'),
  ('gevent.testing.testcase',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\testcase.py',
   'PYMODULE'),
  ('gevent.testing.testrunner',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\testrunner.py',
   'PYMODULE'),
  ('gevent.testing.timing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\timing.py',
   'PYMODULE'),
  ('gevent.testing.travis',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\travis.py',
   'PYMODULE'),
  ('gevent.testing.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\testing\\util.py',
   'PYMODULE'),
  ('gevent.tests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\__main__.py',
   'PYMODULE'),
  ('gevent.tests._blocks_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_blocks_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._import_import_patch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_patch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_import_wait.py',
   'PYMODULE'),
  ('gevent.tests._imports_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._imports_imports_at_top_level',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\_imports_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests.getaddrinfo_module',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\getaddrinfo_module.py',
   'PYMODULE'),
  ('gevent.tests.known_failures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\known_failures.py',
   'PYMODULE'),
  ('gevent.tests.lock_tests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\lock_tests.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\__main__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_with_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_with_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue302monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\issue302monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.script',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\script.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_monkey_patches',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_monkey_patches.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__GreenletExit',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__GreenletExit.py',
   'PYMODULE'),
  ('gevent.tests.test___config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___config.py',
   'PYMODULE'),
  ('gevent.tests.test___ident',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___ident.py',
   'PYMODULE'),
  ('gevent.tests.test___monitor',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___monitor.py',
   'PYMODULE'),
  ('gevent.tests.test___monkey_patching',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test___monkey_patching.py',
   'PYMODULE'),
  ('gevent.tests.test__all__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__all__.py',
   'PYMODULE'),
  ('gevent.tests.test__api',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__api.py',
   'PYMODULE'),
  ('gevent.tests.test__api_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__api_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_host_result',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ares_host_result.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ares_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__backdoor',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__backdoor.py',
   'PYMODULE'),
  ('gevent.tests.test__close_backend_fd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__close_backend_fd.py',
   'PYMODULE'),
  ('gevent.tests.test__compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__compat.py',
   'PYMODULE'),
  ('gevent.tests.test__contextvars',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__contextvars.py',
   'PYMODULE'),
  ('gevent.tests.test__core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core.py',
   'PYMODULE'),
  ('gevent.tests.test__core_async',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_async.py',
   'PYMODULE'),
  ('gevent.tests.test__core_callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__core_fork',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_fork.py',
   'PYMODULE'),
  ('gevent.tests.test__core_loop_run',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_loop_run.py',
   'PYMODULE'),
  ('gevent.tests.test__core_stat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_stat.py',
   'PYMODULE'),
  ('gevent.tests.test__core_timer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_timer.py',
   'PYMODULE'),
  ('gevent.tests.test__core_watcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__core_watcher.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__destroy.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy_default_loop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__destroy_default_loop.py',
   'PYMODULE'),
  ('gevent.tests.test__doctests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__doctests.py',
   'PYMODULE'),
  ('gevent.tests.test__environ',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__environ.py',
   'PYMODULE'),
  ('gevent.tests.test__event',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__event.py',
   'PYMODULE'),
  ('gevent.tests.test__events',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__events.py',
   'PYMODULE'),
  ('gevent.tests.test__example_echoserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_echoserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_portforwarder',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_portforwarder.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_udp_client.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_udp_server.py',
   'PYMODULE'),
  ('gevent.tests.test__example_webproxy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_webproxy.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver_ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__example_wsgiserver_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__examples',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__examples.py',
   'PYMODULE'),
  ('gevent.tests.test__exc_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__exc_info.py',
   'PYMODULE'),
  ('gevent.tests.test__execmodules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__execmodules.py',
   'PYMODULE'),
  ('gevent.tests.test__fileobject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__fileobject.py',
   'PYMODULE'),
  ('gevent.tests.test__getaddrinfo_import',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__getaddrinfo_import.py',
   'PYMODULE'),
  ('gevent.tests.test__greenio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenio.py',
   'PYMODULE'),
  ('gevent.tests.test__greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__greenletset',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenletset.py',
   'PYMODULE'),
  ('gevent.tests.test__greenness',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__greenness.py',
   'PYMODULE'),
  ('gevent.tests.test__hub',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub_join.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__hub_join_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__import_blocking_in_greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__import_blocking_in_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__import_wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__import_wait.py',
   'PYMODULE'),
  ('gevent.tests.test__issue112',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue112.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1686',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue1686.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1864',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue1864.py',
   'PYMODULE'),
  ('gevent.tests.test__issue230',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue230.py',
   'PYMODULE'),
  ('gevent.tests.test__issue330',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue330.py',
   'PYMODULE'),
  ('gevent.tests.test__issue467',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue467.py',
   'PYMODULE'),
  ('gevent.tests.test__issue6',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue6.py',
   'PYMODULE'),
  ('gevent.tests.test__issue600',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue600.py',
   'PYMODULE'),
  ('gevent.tests.test__issue607',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue607.py',
   'PYMODULE'),
  ('gevent.tests.test__issue639',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue639.py',
   'PYMODULE'),
  ('gevent.tests.test__issue_728',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issue_728.py',
   'PYMODULE'),
  ('gevent.tests.test__issues461_471',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__issues461_471.py',
   'PYMODULE'),
  ('gevent.tests.test__iwait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__iwait.py',
   'PYMODULE'),
  ('gevent.tests.test__joinall',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__joinall.py',
   'PYMODULE'),
  ('gevent.tests.test__local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__local.py',
   'PYMODULE'),
  ('gevent.tests.test__lock',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__lock.py',
   'PYMODULE'),
  ('gevent.tests.test__loop_callback',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__loop_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__makefile_ref',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__makefile_ref.py',
   'PYMODULE'),
  ('gevent.tests.test__memleak',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__memleak.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_builtins_future',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_builtins_future.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_hub_in_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_hub_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_logging.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_module_run',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_module_run.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_multiple_imports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_multiple_imports.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_queue.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_select.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning3.py',
   'PYMODULE'),
  ('gevent.tests.test__nondefaultloop',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__nondefaultloop.py',
   'PYMODULE'),
  ('gevent.tests.test__order',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__order.py',
   'PYMODULE'),
  ('gevent.tests.test__os',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__os.py',
   'PYMODULE'),
  ('gevent.tests.test__pool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__pool.py',
   'PYMODULE'),
  ('gevent.tests.test__pywsgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__queue',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__queue.py',
   'PYMODULE'),
  ('gevent.tests.test__real_greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__real_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__refcount.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount_core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__refcount_core.py',
   'PYMODULE'),
  ('gevent.tests.test__resolver_dnspython',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__resolver_dnspython.py',
   'PYMODULE'),
  ('gevent.tests.test__select',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__select.py',
   'PYMODULE'),
  ('gevent.tests.test__selectors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__semaphore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__semaphore.py',
   'PYMODULE'),
  ('gevent.tests.test__server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__server.py',
   'PYMODULE'),
  ('gevent.tests.test__server_pywsgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__server_pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__signal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__signal.py',
   'PYMODULE'),
  ('gevent.tests.test__sleep0',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__sleep0.py',
   'PYMODULE'),
  ('gevent.tests.test__socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_close',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_close.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_dns.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns6',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_dns6.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_errors.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ex',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_ex.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_send_memoryview',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_send_memoryview.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socket_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__socketpair',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__socketpair.py',
   'PYMODULE'),
  ('gevent.tests.test__ssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_interrupted',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess_interrupted.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_poll',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__subprocess_poll.py',
   'PYMODULE'),
  ('gevent.tests.test__systemerror',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__systemerror.py',
   'PYMODULE'),
  ('gevent.tests.test__thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_2',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_2.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_before_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_fork_from_dummy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_fork_from_dummy.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_holding_lock_while_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_holding_lock_while_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_monkey_in_thread',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_monkey_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_native_before_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_native_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_no_monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_patched_local',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_patched_local.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_vs_settrace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threading_vs_settrace.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threadpool.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool_executor_patched',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__threadpool_executor_patched.py',
   'PYMODULE'),
  ('gevent.tests.test__timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\tests\\test__util.py',
   'PYMODULE'),
  ('gevent.thread',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\thread.py',
   'PYMODULE'),
  ('gevent.threading',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\threading.py',
   'PYMODULE'),
  ('gevent.threadpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\threadpool.py',
   'PYMODULE'),
  ('gevent.time',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\time.py',
   'PYMODULE'),
  ('gevent.timeout',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\timeout.py',
   'PYMODULE'),
  ('gevent.util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\gevent\\util.py',
   'PYMODULE'),
  ('gevent.win32util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\win32util.py',
   'PYMODULE'),
  ('glob', 'D:\\Program Files (x86)\\python32\\Lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\Program Files (x86)\\python32\\Lib\\gzip.py', 'PYMODULE'),
  ('h11',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Program Files (x86)\\python32\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files (x86)\\python32\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Program Files (x86)\\python32\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files (x86)\\python32\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files (x86)\\python32\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files (x86)\\python32\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\Program Files (x86)\\python32\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('itsdangerous',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files (x86)\\python32\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\__init__.py',
   'PYMODULE'),
  ('jwt.algorithms',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\algorithms.py',
   'PYMODULE'),
  ('jwt.api_jwk',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwk.py',
   'PYMODULE'),
  ('jwt.api_jws',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jws.py',
   'PYMODULE'),
  ('jwt.api_jwt',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\api_jwt.py',
   'PYMODULE'),
  ('jwt.exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\exceptions.py',
   'PYMODULE'),
  ('jwt.jwk_set_cache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\jwt\\jwk_set_cache.py',
   'PYMODULE'),
  ('jwt.jwks_client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\jwks_client.py',
   'PYMODULE'),
  ('jwt.types',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\types.py',
   'PYMODULE'),
  ('jwt.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\utils.py',
   'PYMODULE'),
  ('jwt.warnings',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\jwt\\warnings.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files (x86)\\python32\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files (x86)\\python32\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files (x86)\\python32\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\Program Files (x86)\\python32\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multidict',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\__init__.py',
   'PYMODULE'),
  ('multidict._abc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\multidict\\_abc.py',
   'PYMODULE'),
  ('multidict._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_compat.py',
   'PYMODULE'),
  ('multidict._multidict_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict_py.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files (x86)\\python32\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Program Files (x86)\\python32\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files (x86)\\python32\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files (x86)\\python32\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files (x86)\\python32\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse',
   'D:\\Program Files (x86)\\python32\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'D:\\Program Files (x86)\\python32\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files (x86)\\python32\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Program Files (x86)\\python32\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Program Files (x86)\\python32\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform',
   'D:\\Program Files (x86)\\python32\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint', 'D:\\Program Files (x86)\\python32\\Lib\\pprint.py', 'PYMODULE'),
  ('profile', 'D:\\Program Files (x86)\\python32\\Lib\\profile.py', 'PYMODULE'),
  ('propcache',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\__init__.py',
   'PYMODULE'),
  ('propcache._helpers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers.py',
   'PYMODULE'),
  ('propcache._helpers_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_py.py',
   'PYMODULE'),
  ('propcache.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\propcache\\api.py',
   'PYMODULE'),
  ('pstats', 'D:\\Program Files (x86)\\python32\\Lib\\pstats.py', 'PYMODULE'),
  ('psutil',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Program Files (x86)\\python32\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files (x86)\\python32\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files (x86)\\python32\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pymysql',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\__init__.py',
   'PYMODULE'),
  ('pymysql._auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\_auth.py',
   'PYMODULE'),
  ('pymysql.charset',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\charset.py',
   'PYMODULE'),
  ('pymysql.connections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\connections.py',
   'PYMODULE'),
  ('pymysql.constants',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\__init__.py',
   'PYMODULE'),
  ('pymysql.constants.CLIENT',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\CLIENT.py',
   'PYMODULE'),
  ('pymysql.constants.COMMAND',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\COMMAND.py',
   'PYMODULE'),
  ('pymysql.constants.CR',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\CR.py',
   'PYMODULE'),
  ('pymysql.constants.ER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\ER.py',
   'PYMODULE'),
  ('pymysql.constants.FIELD_TYPE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\FIELD_TYPE.py',
   'PYMODULE'),
  ('pymysql.constants.SERVER_STATUS',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\constants\\SERVER_STATUS.py',
   'PYMODULE'),
  ('pymysql.converters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\converters.py',
   'PYMODULE'),
  ('pymysql.cursors',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\cursors.py',
   'PYMODULE'),
  ('pymysql.err',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\err.py',
   'PYMODULE'),
  ('pymysql.optionfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\optionfile.py',
   'PYMODULE'),
  ('pymysql.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pymysql\\protocol.py',
   'PYMODULE'),
  ('pymysql.times',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\pymysql\\times.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files (x86)\\python32\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files (x86)\\python32\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Program Files (x86)\\python32\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files (x86)\\python32\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files (x86)\\python32\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Program Files (x86)\\python32\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\Program Files (x86)\\python32\\Lib\\selectors.py',
   'PYMODULE'),
  ('server_patch_upload',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_patch_upload.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files (x86)\\python32\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files (x86)\\python32\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files (x86)\\python32\\Lib\\signal.py', 'PYMODULE'),
  ('simple_websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\__init__.py',
   'PYMODULE'),
  ('simple_websocket.aiows',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\aiows.py',
   'PYMODULE'),
  ('simple_websocket.asgi',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\asgi.py',
   'PYMODULE'),
  ('simple_websocket.errors',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\errors.py',
   'PYMODULE'),
  ('simple_websocket.ws',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\simple_websocket\\ws.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files (x86)\\python32\\Lib\\site.py', 'PYMODULE'),
  ('smtplib', 'D:\\Program Files (x86)\\python32\\Lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files (x86)\\python32\\Lib\\socket.py', 'PYMODULE'),
  ('socketio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\__init__.py',
   'PYMODULE'),
  ('socketio.admin',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\admin.py',
   'PYMODULE'),
  ('socketio.asgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\asgi.py',
   'PYMODULE'),
  ('socketio.async_admin',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_admin.py',
   'PYMODULE'),
  ('socketio.async_aiopika_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_aiopika_manager.py',
   'PYMODULE'),
  ('socketio.async_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_client.py',
   'PYMODULE'),
  ('socketio.async_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_manager.py',
   'PYMODULE'),
  ('socketio.async_namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_namespace.py',
   'PYMODULE'),
  ('socketio.async_pubsub_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_pubsub_manager.py',
   'PYMODULE'),
  ('socketio.async_redis_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_redis_manager.py',
   'PYMODULE'),
  ('socketio.async_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_server.py',
   'PYMODULE'),
  ('socketio.async_simple_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\async_simple_client.py',
   'PYMODULE'),
  ('socketio.base_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_client.py',
   'PYMODULE'),
  ('socketio.base_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_manager.py',
   'PYMODULE'),
  ('socketio.base_namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_namespace.py',
   'PYMODULE'),
  ('socketio.base_server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\base_server.py',
   'PYMODULE'),
  ('socketio.client',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\client.py',
   'PYMODULE'),
  ('socketio.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\exceptions.py',
   'PYMODULE'),
  ('socketio.kafka_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\kafka_manager.py',
   'PYMODULE'),
  ('socketio.kombu_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\kombu_manager.py',
   'PYMODULE'),
  ('socketio.manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\manager.py',
   'PYMODULE'),
  ('socketio.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\middleware.py',
   'PYMODULE'),
  ('socketio.msgpack_packet',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\msgpack_packet.py',
   'PYMODULE'),
  ('socketio.namespace',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\namespace.py',
   'PYMODULE'),
  ('socketio.packet',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\packet.py',
   'PYMODULE'),
  ('socketio.pubsub_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\pubsub_manager.py',
   'PYMODULE'),
  ('socketio.redis_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\redis_manager.py',
   'PYMODULE'),
  ('socketio.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\socketio\\server.py',
   'PYMODULE'),
  ('socketio.simple_client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\simple_client.py',
   'PYMODULE'),
  ('socketio.tornado',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\tornado.py',
   'PYMODULE'),
  ('socketio.zmq_manager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\socketio\\zmq_manager.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Program Files (x86)\\python32\\Lib\\socketserver.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Program Files (x86)\\python32\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files (x86)\\python32\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files (x86)\\python32\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files (x86)\\python32\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Program Files (x86)\\python32\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Program Files (x86)\\python32\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Program Files (x86)\\python32\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Program Files (x86)\\python32\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile',
   'D:\\Program Files (x86)\\python32\\Lib\\tempfile.py',
   'PYMODULE'),
  ('test',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\__init__.py',
   'PYMODULE'),
  ('test.libregrtest',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\libregrtest\\__init__.py',
   'PYMODULE'),
  ('test.lock_tests',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\lock_tests.py',
   'PYMODULE'),
  ('test.support',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.import_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\import_helper.py',
   'PYMODULE'),
  ('test.support.os_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\os_helper.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('test.support.threading_helper',
   'D:\\Program Files (x86)\\python32\\Lib\\test\\support\\threading_helper.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\Program Files (x86)\\python32\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\Program Files (x86)\\python32\\Lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Program Files (x86)\\python32\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files (x86)\\python32\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('token', 'D:\\Program Files (x86)\\python32\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\Program Files (x86)\\python32\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Program Files (x86)\\python32\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files (x86)\\python32\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files (x86)\\python32\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Program Files (x86)\\python32\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files (x86)\\python32\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files (x86)\\python32\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('user_manager',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\user_manager.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files (x86)\\python32\\Lib\\uuid.py', 'PYMODULE'),
  ('waitress',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\__init__.py',
   'PYMODULE'),
  ('waitress.adjustments',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\adjustments.py',
   'PYMODULE'),
  ('waitress.buffers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\buffers.py',
   'PYMODULE'),
  ('waitress.channel',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\channel.py',
   'PYMODULE'),
  ('waitress.compat',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\compat.py',
   'PYMODULE'),
  ('waitress.parser',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\parser.py',
   'PYMODULE'),
  ('waitress.proxy_headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\proxy_headers.py',
   'PYMODULE'),
  ('waitress.receiver',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\receiver.py',
   'PYMODULE'),
  ('waitress.rfc7230',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\rfc7230.py',
   'PYMODULE'),
  ('waitress.server',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\server.py',
   'PYMODULE'),
  ('waitress.task',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\waitress\\task.py',
   'PYMODULE'),
  ('waitress.trigger',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\trigger.py',
   'PYMODULE'),
  ('waitress.utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\utilities.py',
   'PYMODULE'),
  ('waitress.wasyncore',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\waitress\\wasyncore.py',
   'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files (x86)\\python32\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('websocket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('websockets',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wsgiref',
   'D:\\Program Files (x86)\\python32\\Lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.validate',
   'D:\\Program Files (x86)\\python32\\Lib\\wsgiref\\validate.py',
   'PYMODULE'),
  ('wsproto',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\__init__.py',
   'PYMODULE'),
  ('wsproto.connection',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\connection.py',
   'PYMODULE'),
  ('wsproto.events',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\wsproto\\events.py',
   'PYMODULE'),
  ('wsproto.extensions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\extensions.py',
   'PYMODULE'),
  ('wsproto.frame_protocol',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\frame_protocol.py',
   'PYMODULE'),
  ('wsproto.handshake',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\handshake.py',
   'PYMODULE'),
  ('wsproto.typing',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\wsproto\\typing.py',
   'PYMODULE'),
  ('wsproto.utilities',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\wsproto\\utilities.py',
   'PYMODULE'),
  ('xml',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files (x86)\\python32\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files (x86)\\python32\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yarl',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\__init__.py',
   'PYMODULE'),
  ('yarl._parse',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_parse.py',
   'PYMODULE'),
  ('yarl._path',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_path.py',
   'PYMODULE'),
  ('yarl._query',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_query.py',
   'PYMODULE'),
  ('yarl._quoters',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoters.py',
   'PYMODULE'),
  ('yarl._quoting',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_quoting.py',
   'PYMODULE'),
  ('yarl._quoting_py',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_py.py',
   'PYMODULE'),
  ('yarl._url',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\yarl\\_url.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Program Files (x86)\\python32\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\Program Files (x86)\\python32\\Lib\\zipimport.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.event',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\event\\__init__.py',
   'PYMODULE'),
  ('zope.interface',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zope.interface.verify',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\verify.py',
   'PYMODULE')])
