
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), gevent.subprocess (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional), psutil (optional), gevent.subprocess (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), test.support (delayed, conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level), eventlet.patcher (delayed, conditional, optional)
missing module named typing_extensions.Buffer - imported by setuptools._vendor.typing_extensions (top-level), setuptools._vendor.wheel.wheelfile (conditional)
missing module named typing_extensions.Literal - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config._validate_pyproject.formats (conditional)
missing module named typing_extensions.Self - imported by setuptools._vendor.typing_extensions (top-level), setuptools.config.expand (conditional), setuptools.config.pyprojecttoml (conditional), setuptools.config._validate_pyproject.error_reporting (conditional)
missing module named typing_extensions.deprecated - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.sysconfig (conditional), setuptools._distutils.command.bdist (conditional)
missing module named typing_extensions.TypeAlias - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.compilers.C.base (conditional), setuptools._reqs (conditional), setuptools.warnings (conditional), setuptools._path (conditional), setuptools._distutils.dist (conditional), setuptools.config.setupcfg (conditional), setuptools.config._apply_pyprojecttoml (conditional), setuptools.dist (conditional), setuptools.command.bdist_egg (conditional), setuptools.compat.py311 (conditional)
missing module named typing_extensions.Unpack - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named typing_extensions.TypeVarTuple - imported by setuptools._vendor.typing_extensions (top-level), setuptools._distutils.util (conditional), setuptools._distutils.compilers.C.base (conditional), setuptools._distutils.cmd (conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by getpass (optional), tty (top-level), werkzeug._reloader (delayed, optional), click._termui_impl (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), gevent.tests.test__issue600 (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), gevent.testing.testrunner (top-level)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), werkzeug.debug (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by code (delayed, conditional, optional), flask.cli (delayed, conditional, optional), rlcompleter (optional), cmd (delayed, conditional, optional), pstats (conditional, optional), site (delayed, optional), websockets.cli (delayed, optional), pdb (delayed, optional)
missing module named _typeshed - imported by werkzeug._internal (conditional), setuptools._distutils.dist (conditional), setuptools.glob (conditional), setuptools.compat.py311 (conditional), click.testing (conditional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by D:\Program Files (x86)\python32\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by eventlet.patcher (optional), eventlet.tpool (optional), gevent._compat (optional), cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named collections.Callable - imported by collections (optional), cffi.api (optional)
missing module named _dummy_thread - imported by cffi.lock (conditional, optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named '_typeshed.wsgi' - imported by werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.datastructures.headers (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional), werkzeug.routing.exceptions (conditional), werkzeug.routing.map (conditional), werkzeug.local (conditional), flask.typing (conditional), flask.ctx (conditional), flask.testing (conditional), flask.cli (conditional), flask.app (conditional)
missing module named 'tornado.websocket' - imported by engineio.async_drivers.tornado (top-level)
missing module named tornado - imported by engineio.async_drivers.tornado (top-level)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks.sync' - imported by websocket._http (optional), websockets.sync.client (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named 'python_socks._errors' - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named socketio.socketio_manage - imported by socketio (optional), flask_socketio (optional)
missing module named aio_pika - imported by socketio.async_aiopika_manager (optional)
missing module named 'aioredis.exceptions' - imported by socketio.async_redis_manager (optional)
missing module named aioredis - imported by socketio.async_redis_manager (optional)
missing module named 'redis.exceptions' - imported by socketio.async_redis_manager (optional)
missing module named redis - imported by socketio.redis_manager (optional), socketio.async_redis_manager (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), eventlet.greenio.base (optional), eventlet.green.OpenSSL.SSL (top-level)
missing module named fcntl - imported by subprocess (optional), eventlet.greenio.base (delayed, optional), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional)
missing module named monotonic - imported by eventlet.hubs.hub (optional), eventlet (optional)
missing module named itimer - imported by eventlet.hubs.hub (conditional, optional)
missing module named psycopg2 - imported by eventlet.support.psycopg2_patcher (top-level)
missing module named kafka - imported by socketio.kafka_manager (optional)
missing module named win32api - imported by gevent.win32util (delayed, optional)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named kombu - imported by socketio.kombu_manager (optional)
missing module named msgpack - imported by socketio.msgpack_packet (top-level)
missing module named 'OpenSSL.SSL' - imported by eventlet.green.OpenSSL.SSL (top-level)
missing module named 'OpenSSL.version' - imported by eventlet.green.OpenSSL.version (top-level)
missing module named 'OpenSSL.tsafe' - imported by eventlet.green.OpenSSL.tsafe (top-level)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional), eventlet.green.OpenSSL.crypto (top-level)
missing module named httplib - imported by gevent.tests.test__socket_ssl (optional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named collections.Mapping - imported by collections (optional), gevent.contextvars (optional), gevent.tests.test__local (optional)
missing module named _import_wait - imported by gevent.tests.test__import_wait (optional)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
missing module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named urllib2 - imported by gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (optional)
excluded module named tkinter - imported by test.support (delayed, conditional, optional)
missing module named __builtin__ - imported by gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named test.libregrtest.ALL_RESOURCES - imported by test.libregrtest (delayed, optional), gevent.testing.resources (delayed, optional)
missing module named objgraph - imported by gevent.testing.leakcheck (optional)
missing module named mock - imported by gevent.testing (optional)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named chardet - imported by requests (optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named nacl - imported by pymysql._auth (delayed, optional)
missing module named 'watchdog.observers' - imported by werkzeug._reloader (delayed)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named win32evtlog - imported by logging.handlers (delayed, optional)
missing module named win32evtlogutil - imported by logging.handlers (delayed, optional)
missing module named geventwebsocket - imported by flask_socketio (delayed, conditional, optional)
missing module named dotenv - imported by flask.cli (delayed, optional)
missing module named asgiref - imported by flask.app (delayed, optional)
missing module named python_socks - imported by websockets.asyncio.client (optional), websockets.sync.client (optional)
missing module named 'python_socks.async_' - imported by websockets.asyncio.client (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional)
