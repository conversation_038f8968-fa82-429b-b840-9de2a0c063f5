#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底修复所有问题的脚本
解决剩余的数据库和导入问题
"""

import os
import re

def fix_all_get_sqlite_connection_issues():
    """修复所有get_sqlite_connection相关问题"""
    print("🔧 修复所有get_sqlite_connection相关问题...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        with open('server.py.final_backup', 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ 已备份原文件")
        
        # 1. 确保在文件开头有正确的导入
        import_pattern = r'(import os\s*\n)'
        if 'from user_manager import get_sqlite_connection' not in content:
            content = re.sub(
                import_pattern,
                r'\1from user_manager import get_sqlite_connection\n',
                content
            )
            print("✅ 添加get_sqlite_connection导入")
        
        # 2. 修复API配置初始化函数中的问题
        old_api_init = r'def init_api_config_database\(\):\s*"""初始化API配置数据库表"""\s*try:\s*conn = get_sqlite_connection\(\)'
        new_api_init = '''def init_api_config_database():
    """初始化API配置数据库表"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()'''
        
        content = re.sub(old_api_init, new_api_init, content, flags=re.MULTILINE | re.DOTALL)
        
        # 3. 修复所有使用get_sqlite_connection的地方，确保都有导入
        functions_to_fix = [
            'get_api_config',
            'get_api_config_by_path',
            'register_dynamic_routes'
        ]
        
        for func_name in functions_to_fix:
            # 在函数开头添加导入
            func_pattern = rf'(def {func_name}\([^)]*\):[^{{]*?try:)'
            replacement = r'\1\n        from user_manager import get_sqlite_connection'
            content = re.sub(func_pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        # 4. 修复所有直接使用sqlite3.connect的地方
        content = re.sub(
            r'import sqlite3\s*\n\s*conn = sqlite3\.connect\([^)]+\)',
            'from user_manager import get_sqlite_connection\n        conn = get_sqlite_connection()',
            content,
            flags=re.MULTILINE
        )
        
        content = re.sub(
            r'sqlite3\.connect\([^)]+\)',
            'get_sqlite_connection()',
            content
        )
        
        # 5. 在所有使用get_sqlite_connection的地方前添加try-except导入
        sqlite_usage_pattern = r'(\s+)(conn = get_sqlite_connection\(\))'
        replacement = r'\1try:\n\1    from user_manager import get_sqlite_connection\n\1except ImportError:\n\1    import sqlite3\n\1    def get_sqlite_connection():\n\1        return sqlite3.connect("server_data.db")\n\1\2'
        
        # 只在第一次出现时添加
        if content.count('conn = get_sqlite_connection()') > 0 and 'except ImportError:' not in content:
            content = re.sub(sqlite_usage_pattern, replacement, content, count=1)
            print("✅ 添加了get_sqlite_connection的备用实现")
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 所有get_sqlite_connection问题修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复get_sqlite_connection问题失败: {str(e)}")
        return False

def create_comprehensive_database_fix():
    """创建全面的数据库修复脚本"""
    print("\n🔧 创建全面的数据库修复脚本...")
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面的数据库修复脚本
修复所有数据库相关问题
"""

import os
import sys
import sqlite3
import logging
import glob

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def find_database_files():
    """查找所有可能的数据库文件"""
    possible_paths = []
    
    # 当前目录
    current_dir = os.getcwd()
    possible_paths.append(os.path.join(current_dir, "server_data.db"))
    
    # 打包环境的临时目录
    if hasattr(sys, '_MEIPASS'):
        base_path = sys._MEIPASS
        possible_paths.append(os.path.join(base_path, "server_data.db"))
    
    # 在临时目录中查找
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        mei_patterns = [
            os.path.join(temp_dir, "_MEI*", "server_data.db"),
            os.path.join(temp_dir, "_MEI*", "_internal", "server_data.db")
        ]
        for pattern in mei_patterns:
            matches = glob.glob(pattern)
            possible_paths.extend(matches)
    
    # 返回存在的数据库文件
    existing_dbs = []
    for path in possible_paths:
        if os.path.exists(path):
            existing_dbs.append(path)
    
    return existing_dbs

def fix_database_structure(db_path):
    """修复单个数据库的结构"""
    logger.info(f"修复数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 1. 创建client_updates表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 2. 检查现有表结构并添加缺失的列
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0",
            "is_folder_update": "INTEGER DEFAULT 0",
            "file_path": "TEXT",
            "file_size": "INTEGER",
            "file_hash": "TEXT",
            "download_count": "INTEGER DEFAULT 0",
            "status": "TEXT DEFAULT 'pending'"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"✅ 添加列: {column}")
                    print(f"✅ 添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
        
        # 3. 创建其他必要的表
        tables = {
            "api_configs": """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "logs": """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "live_status": """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """,
            "tokens": """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """,
            "danmaku_records": """
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                sender TEXT,
                content TEXT NOT NULL,
                timestamp TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """
        }
        
        for table_name, create_sql in tables.items():
            cursor.execute(create_sql)
            logger.info(f"✅ 确保表存在: {table_name}")
            print(f"✅ 确保表存在: {table_name}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"数据库修复完成: {db_path}")
        print(f"✅ 数据库修复完成: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        print(f"❌ 数据库修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始全面修复数据库...")
    print("=" * 60)
    
    # 查找所有数据库文件
    db_files = find_database_files()
    
    if not db_files:
        print("⚠️  未找到数据库文件，创建新的数据库...")
        # 创建新的数据库
        db_path = os.path.join(os.getcwd(), "server_data.db")
        success = fix_database_structure(db_path)
    else:
        print(f"📁 找到 {len(db_files)} 个数据库文件:")
        for db_file in db_files:
            print(f"  - {db_file}")
        
        success = True
        for db_file in db_files:
            if not fix_database_structure(db_file):
                success = False
    
    print("=" * 60)
    if success:
        print("🎉 数据库修复完成！")
        print("💡 所有已知的数据库问题都已修复")
        print("💡 现在可以正常运行服务器了")
    else:
        print("❌ 数据库修复失败！")
        print("💡 请检查错误信息并重试")
    
    input("\\n按回车键退出...")

if __name__ == "__main__":
    main()
'''
    
    with open('fix_all_database_issues.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 已创建全面的数据库修复脚本: fix_all_database_issues.py")
    return True

def main():
    """主函数"""
    print("🚀 彻底修复所有问题")
    print("=" * 60)
    
    try:
        # 1. 修复所有get_sqlite_connection相关问题
        if not fix_all_get_sqlite_connection_issues():
            return False
        
        # 2. 创建全面的数据库修复脚本
        if not create_comprehensive_database_fix():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 彻底修复完成！")
        print("\n📝 修复内容:")
        print("✅ 修复了所有get_sqlite_connection导入问题")
        print("✅ 添加了备用数据库连接实现")
        print("✅ 修复了API配置初始化问题")
        print("✅ 创建了全面的数据库修复脚本")
        
        print("\n💡 下一步:")
        print("1. 重新打包程序")
        print("2. 运行 fix_all_database_issues.py 修复数据库")
        print("3. 全面测试所有功能")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
