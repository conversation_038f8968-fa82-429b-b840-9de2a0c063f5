<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新管理 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .update-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .update-form {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .form-group.full-width {
            grid-column: span 2;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary:hover {
            background-color: #45a049;
        }
        .update-history {
            margin-top: 30px;
        }
        .update-history h3 {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .update-item {
            border-left: 3px solid #4CAF50;
            padding: 10px 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .update-item h4 {
            margin: 0 0 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .update-item p {
            margin: 5px 0;
        }
        .version-tag {
            background-color: #4CAF50;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .update-actions {
            margin-top: 10px;
        }
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        .btn-danger {
            background-color: #f44336;
        }
        .btn-danger:hover {
            background-color: #d32f2f;
        }
        .current-version {
            background-color: #2196F3;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 20px;
        }
        .file-upload-container {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .file-upload-container.highlight {
            border-color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
        }
        .file-upload-container input[type="file"] {
            display: none;
        }
        .file-upload-container label {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
            margin-top: 15px;
        }
        .file-upload-container label:hover {
            background-color: #45a049;
        }
        .upload-progress {
            margin-top: 20px;
        }
        .progress-bar {
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        .progress-bar-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .progress-text {
            text-align: center;
            font-size: 14px;
        }
        .alert {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .alert-success {
            background-color: #dff0d8;
            border: 1px solid #d6e9c6;
            color: #3c763d;
        }
        .alert-danger {
            background-color: #f2dede;
            border: 1px solid #ebccd1;
            color: #a94442;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management" class="active"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>
    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>更新管理</h2>
            </div>
            <div class="card-body">
                <div class="current-version-container">
                    <p>当前版本: <span id="current-version">加载中...</span></p>
                </div>

                <div class="tabs">
                    <div class="tab active" data-tab="updates-list">更新列表</div>
                    <div class="tab" data-tab="add-update">添加更新</div>
                    <div class="tab" data-tab="upload-package">上传更新包</div>
                </div>

                <!-- 更新列表 -->
                <div class="tab-content active" id="updates-list">
                    <div class="update-list" id="update-list">
                        <p>加载中...</p>
                    </div>
                </div>

                <!-- 添加更新 -->
                <div class="tab-content" id="add-update">
                    <div class="update-form">
                        <h3>添加新版本</h3>
                        <form id="update-form" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="version">版本号</label>
                                <input type="text" id="version" name="version" class="form-control" placeholder="例如: 1.1.0" required>
                            </div>
                            <div class="form-group">
                                <label for="release_date">发布日期</label>
                                <input type="date" id="release_date" name="release_date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="description">更新内容</label>
                                <textarea id="description" name="description" class="form-control" placeholder="每行一个更新内容，例如:&#10;1. 修复了xxx问题&#10;2. 优化了xxx功能" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="update_file">更新文件</label>
                                <input type="file" id="update_file" name="update_file" class="form-control" accept=".zip">
                                <small style="display: block; margin-top: 5px; color: #666;">上传ZIP格式的更新包，或者在下方提供下载URL</small>
                            </div>
                            <div class="form-group">
                                <label for="download_url">下载地址</label>
                                <input type="text" id="download_url" name="download_url" class="form-control" placeholder="例如: /static/downloads/update_1.1.0.zip">
                                <small style="display: block; margin-top: 5px; color: #666;">如果不上传文件，请提供完整的下载URL</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="force_update" name="force_update"> 强制更新
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，用户必须更新才能继续使用软件</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="set_current" name="set_current"> 设为当前版本
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，此版本将被设置为当前版本</small>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存</button>
                                <button type="button" id="cancel-update-btn" class="btn btn-secondary">取消</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 上传更新包 -->
                <div class="tab-content" id="upload-package">
                    <div class="file-upload-container" id="drop-area">
                        <p>拖放更新包文件到这里，或者点击下方按钮选择文件或文件夹</p>
                        <p style="font-size: 14px; color: #666;">支持 .zip 和 .exe 格式的更新包</p>
                        <div style="display: flex; justify-content: center; gap: 10px;">
                            <div>
                                <input type="file" id="file-upload" accept=".zip,.exe" style="display: none;">
                                <label for="file-upload" class="btn btn-primary">选择文件</label>
                            </div>
                            <div>
                                <input type="file" id="folder-upload" webkitdirectory directory multiple style="display: none;">
                                <label for="folder-upload" class="btn btn-success">选择文件夹</label>
                            </div>
                        </div>
                        <div class="upload-progress" id="upload-progress" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" id="progress-bar-fill"></div>
                            </div>
                            <div class="progress-text" id="progress-text">0%</div>
                        </div>
                    </div>
                    <div id="upload-result" style="margin-top: 20px;"></div>

                    <!-- 上传成功后显示的表单 -->
                    <div id="update-info-form" style="display: none; margin-top: 20px;">
                        <h3>填写更新信息</h3>
                        <div class="alert alert-info">
                            <p><strong>注意:</strong> 更新包已经创建为ZIP文件，客户端会自动下载并解压更新文件。</p>
                            <p>请填写以下信息，完成更新发布流程。</p>
                        </div>
                        <form id="uploaded-update-form">
                            <input type="hidden" id="uploaded-file-url" name="download_url">
                            <div class="form-group">
                                <label for="uploaded-version">版本号</label>
                                <input type="text" id="uploaded-version" name="version" class="form-control" placeholder="例如: 1.1.0" required>
                            </div>
                            <div class="form-group">
                                <label for="uploaded-release-date">发布日期</label>
                                <input type="date" id="uploaded-release-date" name="release_date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="uploaded-description">更新内容</label>
                                <textarea id="uploaded-description" name="description" class="form-control" rows="5" required>1. 新版本发布
2. 优化了更新功能
3. 修复了已知问题
4. 提升了系统稳定性</textarea>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="uploaded-force-update" name="force_update"> 强制更新
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，用户必须更新才能继续使用软件</small>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="uploaded-set-current" name="set_current" checked> 设为当前版本
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，此版本将成为客户端检查更新时获取的版本</small>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">发布更新</button>
                                <button type="button" id="cancel-uploaded-update-btn" class="btn btn-secondary">取消</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 设置默认日期为今天
            if (document.getElementById('release_date')) {
                document.getElementById('release_date').valueAsDate = new Date();
            }
            if (document.getElementById('uploaded-release-date')) {
                document.getElementById('uploaded-release-date').valueAsDate = new Date();
            }

            // 加载更新历史
            loadUpdateHistory();

            // 加载当前版本
            loadCurrentVersion();

            // 表单提交处理
            if (document.getElementById('update-form')) {
                document.getElementById('update-form').addEventListener('submit', function(e) {
                    e.preventDefault();
                    publishUpdate();
                });
            }

            // 上传后的表单提交处理
            if (document.getElementById('uploaded-update-form')) {
                document.getElementById('uploaded-update-form').addEventListener('submit', function(e) {
                    e.preventDefault();
                    publishUploadedUpdate();
                });
            }

            // 取消上传更新
            if (document.getElementById('cancel-uploaded-update-btn')) {
                document.getElementById('cancel-uploaded-update-btn').addEventListener('click', function() {
                    document.getElementById('update-info-form').style.display = 'none';
                    document.getElementById('drop-area').style.display = 'block';
                    document.getElementById('upload-result').innerHTML = '';
                });
            }

            // 文件上传处理
            setupFileUpload();

            // 标签切换处理
            setupTabs();

            // 退出登录
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        });

        // 加载更新历史
        function loadUpdateHistory() {
            fetch('/admin/api/updates')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        displayUpdateHistory(data.数据);
                    } else {
                        showError('加载更新历史失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    showError('加载更新历史出错: ' + error);
                });
        }

        // 全局变量，保存当前版本号
        let currentVersion = '1.0.0';

        // 加载当前版本
        function loadCurrentVersion() {
            fetch('/admin/api/updates/current')
                .then(response => response.json())
                .then(data => {
                    console.log('获取到当前版本信息:', data);

                    // 检查响应格式，支持两种格式：
                    // 1. 直接包含version字段的对象
                    // 2. 包含状态和数据字段的对象，其中数据字段包含version
                    if (data && data.version) {
                        // 直接格式
                        currentVersion = data.version;
                        document.getElementById('current-version').textContent = currentVersion;
                        console.log('当前版本(直接格式):', currentVersion);
                    } else if (data && data.状态 === '成功' && data.数据 && data.数据.version) {
                        // 嵌套格式
                        currentVersion = data.数据.version;
                        document.getElementById('current-version').textContent = currentVersion;
                        console.log('当前版本(嵌套格式):', currentVersion);
                    } else {
                        document.getElementById('current-version').textContent = '未发布';
                        currentVersion = '1.0.0';
                        console.log('未找到版本信息，使用默认版本:', currentVersion);
                    }

                    // 设置上传表单中的默认版本号为下一个版本
                    setNextVersionNumber();
                })
                .catch(error => {
                    document.getElementById('current-version').textContent = '加载失败';
                    console.error('加载当前版本出错:', error);
                    currentVersion = '1.0.0';
                });
        }

        // 计算下一个版本号
        function getNextVersion(version) {
            // 解析版本号
            const parts = version.split('.');

            // 如果是标准的三段式版本号 (x.y.z)
            if (parts.length === 3) {
                // 增加最后一个数字
                let patch = parseInt(parts[2]) + 1;
                return `${parts[0]}.${parts[1]}.${patch}`;
            }
            // 如果是两段式版本号 (x.y)
            else if (parts.length === 2) {
                // 增加最后一个数字
                let minor = parseInt(parts[1]) + 1;
                return `${parts[0]}.${minor}`;
            }
            // 如果是单段式版本号或其他格式
            else {
                // 尝试将最后一个数字加1
                const match = version.match(/(\d+)$/);
                if (match) {
                    const num = parseInt(match[1]) + 1;
                    return version.replace(/\d+$/, num);
                }
                // 如果无法解析，返回原版本号加.1
                return version + '.1';
            }
        }

        // 设置下一个版本号到表单中
        function setNextVersionNumber() {
            const nextVersion = getNextVersion(currentVersion);

            // 设置上传表单中的版本号
            if (document.getElementById('uploaded-version')) {
                document.getElementById('uploaded-version').value = nextVersion;
            }

            // 设置添加更新表单中的版本号
            if (document.getElementById('version')) {
                document.getElementById('version').value = nextVersion;
            }
        }

        // 显示更新历史
        function displayUpdateHistory(updates) {
            const updateList = document.getElementById('update-list');
            updateList.innerHTML = '';

            if (updates.length === 0) {
                updateList.innerHTML = '<p>暂无更新历史</p>';
                return;
            }

            updates.forEach(update => {
                const updateItem = document.createElement('div');
                updateItem.className = 'update-item';

                const header = document.createElement('h4');
                header.innerHTML = `
                    版本 ${update.version}
                    <span class="version-tag">${update.is_current ? '当前版本' : '历史版本'}</span>
                `;

                const releaseDate = document.createElement('p');
                releaseDate.textContent = `发布日期: ${update.release_date}`;

                const forceUpdate = document.createElement('p');
                forceUpdate.textContent = `强制更新: ${update.force_update ? '是' : '否'}`;

                const description = document.createElement('p');
                description.textContent = `更新说明: ${update.description}`;

                const downloadLink = document.createElement('div');
                downloadLink.innerHTML = `
                    <p>下载链接: <span class="download-url">${update.download_url}</span> <button class="btn-sm btn-link" onclick="window.open('${update.download_url}', '_blank')">打开</button></p>
                    ${update.fast_download_url ? `<p>快速下载链接: <span class="download-url">${update.fast_download_url}</span> <button class="btn-sm btn-link" onclick="window.open('${update.fast_download_url}', '_blank')">打开</button></p>` : ''}
                `;

                const actions = document.createElement('div');
                actions.className = 'update-actions';

                if (update.is_current) {
                    // 当前版本不能删除
                    actions.innerHTML = `
                        <button class="btn-primary btn-sm" onclick="setAsCurrent('${update.id}')">已是当前版本</button>
                    `;
                } else {
                    actions.innerHTML = `
                        <button class="btn-primary btn-sm" onclick="setAsCurrent('${update.id}')">设为当前版本</button>
                        <button class="btn-danger btn-sm" onclick="deleteUpdate('${update.id}')">删除</button>
                    `;
                }

                updateItem.appendChild(header);
                updateItem.appendChild(releaseDate);
                updateItem.appendChild(forceUpdate);
                updateItem.appendChild(description);
                updateItem.appendChild(downloadLink);
                updateItem.appendChild(actions);

                updateList.appendChild(updateItem);
            });
        }

        // 发布更新
        function publishUpdate() {
            const form = document.getElementById('update-form');
            const formData = new FormData(form);

            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '发布中...';
            submitBtn.disabled = true;

            fetch('/admin/api/updates', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('更新发布成功!');
                    form.reset();
                    document.getElementById('release_date').valueAsDate = new Date();
                    loadUpdateHistory();
                    loadCurrentVersion();
                } else {
                    showError('发布更新失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('发布更新出错: ' + error);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }

        // 设置为当前版本
        function setAsCurrent(updateId) {
            if (!confirm('确定要将此版本设置为当前版本吗?')) {
                return;
            }

            fetch(`/admin/api/updates/${updateId}/set-current`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('已成功设置为当前版本!');
                    loadUpdateHistory();
                    loadCurrentVersion();
                } else {
                    showError('设置当前版本失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('设置当前版本出错: ' + error);
            });
        }

        // 删除更新
        function deleteUpdate(updateId) {
            if (!confirm('确定要删除此版本吗? 此操作不可恢复!')) {
                return;
            }

            fetch(`/admin/api/updates/${updateId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('版本已成功删除!');
                    loadUpdateHistory();
                } else {
                    showError('删除版本失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('删除版本出错: ' + error);
            });
        }

        // 退出登录
        function logout() {
            fetch('/admin/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    window.location.href = '/admin';
                } else {
                    showError('退出登录失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('退出登录出错: ' + error);
            });
        }

        // 显示错误信息
        function showError(message) {
            alert(message);
            console.error(message);
        }

        // 设置文件上传
        function setupFileUpload() {
            const fileUpload = document.getElementById('file-upload');
            const folderUpload = document.getElementById('folder-upload');
            const dropArea = document.getElementById('drop-area');
            const progressBar = document.getElementById('progress-bar-fill');
            const progressText = document.getElementById('progress-text');
            const uploadProgress = document.getElementById('upload-progress');
            const uploadResult = document.getElementById('upload-result');
            const updateInfoForm = document.getElementById('update-info-form');

            // 存储选择的文件
            let selectedFiles = [];

            // 文件选择处理
            fileUpload.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    // 单文件上传模式
                    const file = this.files[0];
                    uploadFile(file);
                }
            });

            // 文件夹选择处理
            folderUpload.addEventListener('change', function(e) {
                if (this.files.length > 0) {
                    // 多文件上传模式
                    selectedFiles = Array.from(this.files);
                    // 显示选择的文件列表
                    showSelectedFiles();
                    // 自动上传文件夹
                    uploadFolderFiles();
                }
            });

            // 拖放处理
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                dropArea.classList.add('highlight');
            }

            function unhighlight() {
                dropArea.classList.remove('highlight');
            }

            dropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;

                if (files.length === 1) {
                    // 单文件上传模式
                    const file = files[0];
                    uploadFile(file);
                } else if (files.length > 1) {
                    // 多文件上传模式
                    selectedFiles = Array.from(files);
                    showSelectedFiles();
                }
            }

            // 显示选择的文件列表
            function showSelectedFiles() {
                // 清除之前的结果
                uploadResult.innerHTML = '';

                // 创建文件列表容器
                const container = document.createElement('div');
                container.className = 'selected-files-container';
                container.style.marginTop = '20px';
                container.style.maxHeight = '300px';
                container.style.overflowY = 'auto';

                // 添加标题
                const title = document.createElement('h4');
                title.textContent = '已选择的文件';
                container.appendChild(title);

                // 添加文件数量和总大小
                let totalSize = 0;
                selectedFiles.forEach(file => totalSize += file.size);

                const summary = document.createElement('p');
                summary.innerHTML = `共选择了 <strong>${selectedFiles.length}</strong> 个文件，总大小: <strong>${formatFileSize(totalSize)}</strong>`;
                container.appendChild(summary);

                // 添加文件列表
                const fileList = document.createElement('ul');
                fileList.style.listStyle = 'none';
                fileList.style.padding = '0';

                selectedFiles.forEach((file, index) => {
                    const li = document.createElement('li');
                    li.style.marginBottom = '5px';
                    li.style.display = 'flex';
                    li.style.justifyContent = 'space-between';
                    li.style.alignItems = 'center';

                    // 文件信息
                    const fileInfo = document.createElement('div');
                    fileInfo.style.flexGrow = '1';
                    fileInfo.style.display = 'flex';
                    fileInfo.style.justifyContent = 'space-between';
                    fileInfo.style.alignItems = 'center';

                    const fileName = document.createElement('span');
                    fileName.style.wordBreak = 'break-all';
                    fileName.style.marginRight = '10px';
                    fileName.textContent = file.webkitRelativePath || file.name;

                    const fileSize = document.createElement('span');
                    fileSize.textContent = formatFileSize(file.size);

                    fileInfo.appendChild(fileName);
                    fileInfo.appendChild(fileSize);

                    // 删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-danger';
                    deleteBtn.textContent = '×';
                    deleteBtn.style.marginLeft = '10px';
                    deleteBtn.addEventListener('click', function() {
                        selectedFiles.splice(index, 1);
                        showSelectedFiles();
                    });

                    li.appendChild(fileInfo);
                    li.appendChild(deleteBtn);
                    fileList.appendChild(li);
                });

                container.appendChild(fileList);

                // 添加按钮
                const buttonContainer = document.createElement('div');
                buttonContainer.style.marginTop = '15px';
                buttonContainer.style.display = 'flex';
                buttonContainer.style.gap = '10px';

                // 上传按钮
                const uploadBtn = document.createElement('button');
                uploadBtn.className = 'btn btn-primary';
                uploadBtn.textContent = '上传所有文件';
                uploadBtn.addEventListener('click', function() {
                    uploadFolderFiles();
                });

                // 清空按钮
                const clearBtn = document.createElement('button');
                clearBtn.className = 'btn btn-secondary';
                clearBtn.textContent = '清空文件';
                clearBtn.addEventListener('click', function() {
                    selectedFiles = [];
                    showSelectedFiles();
                });

                buttonContainer.appendChild(uploadBtn);
                buttonContainer.appendChild(clearBtn);
                container.appendChild(buttonContainer);

                // 显示容器
                uploadResult.appendChild(container);
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 上传文件夹中的所有文件
            function uploadFolderFiles() {
                if (selectedFiles.length === 0) {
                    showError('请先选择文件');
                    return;
                }

                // 显示进度条
                uploadProgress.style.display = 'block';
                progressBar.style.width = '0%';
                progressText.textContent = '0%';

                // 显示上传状态
                uploadResult.innerHTML = `
                    <div class="alert alert-info">
                        <h4>正在准备上传...</h4>
                        <p>文件数量: ${selectedFiles.length}</p>
                    </div>
                `;

                // 分批上传文件，每批最多50个文件
                const batchSize = 50;
                const totalFiles = selectedFiles.length;
                let uploadedFiles = 0;
                let currentBatch = 0;
                const totalBatches = Math.ceil(totalFiles / batchSize);

                // 创建版本号
                const version = getNextVersion(currentVersion);

                // 上传第一批文件
                uploadBatch(currentBatch, version);

                // 分批上传函数
                function uploadBatch(batchIndex, version) {
                    // 计算当前批次的起始和结束索引
                    const startIndex = batchIndex * batchSize;
                    const endIndex = Math.min(startIndex + batchSize, totalFiles);
                    const batchFiles = selectedFiles.slice(startIndex, endIndex);

                    // 创建FormData对象
                    const formData = new FormData();
                    formData.append('version', version);
                    formData.append('batch_index', batchIndex);
                    formData.append('total_batches', totalBatches);

                    // 添加当前批次的文件
                    batchFiles.forEach(file => {
                        const filePath = file.webkitRelativePath || file.name;
                        formData.append(filePath, file);
                    });

                    // 发送请求
                    const xhr = new XMLHttpRequest();

                    // 监听上传进度
                    xhr.upload.addEventListener('progress', function(e) {
                        if (e.lengthComputable) {
                            // 计算总体进度
                            const batchProgress = e.loaded / e.total;
                            const overallProgress = (batchIndex + batchProgress) / totalBatches;
                            const percentComplete = Math.round(overallProgress * 100);

                            progressBar.style.width = percentComplete + '%';
                            progressText.textContent = percentComplete + '%';

                            // 更新上传状态
                            uploadResult.innerHTML = `
                                <div class="alert alert-info">
                                    <h4>正在上传第 ${batchIndex + 1}/${totalBatches} 批文件...</h4>
                                    <p>已上传: ${uploadedFiles} / ${totalFiles} 个文件</p>
                                </div>
                            `;
                        }
                    });

                    // 监听请求完成
                    xhr.addEventListener('load', function() {
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.状态 === '成功') {
                                    // 更新已上传文件数量
                                    uploadedFiles += batchFiles.length;

                                    // 如果还有下一批文件，继续上传
                                    if (batchIndex + 1 < totalBatches) {
                                        // 上传下一批
                                        uploadBatch(batchIndex + 1, version);
                                    } else {
                                        // 所有文件上传完成
                                        // 清空文件列表
                                        selectedFiles = [];

                                        // 显示成功信息
                                        uploadResult.innerHTML = `
                                            <div class="alert alert-success">
                                                <h4>上传成功</h4>
                                                <p>文件数量: ${totalFiles}</p>
                                                <p>更新包已创建: ${response.数据.unique_filename}</p>
                                                <p><small>注意: 更新包是ZIP格式，客户端会自动解压并更新文件</small></p>
                                            </div>
                                        `;

                                        // 填充更新表单
                                        document.getElementById('uploaded-file-url').value = response.数据.download_url;
                                        document.getElementById('uploaded-version').value = version;

                                        // 显示更新表单
                                        updateInfoForm.style.display = 'block';
                                    }
                                } else {
                                    uploadResult.innerHTML = `
                                        <div class="alert alert-danger">
                                            <h4>上传失败</h4>
                                            <p>${response.信息 || '未知错误'}</p>
                                        </div>
                                    `;
                                }
                            } catch (e) {
                                uploadResult.innerHTML = `
                                    <div class="alert alert-danger">
                                        <h4>解析响应失败</h4>
                                        <p>${e.message}</p>
                                    </div>
                                `;
                            }
                        } else {
                            uploadResult.innerHTML = `
                                <div class="alert alert-danger">
                                    <h4>上传失败</h4>
                                    <p>服务器返回状态码: ${xhr.status}</p>
                                </div>
                            `;
                        }
                    });

                    // 监听错误
                    xhr.addEventListener('error', function() {
                        uploadResult.innerHTML = `
                            <div class="alert alert-danger">
                                <h4>上传失败</h4>
                                <p>网络错误</p>
                            </div>
                        `;
                    });

                    // 发送请求
                    xhr.open('POST', '/admin/api/upload-folder-update');
                    xhr.send(formData);
                }
            }

            // 上传单个文件
            function uploadFile(file) {
                // 检查文件类型
                if (!file.name.endsWith('.zip') && !file.name.endsWith('.exe')) {
                    showError('只能上传ZIP或EXE格式的更新包');
                    return;
                }

                // 显示进度条
                uploadProgress.style.display = 'block';
                progressBar.style.width = '0%';
                progressText.textContent = '0%';

                const formData = new FormData();
                formData.append('file', file);

                // 如果已经填写了更新描述，一并上传
                const description = document.getElementById('uploaded-description');
                if (description && description.value) {
                    formData.append('description', description.value);
                }

                // 如果已经设置了强制更新，一并上传
                const forceUpdate = document.getElementById('uploaded-force-update');
                if (forceUpdate && forceUpdate.checked) {
                    formData.append('force_update', 'true');
                }

                // 添加版本号 - 使用下一个版本号
                const nextVersion = getNextVersion(currentVersion);
                formData.append('version', nextVersion);
                console.log(`使用自动生成的下一个版本号: ${nextVersion}`);

                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/admin/api/upload-update', true);

                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        progressBar.style.width = percentComplete + '%';
                        progressText.textContent = percentComplete + '%';
                    }
                });

                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.状态 === '成功') {
                                // 上传成功
                                uploadResult.innerHTML = `
                                    <div class="alert alert-success">
                                        <p>文件上传成功!</p>
                                        <p>文件名: ${response.数据.filename}</p>
                                        <p>文件大小: ${formatFileSize(response.数据.size)}</p>
                                        ${response.数据.version ? `<p>版本号: ${response.数据.version}</p>` : ''}
                                    </div>
                                `;

                                // 隐藏上传区域，显示更新信息表单
                                dropArea.style.display = 'none';
                                updateInfoForm.style.display = 'block';

                                // 设置下载URL
                                document.getElementById('uploaded-file-url').value = response.数据.download_url;

                                // 如果响应中包含fast_download_url，优先使用它
                                if (response.数据.fast_download_url) {
                                    document.getElementById('uploaded-file-url').value = response.数据.fast_download_url;
                                }

                                // 设置版本号 - 使用服务器返回的版本号
                                if (response.数据.version) {
                                    document.getElementById('uploaded-version').value = response.数据.version;
                                    // 更新当前版本号，以便下次上传时使用正确的下一个版本号
                                    currentVersion = response.数据.version;
                                }

                                // 设置发布日期为今天
                                document.getElementById('uploaded-release-date').valueAsDate = new Date();

                                // 设置更新描述
                                if (response.数据.description) {
                                    document.getElementById('uploaded-description').value = response.数据.description;
                                }

                                // 设置强制更新
                                if (response.数据.force_update) {
                                    document.getElementById('uploaded-force-update').checked = response.数据.force_update;
                                }
                            } else {
                                // 上传失败
                                uploadResult.innerHTML = `
                                    <div class="alert alert-danger">
                                        <p>文件上传失败: ${response.信息}</p>
                                    </div>
                                `;
                            }
                        } catch (e) {
                            uploadResult.innerHTML = `
                                <div class="alert alert-danger">
                                    <p>解析响应出错: ${e.message}</p>
                                </div>
                            `;
                        }
                    } else {
                        uploadResult.innerHTML = `
                            <div class="alert alert-danger">
                                <p>上传失败: HTTP ${xhr.status}</p>
                            </div>
                        `;
                    }

                    // 隐藏进度条
                    uploadProgress.style.display = 'none';
                };

                xhr.onerror = function() {
                    uploadResult.innerHTML = `
                        <div class="alert alert-danger">
                            <p>上传出错，请检查网络连接</p>
                        </div>
                    `;
                    uploadProgress.style.display = 'none';
                };

                xhr.send(formData);
            }

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes < 1024) {
                    return bytes + ' B';
                } else if (bytes < 1024 * 1024) {
                    return (bytes / 1024).toFixed(2) + ' KB';
                } else {
                    return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
                }
            }
        }

        // 设置标签切换
        function setupTabs() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // 移除所有标签的active类
                    tabs.forEach(t => t.classList.remove('active'));
                    // 添加当前标签的active类
                    this.classList.add('active');

                    // 隐藏所有内容
                    tabContents.forEach(content => content.classList.remove('active'));
                    // 显示当前内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }

        // 发布上传的更新
        function publishUploadedUpdate() {
            const form = document.getElementById('uploaded-update-form');
            const formData = new FormData(form);

            // 获取表单数据
            const version = formData.get('version');
            const releaseDate = formData.get('release_date');
            const description = formData.get('description');
            const downloadUrl = formData.get('download_url');
            const forceUpdate = formData.get('force_update') === 'on';
            const setCurrent = formData.get('set_current') === 'on';

            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = '发布中...';
            submitBtn.disabled = true;

            // 发送请求
            fetch('/admin/api/updates/uploaded', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version: version,
                    release_date: releaseDate,
                    description: description,
                    download_url: downloadUrl,
                    force_update: forceUpdate,
                    set_current: setCurrent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('更新发布成功!');
                    form.reset();
                    document.getElementById('uploaded-release-date').valueAsDate = new Date();

                    // 重置上传表单
                    document.getElementById('update-info-form').style.display = 'none';
                    document.getElementById('drop-area').style.display = 'block';
                    document.getElementById('upload-result').innerHTML = '';

                    // 刷新更新列表和当前版本
                    loadUpdateHistory();
                    loadCurrentVersion();

                    // 切换到更新列表标签
                    document.querySelector('.tab[data-tab="updates-list"]').click();
                } else {
                    showError('发布更新失败: ' + data.信息);
                }
            })
            .catch(error => {
                showError('发布更新出错: ' + error);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            });
        }
    </script>
</body>
</html>
