#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的数据库初始化脚本
用于修复打包后的数据库表缺失问题
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        # 尝试多个可能的数据库位置
        possible_paths = [
            os.path.join(base_path, "server_data.db"),
            os.path.join(os.getcwd(), "server_data.db"),
            os.path.join(base_path, "_internal", "server_data.db"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都不存在，使用第一个路径创建新的
        return possible_paths[0]
        
    except Exception:
        return os.path.join(os.getcwd(), "server_data.db")

def init_all_tables():
    """初始化所有必要的数据库表"""
    db_path = get_database_path()
    logger.info(f"初始化数据库: {db_path}")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建API配置表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建日志表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建直播状态表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        # 创建token表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("所有数据库表初始化完成")
        print("✅ 数据库初始化成功！")
        print(f"📁 数据库位置: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始初始化数据库...")
    success = init_all_tables()
    
    if success:
        print("🎉 数据库初始化完成！")
        print("💡 现在可以正常运行服务器了")
    else:
        print("❌ 数据库初始化失败！")
        print("💡 请检查错误信息并重试")
    
    input("按回车键退出...")
