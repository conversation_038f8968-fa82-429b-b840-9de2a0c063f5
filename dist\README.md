# AI主播系统服务器 - 独立版本

## 📦 打包信息
- 打包时间: 2025-06-14 21:59:18
- Python版本: 3.12.10 (tags/v3.12.10:0cc8128, Apr  8 2025, 11:58:42) [MSC v.1943 32 bit (Intel)]
- 打包工具: PyInstaller
- 系统平台: win32

## 🚀 使用方法

### 方法1: 双击启动
直接双击 `AI主播系统服务器.exe` 启动服务器

### 方法2: 使用启动脚本
- Windows: 双击 `启动服务器.bat`
- PowerShell: 右键 `启动服务器.ps1` → "使用PowerShell运行"

### 方法3: 命令行启动
```bash
./AI主播系统服务器.exe
```

## 🌐 访问地址
服务器启动后，访问以下地址：
- 主页: http://localhost:12456
- 管理后台: http://localhost:12456/admin
- 用户管理: http://localhost:12456/admin/user_management

## 🔑 默认登录信息
- 用户名: kaer
- 密码: a13456A

## 📁 文件结构
```
AI主播系统服务器/
├── AI主播系统服务器.exe    # 主程序
├── 启动服务器.bat          # Windows启动脚本
├── 启动服务器.ps1          # PowerShell启动脚本
├── README.md              # 使用说明
└── 更新管理数据库修复.py    # 数据库修复工具
```

## 🔧 故障排除

### 如果遇到数据库错误
运行数据库修复工具：
```bash
python 更新管理数据库修复.py
```

### 如果端口被占用
1. 检查端口占用: `netstat -ano | findstr :12456`
2. 结束占用进程: `taskkill /PID <进程ID> /F`
3. 重新启动服务器

### 如果防火墙阻止
1. 允许程序通过防火墙
2. 或临时关闭防火墙进行测试

## 📊 功能特性
- ✅ 用户管理 (885条用户记录)
- ✅ 卡密管理 (42条卡密记录)
- ✅ 更新管理 (版本控制)
- ✅ 直播状态监控
- ✅ 系统设置
- ✅ API接口管理
- ✅ 文件上传下载
- ✅ 实时日志查看

## 🎯 系统要求
- 操作系统: Windows 7/8/10/11 (64位)
- 内存: 至少 512MB 可用内存
- 磁盘: 至少 100MB 可用空间
- 网络: 需要访问MySQL数据库

## 📞 技术支持
如有问题，请检查：
1. 防火墙设置
2. 端口占用情况
3. 数据库连接
4. 日志文件内容

---
🎉 感谢使用AI主播系统服务器！
