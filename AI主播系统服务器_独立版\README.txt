# AI主播系统服务器 - 独立版

## 🚀 快速开始

### 启动方法
1. 双击 `启动服务器.bat` （推荐）
2. 或直接运行 `AI主播系统服务器.exe`

### 访问地址
- 🌐 管理后台: http://localhost:12456/admin
- 🔌 API接口: http://localhost:12456/api/

### 默认登录
- 👤 用户名: kaer
- 🔑 密码: a13456A

## ✨ 功能特性

- ✅ 无需Python环境，开箱即用
- ✅ 完整的Web管理界面
- ✅ 自定义API接口管理
- ✅ 用户管理和卡密系统
- ✅ 实时WebSocket通信
- ✅ 数据库自动初始化

## 📁 文件说明

- `AI主播系统服务器.exe` - 主程序
- `启动服务器.bat` - 启动脚本
- `templates/` - 网页模板文件
- `static/` - 静态资源文件
- `config.py` - 系统配置文件
- `init_db.py` - 数据库初始化脚本

## ⚠️ 注意事项

1. **首次运行**: 会自动创建数据库和配置文件
2. **端口要求**: 需要端口12456可用
3. **权限要求**: 建议以管理员权限运行
4. **防火墙**: 可能需要允许程序通过防火墙

## 🔧 故障排除

### 启动失败
- 检查端口12456是否被占用
- 以管理员权限运行
- 检查防火墙设置

### 数据库错误
- 运行 `init_db.py` 初始化数据库
- 删除 `server_data.db` 文件重新创建

### 无法访问
- 确认服务器已启动（控制台有输出）
- 检查浏览器地址: http://localhost:12456/admin
- 尝试使用 127.0.0.1:12456

### 功能异常
- 查看控制台错误信息
- 检查数据库文件是否正常创建
- 重新启动服务器

## 📞 技术支持

如有问题，请查看控制台输出的详细错误信息。

---
📅 构建时间: 2024-06-14
🏷️ 版本: 独立版 v1.0
🛠️ 构建工具: PyInstaller
💾 支持系统: Windows 7/8/10/11
