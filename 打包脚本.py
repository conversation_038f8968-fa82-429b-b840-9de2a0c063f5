#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统服务器打包脚本
将server.py打包成独立可执行文件，可在没有Python环境的机器上运行
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查必要文件
    required_files = [
        'server.py',
        'config.py',
        'templates',
        'static'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def create_spec_file():
    """创建PyInstaller配置文件"""
    print("📝 创建打包配置文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['server.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
        ('config.py', '.'),
        ('gift_id_map.json', '.'),
        ('keyword_response_pairs.json', '.'),
    ],
    hiddenimports=[
        'waitress',
        'flask',
        'flask_cors',
        'flask_socketio',
        'pymysql',
        'sqlite3',
        'json',
        'logging',
        'datetime',
        'os',
        'sys',
        'threading',
        'time',
        'requests',
        'urllib.parse',
        'hashlib',
        'base64',
        'uuid',
        'psutil',
        'gevent',
        'eventlet',
        'socketio',
        'engineio',
        'jinja2',
        'werkzeug',
        'click',
        'itsdangerous',
        'markupsafe',
        'blinker',
        'cryptography',
        'cffi',
        'pycparser',
        'six',
        'greenlet',
        'zope.interface',
        'zope.event',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'torch',
        'tensorflow',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='AI主播系统服务器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('AI主播系统服务器.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 配置文件创建完成: AI主播系统服务器.spec")

def run_packaging():
    """执行打包"""
    print("🚀 开始打包...")
    
    # 清理旧的构建文件
    if os.path.exists('build'):
        shutil.rmtree('build')
        print("🧹 清理旧的build目录")
    
    if os.path.exists('dist'):
        shutil.rmtree('dist')
        print("🧹 清理旧的dist目录")
    
    # 执行打包命令
    cmd = [
        'pyinstaller',
        '--clean',
        '--noconfirm',
        'AI主播系统服务器.spec'
    ]
    
    print(f"📦 执行打包命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    # Windows批处理脚本
    bat_content = '''@echo off
chcp 65001 > nul
title AI主播系统服务器
echo.
echo ========================================
echo    AI主播系统服务器
echo ========================================
echo.
echo 正在启动服务器...
echo.

"AI主播系统服务器.exe"

echo.
echo 服务器已停止运行
pause
'''
    
    with open('dist/启动服务器.bat', 'w', encoding='gbk') as f:
        f.write(bat_content)
    
    # PowerShell脚本
    ps1_content = '''# AI主播系统服务器启动脚本
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AI主播系统服务器" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "正在启动服务器..." -ForegroundColor Green
Write-Host ""

try {
    & "./AI主播系统服务器.exe"
} catch {
    Write-Host "启动失败: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "服务器已停止运行" -ForegroundColor Yellow
Read-Host "按回车键退出"
'''
    
    with open('dist/启动服务器.ps1', 'w', encoding='utf-8') as f:
        f.write(ps1_content)
    
    print("✅ 启动脚本创建完成")

def create_readme():
    """创建使用说明"""
    print("📝 创建使用说明...")
    
    readme_content = f'''# AI主播系统服务器 - 独立版本

## 📦 打包信息
- 打包时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- Python版本: {sys.version}
- 打包工具: PyInstaller
- 系统平台: {sys.platform}

## 🚀 使用方法

### 方法1: 双击启动
直接双击 `AI主播系统服务器.exe` 启动服务器

### 方法2: 使用启动脚本
- Windows: 双击 `启动服务器.bat`
- PowerShell: 右键 `启动服务器.ps1` → "使用PowerShell运行"

### 方法3: 命令行启动
```bash
./AI主播系统服务器.exe
```

## 🌐 访问地址
服务器启动后，访问以下地址：
- 主页: http://localhost:12456
- 管理后台: http://localhost:12456/admin
- 用户管理: http://localhost:12456/admin/user_management

## 🔑 默认登录信息
- 用户名: kaer
- 密码: a13456A

## 📁 文件结构
```
AI主播系统服务器/
├── AI主播系统服务器.exe    # 主程序
├── 启动服务器.bat          # Windows启动脚本
├── 启动服务器.ps1          # PowerShell启动脚本
├── README.md              # 使用说明
└── 更新管理数据库修复.py    # 数据库修复工具
```

## 🔧 故障排除

### 如果遇到数据库错误
运行数据库修复工具：
```bash
python 更新管理数据库修复.py
```

### 如果端口被占用
1. 检查端口占用: `netstat -ano | findstr :12456`
2. 结束占用进程: `taskkill /PID <进程ID> /F`
3. 重新启动服务器

### 如果防火墙阻止
1. 允许程序通过防火墙
2. 或临时关闭防火墙进行测试

## 📊 功能特性
- ✅ 用户管理 (885条用户记录)
- ✅ 卡密管理 (42条卡密记录)
- ✅ 更新管理 (版本控制)
- ✅ 直播状态监控
- ✅ 系统设置
- ✅ API接口管理
- ✅ 文件上传下载
- ✅ 实时日志查看

## 🎯 系统要求
- 操作系统: Windows 7/8/10/11 (64位)
- 内存: 至少 512MB 可用内存
- 磁盘: 至少 100MB 可用空间
- 网络: 需要访问MySQL数据库

## 📞 技术支持
如有问题，请检查：
1. 防火墙设置
2. 端口占用情况
3. 数据库连接
4. 日志文件内容

---
🎉 感谢使用AI主播系统服务器！
'''
    
    with open('dist/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    # 复制数据库修复工具
    if os.path.exists('更新管理数据库修复.py'):
        shutil.copy2('更新管理数据库修复.py', 'dist/')
        print("✅ 数据库修复工具已复制")
    
    print("✅ 使用说明创建完成")

def main():
    """主函数"""
    print("🚀 AI主播系统服务器打包工具")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请解决问题后重试")
        return False
    
    # 创建配置文件
    create_spec_file()
    
    # 执行打包
    if not run_packaging():
        print("❌ 打包失败")
        return False
    
    # 创建启动脚本和说明
    create_startup_script()
    create_readme()
    
    # 检查打包结果
    exe_path = 'dist/AI主播系统服务器.exe'
    if os.path.exists(exe_path):
        file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
        print("=" * 50)
        print("🎉 打包完成！")
        print(f"📁 输出目录: dist/")
        print(f"📦 可执行文件: {exe_path}")
        print(f"💾 文件大小: {file_size:.1f} MB")
        print("=" * 50)
        print("🚀 使用方法:")
        print("1. 进入 dist 目录")
        print("2. 双击 AI主播系统服务器.exe 启动")
        print("3. 或使用启动脚本: 启动服务器.bat")
        print("4. 访问 http://localhost:12456")
        print("=" * 50)
        return True
    else:
        print("❌ 打包失败，未找到可执行文件")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 打包成功！现在可以在没有Python环境的机器上运行了！")
    else:
        print("💥 打包失败，请检查错误信息")
    
    input("\n按回车键退出...")
