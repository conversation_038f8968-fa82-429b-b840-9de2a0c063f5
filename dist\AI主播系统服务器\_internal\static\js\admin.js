// AI主播系统管理后台 JavaScript

// 全局变量
let currentPage = 1;
let pageSize = 10;

// DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化退出登录按钮
    initLogoutButton();
    
    // 初始化侧边栏
    initSidebar();
    
    // 初始化响应式菜单
    initResponsiveMenu();
});

// 初始化退出登录按钮
function initLogoutButton() {
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            if (confirm('确定要退出登录吗？')) {
                logout();
            }
        });
    }
}

// 退出登录
function logout() {
    fetch('/admin/logout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        // 无论后端返回什么，都跳转到登录页
        window.location.href = '/admin/login';
    })
    .catch(error => {
        console.error('退出登录失败:', error);
        // 即使出错也跳转到登录页
        window.location.href = '/admin/login';
    });
}

// 初始化侧边栏
function initSidebar() {
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar a');
    
    sidebarLinks.forEach(link => {
        // 移除所有活动状态
        link.classList.remove('active');
        
        // 为当前页面添加活动状态
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
}

// 初始化响应式菜单
function initResponsiveMenu() {
    // 在小屏幕上添加菜单切换功能
    if (window.innerWidth <= 768) {
        const header = document.querySelector('.header');
        const sidebar = document.querySelector('.sidebar');
        
        // 创建菜单按钮
        const menuBtn = document.createElement('button');
        menuBtn.innerHTML = '<i class="fas fa-bars"></i>';
        menuBtn.className = 'menu-toggle';
        menuBtn.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
        `;
        
        header.insertBefore(menuBtn, header.firstChild);
        
        // 菜单切换事件
        menuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
        
        // 点击主内容区域关闭菜单
        document.querySelector('.main-content').addEventListener('click', function() {
            sidebar.classList.remove('show');
        });
    }
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.innerHTML = '<div class="loading"></div> 加载中...';
    }
}

// 显示错误信息
function showError(element, message) {
    if (element) {
        element.innerHTML = `<div class="alert alert-danger">${message}</div>`;
    }
}

// 显示成功信息
function showSuccess(element, message) {
    if (element) {
        element.innerHTML = `<div class="alert alert-success">${message}</div>`;
    }
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '--';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 创建分页组件
function createPagination(container, totalPages, currentPage, onPageChange) {
    if (!container) return;
    
    let paginationHTML = '<div class="pagination">';
    
    // 上一页按钮
    paginationHTML += `
        <button ${currentPage <= 1 ? 'disabled' : ''} onclick="${onPageChange}(${currentPage - 1})">
            <i class="fas fa-chevron-left"></i> 上一页
        </button>
    `;
    
    // 页码按钮
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);
    
    if (startPage > 1) {
        paginationHTML += `<button onclick="${onPageChange}(1)">1</button>`;
        if (startPage > 2) {
            paginationHTML += '<span>...</span>';
        }
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="${i === currentPage ? 'active' : ''}" onclick="${onPageChange}(${i})">
                ${i}
            </button>
        `;
    }
    
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            paginationHTML += '<span>...</span>';
        }
        paginationHTML += `<button onclick="${onPageChange}(${totalPages})">${totalPages}</button>`;
    }
    
    // 下一页按钮
    paginationHTML += `
        <button ${currentPage >= totalPages ? 'disabled' : ''} onclick="${onPageChange}(${currentPage + 1})">
            下一页 <i class="fas fa-chevron-right"></i>
        </button>
    `;
    
    paginationHTML += '</div>';
    
    container.innerHTML = paginationHTML;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

// 备用复制方法
function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showNotification('已复制到剪贴板', 'success');
        } else {
            showNotification('复制失败', 'error');
        }
    } catch (err) {
        console.error('复制失败:', err);
        showNotification('复制失败', 'error');
    }
    
    document.body.removeChild(textArea);
}

// 显示通知
function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // 设置背景色
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            notification.style.backgroundColor = '#ffc107';
            notification.style.color = '#212529';
            break;
        default:
            notification.style.backgroundColor = '#17a2b8';
    }
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, duration);
}

// 确认对话框
function confirmDialog(message, onConfirm, onCancel) {
    if (confirm(message)) {
        if (onConfirm) onConfirm();
    } else {
        if (onCancel) onCancel();
    }
}

// 导出数据为 CSV
function exportToCSV(data, filename) {
    if (!data || data.length === 0) {
        showNotification('没有数据可导出', 'warning');
        return;
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        showNotification('导出成功', 'success');
    } else {
        showNotification('浏览器不支持导出功能', 'error');
    }
}

// 窗口大小改变时重新初始化响应式功能
window.addEventListener('resize', debounce(function() {
    initResponsiveMenu();
}, 250));
