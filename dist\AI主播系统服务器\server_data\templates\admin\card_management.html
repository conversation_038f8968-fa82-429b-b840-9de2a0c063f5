<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management" class="active"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>生成卡密</h2>
            </div>
            <div class="card-body">
                <form id="generate-card-form">
                    <div class="form-group" style="display: flex; gap: 20px;">
                        <div style="flex: 1;">
                            <label for="card-days">有效天数</label>
                            <input type="number" id="card-days" class="form-control" min="1" value="30" required>
                        </div>
                        <div style="flex: 1;">
                            <label for="card-count">生成数量</label>
                            <input type="number" id="card-count" class="form-control" min="1" max="100" value="1" required>
                        </div>
                        <div style="align-self: flex-end;">
                            <button type="submit" class="btn btn-primary">生成卡密</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>卡密列表</h2>
                <div>
                    <select id="card-status-filter" class="form-control">
                        <option value="all">全部状态</option>
                        <option value="1">未使用</option>
                        <option value="0">已使用</option>
                    </select>
                </div>
            </div>
            <div class="card-body">
                <table class="table" id="cards-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>卡密</th>
                            <th>天数</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>使用时间</th>
                            <th>使用用户</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="7" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- 分页将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页码和状态筛选
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = parseInt(urlParams.get('page')) || 1;
            const statusFilter = urlParams.get('status');

            // 设置状态筛选下拉框的值
            if (statusFilter !== null) {
                document.getElementById('card-status-filter').value = statusFilter;
            }

            // 获取卡密列表
            fetchCards(currentPage, statusFilter);

            // 初始化卡密管理功能
            initCardManagement();
        });

        function fetchCards(page, status) {
            const pageSize = 10;
            let url = `/admin/cards?page=${page}&page_size=${pageSize}`;

            if (status !== null && status !== 'all') {
                url += `&status=${status}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const cards = data.数据.卡密列表;
                        const tbody = document.querySelector('#cards-table tbody');

                        if (cards.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        cards.forEach(card => {
                            const statusText = card.status === 1 ? '未使用' : '已使用';
                            const statusClass = card.status === 1 ? 'success' : 'warning';

                            tbody.innerHTML += `
                                <tr>
                                    <td>${card.id}</td>
                                    <td>${card.card_code}</td>
                                    <td>${card.days}</td>
                                    <td><span class="btn btn-sm btn-${statusClass}">${statusText}</span></td>
                                    <td>${card.create_time}</td>
                                    <td>${card.use_time || '--'}</td>
                                    <td>${card.username || '--'}</td>
                                </tr>
                            `;
                        });

                        // 生成分页
                        generatePagination(data.数据.当前页, data.数据.总页数);
                    }
                })
                .catch(error => {
                    console.error('获取卡密列表出错:', error);
                    const tbody = document.querySelector('#cards-table tbody');
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">加载失败</td></tr>';
                });
        }

        function generatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                return;
            }

            // 获取当前状态筛选
            const urlParams = new URLSearchParams(window.location.search);
            const statusFilter = urlParams.get('status');

            // 上一页
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.textContent = '上一页';
            prevLink.setAttribute('data-page', currentPage - 1);
            if (currentPage === 1) {
                prevLink.classList.add('disabled');
            }
            pagination.appendChild(prevLink);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.setAttribute('data-page', i);
                if (i === currentPage) {
                    pageLink.classList.add('active');
                }
                pagination.appendChild(pageLink);
            }

            // 下一页
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.textContent = '下一页';
            nextLink.setAttribute('data-page', currentPage + 1);
            if (currentPage === totalPages) {
                nextLink.classList.add('disabled');
            }
            pagination.appendChild(nextLink);

            // 添加点击事件
            const pageLinks = pagination.querySelectorAll('a:not(.disabled)');
            pageLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page) {
                        const currentUrl = new URL(window.location.href);
                        currentUrl.searchParams.set('page', page);
                        window.location.href = currentUrl.toString();
                    }
                });
            });
        }
    </script>
</body>
</html>
