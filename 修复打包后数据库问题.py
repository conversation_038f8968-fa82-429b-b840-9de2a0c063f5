#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复打包后数据库问题的脚本
解决 client_updates 表缺失和函数未定义问题
"""

import os
import re

def fix_server_imports():
    """修复server.py中的导入问题"""
    print("🔧 修复server.py中的导入问题...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经有正确的导入
        if 'from user_manager import get_sqlite_connection' in content:
            print("✅ get_sqlite_connection导入已存在")
        else:
            # 在文件开头添加导入
            lines = content.split('\n')
            import_added = False
            
            for i, line in enumerate(lines):
                if line.startswith('from user_manager import'):
                    # 在现有的user_manager导入行添加get_sqlite_connection
                    if 'get_sqlite_connection' not in line:
                        lines[i] = line.rstrip() + ', get_sqlite_connection'
                        import_added = True
                        break
                elif line.startswith('import user_manager'):
                    # 在import user_manager后添加新的导入行
                    lines.insert(i + 1, 'from user_manager import get_sqlite_connection')
                    import_added = True
                    break
            
            if not import_added:
                # 如果没有找到user_manager导入，在文件开头添加
                for i, line in enumerate(lines):
                    if line.startswith('import ') and not line.startswith('import os'):
                        lines.insert(i, 'from user_manager import get_sqlite_connection')
                        import_added = True
                        break
            
            if import_added:
                content = '\n'.join(lines)
                with open('server.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                print("✅ 已添加get_sqlite_connection导入")
            else:
                print("⚠️  未能添加导入，请手动检查")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复导入失败: {str(e)}")
        return False

def fix_database_connections():
    """修复所有数据库连接调用"""
    print("\n🔧 修复数据库连接调用...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有需要修复的模式
        patterns_to_fix = [
            # 直接使用sqlite3.connect的情况
            (r'import sqlite3\s*\n\s*conn = sqlite3\.connect\([^)]+\)', 'conn = get_sqlite_connection()'),
            (r'sqlite3\.connect\([^)]+\)', 'get_sqlite_connection()'),
            # 在函数内部的情况
            (r'(\s+)import sqlite3\s*\n(\s+)conn = sqlite3\.connect\([^)]+\)', r'\1conn = get_sqlite_connection()'),
        ]
        
        original_content = content
        changes_made = 0
        
        for pattern, replacement in patterns_to_fix:
            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            if new_content != content:
                changes_made += content.count(pattern) - new_content.count(pattern)
                content = new_content
        
        if content != original_content:
            with open('server.py', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已修复 {changes_made} 处数据库连接调用")
        else:
            print("ℹ️  未发现需要修复的数据库连接")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复数据库连接失败: {str(e)}")
        return False

def create_database_init_on_startup():
    """在server.py中添加启动时数据库初始化"""
    print("\n🔧 添加启动时数据库初始化...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找main函数或服务器启动的地方
        init_code = '''
def init_database_tables():
    """初始化数据库表"""
    try:
        conn = get_sqlite_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("数据库表初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库表初始化失败: {str(e)}")
        return False
'''
        
        # 查找合适的位置插入初始化函数
        if 'def init_database_tables():' not in content:
            # 在main函数之前插入
            main_pattern = r'(if __name__ == "__main__":)'
            if re.search(main_pattern, content):
                content = re.sub(main_pattern, init_code + r'\n\1', content)
                print("✅ 已添加数据库初始化函数")
            else:
                # 如果找不到main，在文件末尾添加
                content += init_code
                print("✅ 已在文件末尾添加数据库初始化函数")
        else:
            print("ℹ️  数据库初始化函数已存在")
        
        # 在服务器启动前添加初始化调用
        startup_patterns = [
            (r'(logger\.info\("使用 waitress 启动服务器\.\.\."\))', r'init_database_tables()\n    \1'),
            (r'(app\.run\()', r'init_database_tables()\n    \1'),
            (r'(serve\(app)', r'init_database_tables()\n    \1'),
        ]
        
        for pattern, replacement in startup_patterns:
            if re.search(pattern, content) and 'init_database_tables()' not in content.split(pattern)[0]:
                content = re.sub(pattern, replacement, content)
                print("✅ 已添加启动时数据库初始化调用")
                break
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 添加数据库初始化失败: {str(e)}")
        return False

def create_standalone_init_script():
    """创建独立的数据库初始化脚本"""
    print("\n🔧 创建独立的数据库初始化脚本...")
    
    init_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的数据库初始化脚本
用于修复打包后的数据库表缺失问题
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        # 尝试多个可能的数据库位置
        possible_paths = [
            os.path.join(base_path, "server_data.db"),
            os.path.join(os.getcwd(), "server_data.db"),
            os.path.join(base_path, "_internal", "server_data.db"),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # 如果都不存在，使用第一个路径创建新的
        return possible_paths[0]
        
    except Exception:
        return os.path.join(os.getcwd(), "server_data.db")

def init_all_tables():
    """初始化所有必要的数据库表"""
    db_path = get_database_path()
    logger.info(f"初始化数据库: {db_path}")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建API配置表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建日志表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建直播状态表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        # 创建token表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("所有数据库表初始化完成")
        print("✅ 数据库初始化成功！")
        print(f"📁 数据库位置: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始初始化数据库...")
    success = init_all_tables()
    
    if success:
        print("🎉 数据库初始化完成！")
        print("💡 现在可以正常运行服务器了")
    else:
        print("❌ 数据库初始化失败！")
        print("💡 请检查错误信息并重试")
    
    input("按回车键退出...")
'''
    
    with open('fix_database.py', 'w', encoding='utf-8') as f:
        f.write(init_script)
    
    print("✅ 已创建独立数据库初始化脚本: fix_database.py")
    return True

def main():
    """主函数"""
    print("🚀 修复打包后数据库问题")
    print("=" * 50)
    
    try:
        # 1. 修复导入问题
        if not fix_server_imports():
            return False
        
        # 2. 修复数据库连接调用
        if not fix_database_connections():
            return False
        
        # 3. 添加启动时数据库初始化
        if not create_database_init_on_startup():
            return False
        
        # 4. 创建独立初始化脚本
        if not create_standalone_init_script():
            return False
        
        print("\n" + "=" * 50)
        print("🎉 修复完成！")
        print("\n📝 修复内容:")
        print("✅ 修复了get_sqlite_connection导入问题")
        print("✅ 统一了数据库连接调用")
        print("✅ 添加了启动时数据库初始化")
        print("✅ 创建了独立的数据库修复脚本")
        
        print("\n💡 下一步:")
        print("1. 重新打包程序")
        print("2. 如果还有问题，运行 fix_database.py")
        print("3. 测试所有功能")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
