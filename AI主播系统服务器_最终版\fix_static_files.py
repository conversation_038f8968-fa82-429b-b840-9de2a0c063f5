#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
静态文件修复脚本
用于修复打包后静态文件无法加载的问题
"""

import os
import sys
import shutil

def fix_static_files():
    """修复静态文件问题"""
    print("🚀 开始修复静态文件问题...")
    
    try:
        # 获取当前目录
        current_dir = os.getcwd()
        print(f"当前目录: {current_dir}")
        
        # 检查是否在打包后的环境中
        if hasattr(sys, '_MEIPASS'):
            print("✅ 检测到打包环境")
            base_path = sys._MEIPASS
            print(f"打包基础路径: {base_path}")
            
            # 检查静态文件是否存在
            static_source = os.path.join(base_path, 'static')
            static_target = os.path.join(current_dir, 'static')
            
            if os.path.exists(static_source) and not os.path.exists(static_target):
                print("📁 复制静态文件到当前目录...")
                shutil.copytree(static_source, static_target)
                print("✅ 静态文件复制完成")
            elif os.path.exists(static_target):
                print("ℹ️  静态文件已存在")
            else:
                print("⚠️  未找到静态文件源")
            
            # 检查模板文件
            templates_source = os.path.join(base_path, 'templates')
            templates_target = os.path.join(current_dir, 'templates')
            
            if os.path.exists(templates_source) and not os.path.exists(templates_target):
                print("📁 复制模板文件到当前目录...")
                shutil.copytree(templates_source, templates_target)
                print("✅ 模板文件复制完成")
            elif os.path.exists(templates_target):
                print("ℹ️  模板文件已存在")
            else:
                print("⚠️  未找到模板文件源")
        else:
            print("ℹ️  开发环境，无需修复")
        
        # 创建必要的目录
        required_dirs = [
            'static/css',
            'static/js', 
            'static/images',
            'static/fonts',
            'static/downloads',
            'server_data/static/downloads'
        ]
        
        for dir_path in required_dirs:
            full_path = os.path.join(current_dir, dir_path)
            if not os.path.exists(full_path):
                os.makedirs(full_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
        
        print("🎉 静态文件修复完成！")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = fix_static_files()
    if success:
        print("✅ 静态文件修复成功！")
        print("💡 现在可以正常访问网页了")
    else:
        print("❌ 静态文件修复失败！")
    
    input("按回车键退出...")
