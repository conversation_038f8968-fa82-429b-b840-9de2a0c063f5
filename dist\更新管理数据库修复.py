#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新管理数据库修复工具
专门修复 client_updates 表的 fast_download_url 列缺失问题
"""

import sqlite3
import os
import glob
from datetime import datetime

def fix_client_updates_table(db_path):
    """修复 client_updates 表结构"""
    print(f"修复数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_updates'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("  创建 client_updates 表...")
            cursor.execute('''
                CREATE TABLE client_updates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version TEXT NOT NULL,
                    release_date TEXT NOT NULL,
                    description TEXT,
                    download_url TEXT,
                    fast_download_url TEXT,
                    force_update INTEGER DEFAULT 0,
                    is_exe INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 插入示例数据
            cursor.execute('''
                INSERT INTO client_updates (version, release_date, description, download_url, fast_download_url, force_update, is_exe)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                '1.8',
                '2025-06-14',
                '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
                'http://localhost:12456/static/downloads/AI主播系统.zip',
                'http://localhost:12456/api/fast-download/AI主播系统.zip',
                0,
                0
            ))
            print("  ✅ client_updates 表创建完成")
        else:
            # 检查现有列
            cursor.execute("PRAGMA table_info(client_updates)")
            columns = [column[1] for column in cursor.fetchall()]

            # 检查并添加缺失的列
            required_columns = {
                'fast_download_url': 'TEXT',
                'force_update': 'INTEGER DEFAULT 0',
                'is_exe': 'INTEGER DEFAULT 0'
            }

            for col_name, col_type in required_columns.items():
                if col_name not in columns:
                    print(f"  添加 {col_name} 列...")
                    cursor.execute(f'ALTER TABLE client_updates ADD COLUMN {col_name} {col_type}')
                    print(f"  ✅ {col_name} 列添加完成")
                else:
                    print(f"  ✅ {col_name} 列已存在")

            # 更新现有记录的 fast_download_url（如果为空）
            cursor.execute('''
                UPDATE client_updates
                SET fast_download_url = REPLACE(download_url, '/static/downloads/', '/api/fast-download/')
                WHERE fast_download_url IS NULL OR fast_download_url = ''
            ''')

            # 确保有数据
            cursor.execute("SELECT COUNT(*) FROM client_updates")
            count = cursor.fetchone()[0]

            if count == 0:
                print("  插入示例更新记录...")
                cursor.execute('''
                    INSERT INTO client_updates (version, release_date, description, download_url, fast_download_url, force_update, is_exe)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    '1.8',
                    '2025-06-14',
                    '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
                    'http://localhost:12456/static/downloads/AI主播系统.zip',
                    'http://localhost:12456/api/fast-download/AI主播系统.zip',
                    0,
                    0
                ))
                print("  ✅ 示例数据插入完成")
        
        conn.commit()
        conn.close()
        print(f"  ✅ 数据库修复完成: {db_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 开始修复更新管理数据库...")
    print("=" * 50)
    
    # 要修复的数据库文件列表
    db_files = []
    
    # 1. 当前目录的数据库文件
    current_dir_dbs = [
        'server_data.db',
        'local.db'
    ]
    
    for db_file in current_dir_dbs:
        if os.path.exists(db_file):
            db_files.append(db_file)
    
    # 2. 临时目录中的数据库文件
    temp_patterns = [
        r'C:\Users\<USER>\AppData\Local\Temp\_MEI*\server_data.db',
        r'C:\Users\<USER>\AppData\Local\Temp\_MEI*\server_data.db'
    ]
    
    for pattern in temp_patterns:
        temp_dbs = glob.glob(pattern)
        db_files.extend(temp_dbs)
    
    if not db_files:
        print("❌ 未找到数据库文件")
        print("\n手动创建数据库文件...")
        # 创建一个新的数据库文件
        db_files = ['server_data.db']
    
    # 修复所有找到的数据库
    success_count = 0
    for db_file in db_files:
        if fix_client_updates_table(db_file):
            success_count += 1
    
    print("=" * 50)
    print(f"🎉 修复完成！")
    print(f"📊 成功修复: {success_count}/{len(db_files)} 个数据库")
    
    if success_count > 0:
        print("\n💡 修复内容:")
        print("- ✅ client_updates表结构完整")
        print("- ✅ fast_download_url列已添加")
        print("- ✅ 示例更新数据已插入")
        print("\n🔄 请重新启动服务器以应用修复")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
