# AI虚拟主播系统

AI虚拟主播系统是一个集成了AI语音合成、弹幕互动、OBS控制等功能的直播助手软件，为直播场景提供了一个完整的AI主播解决方案。系统分为服务器端和客户端两部分，通过WebSocket实现实时通信。

## 系统架构

系统由以下主要部分组成：

1. **服务器端**：提供用户管理、数据存储、API接口和WebSocket通信服务
2. **客户端**：提供AI主播、话术管理、AI对话、OBS控制等功能
3. **数据库**：存储用户信息、话术内容、AI对话和系统日志
4. **WebSocket服务**：实现客户端和服务器的实时通信
5. **监控系统**：监控服务器和客户端状态

## 服务器端功能

### 1. 用户管理系统
- **用户注册**：支持新用户注册，包含用户名、密码、手机号和机器码验证
- **用户登录**：验证用户凭据并生成带有过期时间的token
- **用户充值**：支持卡密充值系统，延长账号使用期限
- **用户管理**：管理员可查看、编辑和删除用户信息
- **卡密生成**：管理员可生成充值卡密

### 2. 数据库管理
- **话术管理**：存储和管理话术内容
- **AI对话管理**：存储和管理AI对话关键词和回复
- **日志管理**：记录系统操作日志和客户端日志
- **数据备份**：定期自动备份数据库
- **数据统计**：提供数据库使用情况统计

### 3. WebSocket通信
- **实时状态更新**：客户端状态实时上报到服务器
- **命令下发**：服务器可向客户端发送命令
- **心跳机制**：保持连接活跃并检测客户端状态
- **认证机制**：基于token的WebSocket连接认证
- **断线重连**：支持自动重连和连接恢复

### 4. HTTP API接口
- **话术API**：获取/上传/新建话术
- **AI对话API**：获取/上传/新建AI对话
- **用户API**：用户注册/登录/充值
- **管理API**：用户管理/日志查询/数据统计
- **更新API**：客户端版本检查和更新

### 5. 监控系统
- **服务器监控**：监控CPU、内存、磁盘使用率
- **客户端监控**：监控客户端在线状态和使用情况
- **报警机制**：资源使用超过阈值时报警
- **性能统计**：收集和展示系统性能数据
- **状态展示**：在管理后台展示监控数据

### 6. 日志系统
- **系统日志**：记录服务器运行日志
- **用户操作日志**：记录用户操作
- **客户端日志**：记录客户端操作和状态
- **错误日志**：记录系统错误和异常
- **日志查询**：支持按用户名、日期、级别筛选日志
- **日志导出**：支持导出日志数据

### 7. 管理后台
- **用户管理界面**：管理用户账号
- **卡密管理界面**：生成和管理充值卡密
- **日志查看界面**：以表格形式查看系统日志
- **直播状态界面**：查看客户端直播状态
- **数据统计界面**：查看系统使用统计
- **更新管理界面**：管理客户端更新

## 客户端功能

### 1. AI主播功能
- **AI主播选择**：选择不同的AI主播声音
- **语速调整**：调整AI语音的播放速度
- **音量控制**：调整语音播放音量
- **语音生成**：根据话术生成AI语音
- **记忆上次选择**：记住上次选择的AI主播

### 2. 话术管理功能
- **话术选择**：选择不同的话术内容
- **话术编辑**：编辑和保存话术内容
- **新建话术**：创建新的话术
- **变量支持**：支持在话术中使用变量（如{nick}、{date}等）
- **随机选项**：支持【选项1|选项2|选项3】格式实现随机选择

### 3. AI对话功能
- **关键词管理**：管理触发关键词和回复内容
- **弹幕互动**：接收弹幕并根据关键词触发回复
- **变量处理**：在回复中处理变量（如{nick}、{gift}等）
- **副视频触发**：支持通过关键词触发OBS副视频切换
- **同义词识别**：识别关键词的同义词

### 4. 播放控制功能
- **语音生成**：根据话术生成语音
- **播放/暂停/停止**：控制语音播放
- **清空列表**：清空播放队列
- **播放模式**：支持随机模式和顺序模式
- **预备语音数量**：设置预生成的语音数量
- **播放间隔**：设置语音播放的间隔时间

### 5. OBS控制功能
- **OBS连接**：连接到OBS WebSocket服务器
- **媒体源管理**：获取和管理OBS媒体源
- **主副视频切换**：根据关键词触发切换到副视频
- **视频播放控制**：播放、暂停、停止视频
- **播放速度控制**：调整视频播放速度
- **定时切回**：副视频播放指定时间后自动切回主视频

### 6. 弹幕连接功能
- **WebSocket连接**：连接到弹幕WebSocket服务器
- **弹幕接收**：接收不同类型的弹幕消息
- **礼物处理**：处理礼物消息
- **关键词触发**：根据弹幕内容触发AI回复
- **弹幕显示**：在界面上显示弹幕内容

### 7. 系统设置功能
- **预备语音设置**：设置预生成的语音数量
- **循环模式设置**：设置随机或顺序播放模式
- **游戏设置**：设置游戏类型和游戏名称
- **声音设置**：调整音量、音调和间隔
- **设置保存**：保存设置到配置文件
- **设置加载**：启动时自动加载上次的设置

## 使用方法

### 服务器端

1. **安装依赖**
   ```
   pip install -r requirements.txt
   ```

2. **初始化数据库**
   ```
   python init_monitor_db.py
   ```

3. **启动服务器**
   ```
   python server.py
   ```
   
4. **管理后台访问**
   - 打开浏览器访问 `http://localhost:12456/admin`
   - 默认管理员账号：kaer
   - 默认管理员密码：a13456A

### 客户端

1. **安装依赖**
   ```
   pip install -r requirements.txt
   ```

2. **启动客户端**
   ```
   python main.py
   ```

3. **登录系统**
   - 使用管理员提供的账号和密码登录
   - 或使用注册功能创建新账号

4. **基本操作流程**
   1. 选择AI主播和语速
   2. 选择或编辑话术内容
   3. 设置AI对话关键词和回复
   4. 连接OBS（如需要）
   5. 连接弹幕服务器（如需要）
   6. 点击"生成语音"开始播放

## 功能详解

### 话术管理

1. **选择话术**：从下拉菜单中选择已有话术
2. **编辑话术**：在文本框中编辑话术内容
3. **保存话术**：点击"保存"按钮保存修改
4. **新建话术**：点击"新建"按钮创建新话术
5. **话术格式**：每行以数字***开头，如"1***这是一行话术"
6. **变量使用**：在话术中可使用以下变量：
   - `{nick}` - 用户昵称
   - `{date}` - 当前日期
   - `{time}` - 当前时间
   - `{people}` - 人数
   - `{gift}` - 礼物名称
   - `{gametype}` - 游戏类型
   - `{gamename}` - 游戏名称
7. **随机选项**：使用【选项1|选项2|选项3】格式实现随机选择

### AI对话管理

1. **选择对话**：从下拉菜单中选择已有对话
2. **添加关键词**：点击"添加关键词"按钮添加新关键词
3. **编辑回复**：在文本框中编辑关键词对应的回复内容
4. **删除关键词**：选中关键词后点击"删除关键词"按钮
5. **保存对话**：点击"保存"按钮保存修改
6. **新建对话**：点击"新建"按钮创建新对话
7. **副视频触发**：在回复中使用[#关键词#]格式触发副视频切换

### OBS控制

1. **连接OBS**：
   - 输入OBS WebSocket服务器地址和端口
   - 输入密码（如有）
   - 点击"连接"按钮

2. **设置主视频**：
   - 从下拉菜单中选择主视频源
   - 设置主视频播放速度范围

3. **设置副视频**：
   - 点击"添加副视频"按钮
   - 输入关键词、选择媒体源和设置播放时间
   - 点击"确定"添加

4. **测试副视频**：
   - 选择要测试的副视频关键词
   - 点击"测试"按钮

### 弹幕连接

1. **连接弹幕服务器**：
   - 在AI对话区输入弹幕WebSocket地址
   - 点击"连接"按钮

2. **弹幕互动**：
   - 系统自动接收弹幕消息
   - 根据设置的关键词自动触发AI回复
   - 支持处理不同类型的消息（聊天、礼物、会员等）

### 系统设置

1. **预备语音设置**：设置预生成的语音数量（1-10）
2. **循环模式设置**：选择随机模式或顺序模式
3. **游戏设置**：设置当前游戏类型和游戏名称
4. **声音设置**：
   - 调整音量（0-100%）
   - 调整音调（-12至+12）
   - 设置播放间隔（秒）
5. **均衡器设置**：设置播放间隔随机范围

## API接口

### 话术API

- `GET http://localhost:12456/getscriptlist` - 获取话术列表
- `POST http://localhost:12456/` (类型="获取话术") - 获取话术内容
- `POST http://localhost:12456/` (类型="上传话术") - 保存话术内容
- `POST http://localhost:12456/` (类型="新建话术") - 创建新话术

### AI对话API

- `GET http://localhost:12456/dialoguelist` - 获取AI对话列表
- `POST http://localhost:12456/` (类型="获取ai对话") - 获取AI对话内容
- `POST http://localhost:12456/` (类型="上传ai对话") - 保存AI对话内容
- `POST http://localhost:12456/` (类型="新建ai对话") - 创建新AI对话

### 用户API

- `POST http://localhost:12456/api/register` - 用户注册
- `POST http://localhost:12456/api/login` - 用户登录
- `POST http://localhost:12456/api/recharge` - 用户充值

### 管理API

- `GET http://localhost:12456/admin/api/users` - 获取用户列表
- `GET http://localhost:12456/admin/api/logs` - 获取日志列表
- `GET http://localhost:12456/admin/api/stats` - 获取统计数据
- `GET http://localhost:12456/admin/api/live_status` - 获取直播状态

### 监控系统API

- `GET http://localhost:12456/admin/api/monitor/status` - 获取服务器监控状态
- `GET http://localhost:12456/admin/api/monitor/history` - 获取监控历史数据
- `GET http://localhost:12456/admin/api/monitor/health` - 获取系统健康状态

### 备份管理API

- `GET http://localhost:12456/admin/api/backup` - 手动触发备份
- `GET http://localhost:12456/admin/api/backup/list` - 获取备份列表
- `POST http://localhost:12456/admin/api/backup/restore` - 从备份恢复数据

## WebSocket通信

### 服务器端WebSocket

- 监听地址：`ws://服务器IP:9999/`
- 认证方式：通过token认证
- 主要事件：
  - `connect` - 客户端连接
  - `authenticate` - 客户端认证
  - `status_update` - 客户端状态更新
  - `client_log` - 客户端日志上报
  - `ping` - 心跳检测

### 客户端WebSocket

- 连接地址：`ws://服务器IP:9999/`
- 认证方式：通过token认证
- 主要事件：
  - `connection_response` - 连接响应
  - `authentication_response` - 认证响应
  - `command` - 服务器命令
  - `pong` - 心跳响应

## 配置选项

可以通过修改各模块中的配置常量来自定义系统行为：

- `MONITOR_CONFIG` - 监控系统配置
- `BACKUP_CONFIG` - 备份管理器配置
- `ERROR_CONFIG` - 错误处理器配置
- `WS_CONFIG` - WebSocket服务器配置

## 系统要求

- Python 3.7+
- OBS Studio 28.0+ (带WebSocket插件)
- 网络连接（用于AI语音生成和弹幕接收）
- 操作系统：Windows 10/11 或 Linux

## 常见问题

1. **无法连接到服务器**
   - 检查服务器是否正常运行
   - 检查网络连接是否正常
   - 检查防火墙设置是否允许连接

2. **无法生成语音**
   - 检查AI主播是否已选择
   - 检查话术内容是否已填写
   - 检查网络连接是否正常

3. **OBS连接失败**
   - 检查OBS是否已启动
   - 检查WebSocket服务器是否已启用
   - 检查端口和密码是否正确

4. **弹幕连接失败**
   - 检查弹幕服务器地址是否正确
   - 检查网络连接是否正常
   - 检查弹幕服务器是否在线

5. **账号登录失败**
   - 检查用户名和密码是否正确
   - 检查账号是否已过期
   - 检查机器码是否匹配

## 联系与支持

如需技术支持或有任何问题，请联系系统管理员。
