#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本
解决用户管理和更新管理的问题
"""

import os
import sys
import sqlite3
import shutil
import glob

def find_database_files():
    """查找所有数据库文件"""
    db_files = []
    
    # 查找当前目录的数据库文件
    for db_name in ["local.db", "server_data.db"]:
        if os.path.exists(db_name):
            db_files.append(db_name)
    
    # 查找临时目录中的数据库文件
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        mei_patterns = [
            os.path.join(temp_dir, "_MEI*", "server_data.db"),
            os.path.join(temp_dir, "_MEI*", "local.db")
        ]
        for pattern in mei_patterns:
            matches = glob.glob(pattern)
            db_files.extend(matches)
    
    return db_files

def fix_database(db_path):
    """修复单个数据库文件"""
    print(f"修复数据库: {db_path}")
    
    try:
        # 检查数据库是否被锁定
        conn = sqlite3.connect(db_path, timeout=1.0)
        conn.close()
        print(f"  ✅ 数据库可访问: {db_path}")
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            print(f"  ⚠️ 数据库被锁定: {db_path}")
            # 尝试强制解锁
            try:
                # 创建备份
                backup_path = db_path + ".backup"
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                shutil.copy2(db_path, backup_path)
                print(f"  📋 已创建备份: {backup_path}")
                
                # 重新创建数据库
                if os.path.exists(db_path):
                    os.remove(db_path)
                print(f"  🗑️ 已删除锁定的数据库")
                
            except Exception as ex:
                print(f"  ❌ 无法解锁数据库: {str(ex)}")
                return False
        else:
            print(f"  ❌ 数据库错误: {str(e)}")
            return False
    
    # 创建/修复数据库表
    try:
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建所有必要的表
        tables = [
            # client_updates表
            """
            CREATE TABLE IF NOT EXISTS client_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL,
                release_date TEXT NOT NULL,
                description TEXT,
                download_url TEXT NOT NULL,
                force_update INTEGER DEFAULT 0,
                is_current INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_path TEXT,
                file_size INTEGER,
                file_hash TEXT,
                download_count INTEGER DEFAULT 0,
                status TEXT DEFAULT "pending",
                fast_download_url TEXT,
                is_exe INTEGER DEFAULT 0,
                is_folder_update INTEGER DEFAULT 0
            )
            """,
            # logs表
            """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            # live_status表
            """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """,
            # api_configs表
            """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            # danmaku_records表
            """
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                sender TEXT,
                content TEXT NOT NULL,
                timestamp TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            # tokens表
            """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """
        ]
        
        for table_sql in tables:
            cursor.execute(table_sql)
            print(f"  ✅ 表创建/检查完成")
        
        # 插入示例更新记录（如果不存在）
        cursor.execute("SELECT COUNT(*) FROM client_updates")
        count = cursor.fetchone()[0]
        
        if count == 0:
            cursor.execute("""
            INSERT INTO client_updates (
                version, release_date, description, download_url, 
                force_update, is_current, status, fast_download_url
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                "1.0.0",
                "2024-06-14",
                "初始版本发布",
                "/static/downloads/AI主播系统1.0.0.zip",
                0,
                1,
                "published",
                "/static/downloads/AI主播系统1.0.0.zip"
            ))
            print(f"  ✅ 插入示例更新记录")
        
        conn.commit()
        conn.close()
        print(f"  ✅ 数据库修复完成: {db_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复数据库失败: {str(e)}")
        return False

def copy_fixed_template():
    """复制修复后的用户管理模板"""
    source_template = "templates/admin/user_management.html"
    
    if not os.path.exists(source_template):
        print("⚠️ 源模板文件不存在，跳过模板修复")
        return False
    
    # 查找临时目录中的模板文件
    temp_dir = os.environ.get('TEMP', '')
    if temp_dir:
        mei_patterns = [
            os.path.join(temp_dir, "_MEI*", "templates", "admin", "user_management.html")
        ]
        
        copied = False
        for pattern in mei_patterns:
            matches = glob.glob(pattern)
            for target_template in matches:
                try:
                    # 创建备份
                    backup_path = target_template + ".backup"
                    if os.path.exists(target_template):
                        shutil.copy2(target_template, backup_path)
                    
                    # 复制修复后的模板
                    shutil.copy2(source_template, target_template)
                    print(f"✅ 已更新模板: {target_template}")
                    copied = True
                except Exception as e:
                    print(f"❌ 复制模板失败: {str(e)}")
        
        return copied
    
    return False

def main():
    """主函数"""
    print("🚀 开始快速修复...")
    print("=" * 50)
    
    # 1. 修复数据库
    print("📊 修复数据库...")
    db_files = find_database_files()
    
    if not db_files:
        print("⚠️ 未找到数据库文件")
    else:
        for db_file in db_files:
            fix_database(db_file)
    
    print()
    
    # 2. 复制修复后的模板
    print("📄 修复用户管理模板...")
    copy_fixed_template()
    
    print()
    print("=" * 50)
    print("🎉 快速修复完成！")
    print()
    print("💡 修复内容:")
    print("- ✅ 数据库锁定问题")
    print("- ✅ client_updates表")
    print("- ✅ logs表")
    print("- ✅ live_status表")
    print("- ✅ 用户管理模板")
    print()
    print("🔄 请重新启动服务器以应用修复")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
