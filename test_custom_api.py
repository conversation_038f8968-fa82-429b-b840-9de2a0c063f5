#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义API功能
"""

import requests
import json
import time

BASE_URL = "http://localhost:12456"

def test_admin_login():
    """测试管理员登录"""
    print("🔐 测试管理员登录...")
    
    login_url = BASE_URL + "/admin/login"
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    try:
        response = requests.post(login_url, json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功: {data.get('状态', 'N/A')}")
            return response.cookies
        else:
            print(f"❌ 登录失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求失败: {str(e)}")
        return None

def test_create_custom_api(cookies):
    """测试创建自定义API"""
    print("\n🆕 测试创建自定义API...")
    
    # 准备新增数据（使用时间戳确保唯一性）
    import time
    timestamp = str(int(time.time()))
    create_data = {
        "api_name": f"custom_api_{timestamp}",
        "api_path": f"/api/custom_{timestamp}",
        "api_title": f"自定义API_{timestamp}",
        "api_description": "这是一个自定义创建的API接口",
        "response_content": json.dumps({
            "status": "success",
            "message": "这是我的自定义API返回内容",
            "data": {
                "title": "自定义API测试",
                "content": "API创建成功！可以返回任何你想要的内容。",
                "timestamp": "2024-01-20 15:30:00",
                "custom_fields": {
                    "author": "管理员",
                    "version": "1.0",
                    "features": ["自定义内容", "动态配置", "实时生效"]
                }
            }
        }, ensure_ascii=False),
        "is_enabled": 1
    }
    
    try:
        create_url = BASE_URL + "/admin/api/configs"
        response = requests.post(create_url, json=create_data, cookies=cookies, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 创建API成功: {data.get('状态', 'N/A')}")
            print(f"✅ 信息: {data.get('信息', 'N/A')}")
            
            # 测试新创建的API接口
            test_url = BASE_URL + f"/api/custom_{timestamp}"
            print(f"\n🧪 测试新创建的API接口: {test_url}")
            
            time.sleep(1)  # 等待一下
            
            api_response = requests.get(test_url, timeout=10)
            if api_response.status_code == 200:
                api_data = api_response.json()
                print(f"✅ 新API接口测试成功")
                print(f"✅ 响应状态: {api_data.get('status', 'N/A')}")
                print(f"✅ 响应消息: {api_data.get('message', 'N/A')}")
                print(f"✅ 数据标题: {api_data.get('data', {}).get('title', 'N/A')}")
                return data.get('数据', {}).get('id'), f"/api/custom_{timestamp}"
            else:
                print(f"❌ 新API接口测试失败，状态码: {api_response.status_code}")
                return None, None
                
        else:
            print(f"❌ 创建API失败，状态码: {response.status_code}")
            print(f"❌ 响应内容: {response.text[:200]}...")
            return None, None

    except Exception as e:
        print(f"❌ 创建API请求失败: {str(e)}")
        return None, None

def test_update_custom_api(cookies, api_id, api_path):
    """测试更新自定义API"""
    print(f"\n🔧 测试更新自定义API (ID: {api_id})...")

    # 准备更新数据
    update_data = {
        "api_title": "我的自定义API (已更新)",
        "api_description": "这是一个更新后的自定义API接口",
        "response_content": json.dumps({
            "status": "success",
            "message": "这是更新后的API返回内容",
            "data": {
                "title": "更新后的自定义API",
                "content": "API已成功更新！内容已修改。",
                "timestamp": "2024-01-20 16:00:00",
                "update_info": {
                    "updated_by": "管理员",
                    "update_time": "2024-01-20 16:00:00",
                    "changes": ["更新了标题", "修改了内容", "添加了更新信息"]
                }
            }
        }, ensure_ascii=False),
        "is_enabled": 1
    }
    
    try:
        update_url = BASE_URL + f"/admin/api/configs/{api_id}"
        response = requests.put(update_url, json=update_data, cookies=cookies, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 更新API成功: {data.get('状态', 'N/A')}")
            
            # 测试更新后的API接口
            test_url = BASE_URL + api_path
            print(f"\n🧪 测试更新后的API接口: {test_url}")
            
            time.sleep(1)  # 等待一下
            
            api_response = requests.get(test_url, timeout=10)
            if api_response.status_code == 200:
                api_data = api_response.json()
                print(f"✅ 更新后API接口测试成功")
                print(f"✅ 响应消息: {api_data.get('message', 'N/A')}")
                print(f"✅ 数据标题: {api_data.get('data', {}).get('title', 'N/A')}")
            else:
                print(f"❌ 更新后API接口测试失败，状态码: {api_response.status_code}")
                
        else:
            print(f"❌ 更新API失败，状态码: {response.status_code}")
            print(f"❌ 响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 更新API请求失败: {str(e)}")

def test_delete_custom_api(cookies, api_id, api_path):
    """测试删除自定义API"""
    print(f"\n🗑️  测试删除自定义API (ID: {api_id})...")

    try:
        delete_url = BASE_URL + f"/admin/api/configs/{api_id}"
        response = requests.delete(delete_url, cookies=cookies, timeout=10)

        if response.status_code == 200:
            data = response.json()
            print(f"✅ 删除API成功: {data.get('状态', 'N/A')}")

            # 测试删除后的API接口是否还能访问
            test_url = BASE_URL + api_path
            print(f"\n🧪 测试删除后的API接口: {test_url}")
            
            time.sleep(1)  # 等待一下
            
            try:
                api_response = requests.get(test_url, timeout=10)
                if api_response.status_code == 404:
                    print(f"✅ API接口已正确删除，返回404")
                elif api_response.status_code == 200:
                    api_data = api_response.json()
                    if api_data.get('status') == 'error':
                        print(f"✅ API接口已正确删除，返回错误信息")
                    else:
                        print(f"⚠️  API接口删除后仍可访问")
                else:
                    print(f"⚠️  API接口删除后状态码: {api_response.status_code}")
            except requests.exceptions.RequestException:
                print(f"✅ API接口已正确删除，无法访问")
                
        else:
            print(f"❌ 删除API失败，状态码: {response.status_code}")
            print(f"❌ 响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ 删除API请求失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 开始测试自定义API功能")
    print(f"🌐 服务器地址: {BASE_URL}")
    print("=" * 60)
    
    # 1. 测试管理员登录
    cookies = test_admin_login()
    if not cookies:
        print("❌ 无法登录，测试终止")
        return
    
    # 2. 测试创建自定义API
    api_id, api_path = test_create_custom_api(cookies)
    if not api_id:
        print("❌ 无法创建API，测试终止")
        return

    # 3. 测试更新自定义API
    test_update_custom_api(cookies, api_id, api_path)

    # 4. 测试删除自定义API
    test_delete_custom_api(cookies, api_id, api_path)
    
    print("\n" + "=" * 60)
    print("🎉 自定义API功能测试完成！")
    print("\n📝 测试总结:")
    print("✅ 支持通过后台管理页面新增自定义API接口")
    print("✅ 支持自定义API名称、路径、标题、描述和返回内容")
    print("✅ 支持更新已有API配置")
    print("✅ 支持删除API配置")
    print("✅ API配置变更实时生效，无需重启服务器")
    print("✅ 返回内容支持复杂的JSON结构")
    
    print(f"\n🎛️  管理页面: {BASE_URL}/admin/api_management")
    print("💡 提示: 您可以在管理页面中可视化地管理所有API接口")

if __name__ == "__main__":
    main()
