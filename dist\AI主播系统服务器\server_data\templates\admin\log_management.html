<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志查看 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management" class="active"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>操作日志</h2>
                <div style="display: flex; gap: 10px;">
                    <form id="username-filter-form" style="display: flex; gap: 10px;">
                        <input type="text" id="username-filter" class="form-control" placeholder="按用户名筛选">
                        <button type="submit" class="btn btn-primary">筛选</button>
                    </form>
                    <button id="clear-filter-btn" class="btn btn-danger">清除筛选</button>
                </div>
            </div>
            <div class="card-body">
                <table class="table" id="logs-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户ID</th>
                            <th>用户名</th>
                            <th>IP地址</th>
                            <th>操作</th>
                            <th>详情</th>
                            <th>时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="7" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>

                <div class="pagination" id="pagination">
                    <!-- 分页将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页码和用户名筛选
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = parseInt(urlParams.get('page')) || 1;
            const usernameFilter = urlParams.get('username');

            // 设置用户名筛选输入框的值
            if (usernameFilter) {
                document.getElementById('username-filter').value = usernameFilter;
            }

            // 获取日志列表
            fetchLogs(currentPage, usernameFilter);

            // 初始化日志管理功能
            initLogManagement();
        });

        function fetchLogs(page, username) {
            const pageSize = 10;
            let url = `/admin/logs?page=${page}&page_size=${pageSize}`;

            if (username) {
                url += `&username=${encodeURIComponent(username)}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const logs = data.数据.日志列表;
                        const tbody = document.querySelector('#logs-table tbody');

                        if (logs.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        logs.forEach(log => {
                            tbody.innerHTML += `
                                <tr>
                                    <td>${log.id}</td>
                                    <td>${log.user_id || '--'}</td>
                                    <td>${log.username || '--'}</td>
                                    <td>${log.ip || '--'}</td>
                                    <td>${log.action}</td>
                                    <td>${log.details || '--'}</td>
                                    <td>${log.time}</td>
                                </tr>
                            `;
                        });

                        // 生成分页
                        generatePagination(data.数据.当前页, data.数据.总页数);
                    }
                })
                .catch(error => {
                    console.error('获取日志列表出错:', error);
                    const tbody = document.querySelector('#logs-table tbody');
                    tbody.innerHTML = '<tr><td colspan="7" style="text-align: center;">加载失败</td></tr>';
                });
        }

        function generatePagination(currentPage, totalPages) {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                return;
            }

            // 获取当前用户名筛选
            const urlParams = new URLSearchParams(window.location.search);
            const usernameFilter = urlParams.get('username');

            // 上一页
            const prevLink = document.createElement('a');
            prevLink.href = '#';
            prevLink.textContent = '上一页';
            prevLink.setAttribute('data-page', currentPage - 1);
            if (currentPage === 1) {
                prevLink.classList.add('disabled');
            }
            pagination.appendChild(prevLink);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, startPage + 4);

            for (let i = startPage; i <= endPage; i++) {
                const pageLink = document.createElement('a');
                pageLink.href = '#';
                pageLink.textContent = i;
                pageLink.setAttribute('data-page', i);
                if (i === currentPage) {
                    pageLink.classList.add('active');
                }
                pagination.appendChild(pageLink);
            }

            // 下一页
            const nextLink = document.createElement('a');
            nextLink.href = '#';
            nextLink.textContent = '下一页';
            nextLink.setAttribute('data-page', currentPage + 1);
            if (currentPage === totalPages) {
                nextLink.classList.add('disabled');
            }
            pagination.appendChild(nextLink);

            // 添加点击事件
            const pageLinks = pagination.querySelectorAll('a:not(.disabled)');
            pageLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const page = parseInt(this.getAttribute('data-page'));
                    if (page) {
                        const currentUrl = new URL(window.location.href);
                        currentUrl.searchParams.set('page', page);
                        window.location.href = currentUrl.toString();
                    }
                });
            });
        }
    </script>
</body>
</html>
