<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充值管理</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>充值管理</h1>
            <div>
                <button id="refreshBtn" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <a href="/admin" class="btn btn-secondary">返回管理面板</a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">充值记录</h5>
                        <div class="d-flex">
                            <select id="statusFilter" class="form-select me-2">
                                <option value="all">全部状态</option>
                                <option value="1">已使用</option>
                                <option value="0">未使用</option>
                            </select>
                            <button id="applyFilterBtn" class="btn btn-primary">筛选</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>卡密</th>
                                        <th>天数</th>
                                        <th>状态</th>
                                        <th>使用用户</th>
                                        <th>生成时间</th>
                                        <th>使用时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="rechargeTable">
                                    <tr>
                                        <td colspan="7" class="text-center">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <nav aria-label="充值记录分页">
                            <ul class="pagination" id="pagination">
                                <!-- 分页将由JavaScript动态生成 -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">生成卡密</h5>
                    </div>
                    <div class="card-body">
                        <form id="generateCardForm">
                            <div class="mb-3">
                                <label for="cardDays" class="form-label">有效天数</label>
                                <input type="number" class="form-control" id="cardDays" value="30" min="1" required>
                            </div>
                            <div class="mb-3">
                                <label for="cardCount" class="form-label">生成数量</label>
                                <input type="number" class="form-control" id="cardCount" value="1" min="1" max="100" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">生成卡密</button>
                        </form>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">充值统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                总卡密数量
                                <span class="badge bg-primary rounded-pill" id="totalCards">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                已使用卡密
                                <span class="badge bg-success rounded-pill" id="usedCards">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                未使用卡密
                                <span class="badge bg-warning rounded-pill" id="unusedCards">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                今日生成
                                <span class="badge bg-info rounded-pill" id="todayGenerated">0</span>
                            </div>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                今日使用
                                <span class="badge bg-secondary rounded-pill" id="todayUsed">0</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">导出卡密</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button id="exportAllBtn" class="btn btn-outline-primary">导出全部卡密</button>
                            <button id="exportUnusedBtn" class="btn btn-outline-success">导出未使用卡密</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡密详情模态框 -->
    <div class="modal fade" id="cardDetailModal" tabindex="-1" aria-labelledby="cardDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="cardDetailModalLabel">卡密详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">卡密</label>
                        <input type="text" class="form-control" id="detailCardCode" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">天数</label>
                        <input type="text" class="form-control" id="detailDays" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">状态</label>
                        <input type="text" class="form-control" id="detailStatus" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">使用用户</label>
                        <input type="text" class="form-control" id="detailUser" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">生成时间</label>
                        <input type="text" class="form-control" id="detailCreateTime" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">使用时间</label>
                        <input type="text" class="form-control" id="detailUseTime" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成结果模态框 -->
    <div class="modal fade" id="generateResultModal" tabindex="-1" aria-labelledby="generateResultModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="generateResultModalLabel">卡密生成结果</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success" role="alert">
                        成功生成 <span id="generatedCount">0</span> 个卡密
                    </div>
                    <div class="mb-3">
                        <label class="form-label">卡密列表</label>
                        <textarea class="form-control" id="generatedCards" rows="10" readonly></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="copyCardsBtn">复制卡密</button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        const pageSize = 10;
        let totalPages = 1;
        let statusFilter = 'all';

        // 模态框实例
        let cardDetailModal;
        let generateResultModal;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化模态框
            cardDetailModal = new bootstrap.Modal(document.getElementById('cardDetailModal'));
            generateResultModal = new bootstrap.Modal(document.getElementById('generateResultModal'));

            // 加载充值记录
            loadRechargeRecords(currentPage, statusFilter);

            // 加载充值统计
            loadRechargeStats();

            // 绑定事件
            document.getElementById('generateCardForm').addEventListener('submit', function(e) {
                e.preventDefault();
                generateCards();
            });

            document.getElementById('applyFilterBtn').addEventListener('click', function() {
                statusFilter = document.getElementById('statusFilter').value;
                currentPage = 1;
                loadRechargeRecords(currentPage, statusFilter);
            });

            document.getElementById('refreshBtn').addEventListener('click', function() {
                loadRechargeRecords(currentPage, statusFilter);
                loadRechargeStats();
            });

            document.getElementById('exportAllBtn').addEventListener('click', function() {
                exportCards('all');
            });

            document.getElementById('exportUnusedBtn').addEventListener('click', function() {
                exportCards('unused');
            });

            document.getElementById('copyCardsBtn').addEventListener('click', function() {
                const textarea = document.getElementById('generatedCards');
                textarea.select();
                document.execCommand('copy');
                alert('卡密已复制到剪贴板');
            });
        });

        function loadRechargeRecords(page, status) {
            // 构建请求URL
            let url = `/admin/cards?page=${page}&page_size=${pageSize}`;
            if (status !== 'all') {
                url += `&status=${status}`;
            }

            // 显示加载中
            document.getElementById('rechargeTable').innerHTML = '<tr><td colspan="7" class="text-center">加载中...</td></tr>';

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const cards = data.数据.卡密列表;
                        totalPages = Math.ceil(data.数据.总数 / pageSize);

                        // 更新表格
                        updateRechargeTable(cards);

                        // 更新分页
                        updatePagination();
                    } else {
                        document.getElementById('rechargeTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">${data.信息 || '加载失败'}</td></tr>`;
                    }
                })
                .catch(error => {
                    console.error('加载充值记录出错:', error);
                    document.getElementById('rechargeTable').innerHTML = `<tr><td colspan="7" class="text-center text-danger">加载出错: ${error.message}</td></tr>`;
                });
        }

        function updateRechargeTable(cards) {
            const tableBody = document.getElementById('rechargeTable');

            if (!cards || cards.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';
                return;
            }

            let html = '';
            cards.forEach(card => {
                const statusClass = card.status === 1 ? 'bg-success' : 'bg-warning';
                const statusText = card.status === 1 ? '已使用' : '未使用';

                html += `
                <tr>
                    <td>${card.card_code}</td>
                    <td>${card.days}天</td>
                    <td><span class="badge ${statusClass}">${statusText}</span></td>
                    <td>${card.use_username || '-'}</td>
                    <td>${card.create_time || '-'}</td>
                    <td>${card.use_time || '-'}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-card-btn" data-card-id="${card.id}">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${card.status === 0 ? `
                        <button class="btn btn-sm btn-danger delete-card-btn" data-card-id="${card.id}">
                            <i class="bi bi-trash"></i>
                        </button>
                        ` : ''}
                    </td>
                </tr>
                `;
            });

            tableBody.innerHTML = html;

            // 绑定查看按钮事件
            document.querySelectorAll('.view-card-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const cardId = this.getAttribute('data-card-id');
                    viewCardDetail(cardId);
                });
            });

            // 绑定删除按钮事件
            document.querySelectorAll('.delete-card-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const cardId = this.getAttribute('data-card-id');
                    deleteCard(cardId);
                });
            });
        }

        function updatePagination() {
            const pagination = document.getElementById('pagination');
            pagination.innerHTML = '';

            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }

            pagination.style.display = 'flex';

            // 上一页
            const prevLi = document.createElement('li');
            prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            prevLi.innerHTML = `<a class="page-link" href="#" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>`;
            prevLi.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    currentPage--;
                    loadRechargeRecords(currentPage, statusFilter);
                }
            });
            pagination.appendChild(prevLi);

            // 页码
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                const pageLi = document.createElement('li');
                pageLi.className = `page-item ${i === currentPage ? 'active' : ''}`;
                pageLi.innerHTML = `<a class="page-link" href="#">${i}</a>`;
                pageLi.addEventListener('click', function(e) {
                    e.preventDefault();
                    currentPage = i;
                    loadRechargeRecords(currentPage, statusFilter);
                });
                pagination.appendChild(pageLi);
            }

            // 下一页
            const nextLi = document.createElement('li');
            nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            nextLi.innerHTML = `<a class="page-link" href="#" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>`;
            nextLi.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    currentPage++;
                    loadRechargeRecords(currentPage, statusFilter);
                }
            });
            pagination.appendChild(nextLi);
        }

        function loadRechargeStats() {
            fetch('/admin/api/cards/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const stats = data.数据;
                        document.getElementById('totalCards').textContent = stats.total || 0;
                        document.getElementById('usedCards').textContent = stats.used || 0;
                        document.getElementById('unusedCards').textContent = stats.unused || 0;
                        document.getElementById('todayGenerated').textContent = stats.today_generated || 0;
                        document.getElementById('todayUsed').textContent = stats.today_used || 0;
                    }
                })
                .catch(error => {
                    console.error('加载充值统计出错:', error);
                });
        }

        function generateCards() {
            const days = parseInt(document.getElementById('cardDays').value);
            const count = parseInt(document.getElementById('cardCount').value);

            if (isNaN(days) || days <= 0) {
                alert('请输入有效的天数');
                return;
            }

            if (isNaN(count) || count <= 0 || count > 100) {
                alert('请输入有效的数量（1-100）');
                return;
            }

            fetch('/admin/cards/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    days: days,
                    count: count
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    // 显示生成结果
                    document.getElementById('generatedCount').textContent = data.卡密列表.length;
                    document.getElementById('generatedCards').value = data.卡密列表.join('\n');
                    generateResultModal.show();

                    // 刷新数据
                    loadRechargeRecords(1, statusFilter);
                    loadRechargeStats();
                } else {
                    alert('生成卡密失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('生成卡密出错:', error);
                alert('生成卡密出错，请重试');
            });
        }

        function viewCardDetail(cardId) {
            fetch(`/admin/api/cards/${cardId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const card = data.数据;
                        document.getElementById('detailCardCode').value = card.card_code || '';
                        document.getElementById('detailDays').value = card.days ? `${card.days}天` : '';
                        document.getElementById('detailStatus').value = card.status === 1 ? '已使用' : '未使用';
                        document.getElementById('detailUser').value = card.use_username || '-';
                        document.getElementById('detailCreateTime').value = card.create_time || '-';
                        document.getElementById('detailUseTime').value = card.use_time || '-';
                        cardDetailModal.show();
                    } else {
                        alert('获取卡密详情失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('获取卡密详情出错:', error);
                    alert('获取卡密详情出错，请重试');
                });
        }

        function deleteCard(cardId) {
            if (confirm('确定要删除这个卡密吗？此操作不可恢复！')) {
                fetch(`/admin/api/cards/${cardId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert('卡密删除成功');
                        loadRechargeRecords(currentPage, statusFilter);
                        loadRechargeStats();
                    } else {
                        alert('删除卡密失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('删除卡密出错:', error);
                    alert('删除卡密出错，请重试');
                });
            }
        }

        function exportCards(type) {
            fetch(`/admin/api/cards/export?type=${type}`)
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const cards = data.数据;
                        if (cards && cards.length > 0) {
                            // 创建下载链接
                            const content = cards.join('\n');
                            const blob = new Blob([content], { type: 'text/plain' });
                            const url = URL.createObjectURL(blob);
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `卡密导出_${type === 'all' ? '全部' : '未使用'}_${new Date().toISOString().slice(0, 10)}.txt`;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                            URL.revokeObjectURL(url);
                        } else {
                            alert('没有可导出的卡密');
                        }
                    } else {
                        alert('导出卡密失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('导出卡密出错:', error);
                    alert('导出卡密出错，请重试');
                });
        }
    </script>
</body>
</html>
