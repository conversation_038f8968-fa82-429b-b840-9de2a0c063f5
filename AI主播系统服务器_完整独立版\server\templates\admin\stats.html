<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据统计</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 1200px;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .stat-card {
            text-align: center;
            padding: 20px;
        }
        .stat-card .number {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-card .label {
            font-size: 1rem;
            color: #6c757d;
        }
        .stat-card.users {
            color: #007bff;
        }
        .stat-card.scripts {
            color: #28a745;
        }
        .stat-card.dialogues {
            color: #dc3545;
        }
        .stat-card.voices {
            color: #6f42c1;
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1>数据统计</h1>
            <div>
                <button id="refreshBtn" class="btn btn-primary me-2">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <a href="/admin" class="btn btn-secondary">返回管理面板</a>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="stat-card users">
                        <i class="bi bi-people" style="font-size: 2rem;"></i>
                        <div class="number" id="totalUsers">0</div>
                        <div class="label">用户总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="stat-card scripts">
                        <i class="bi bi-file-text" style="font-size: 2rem;"></i>
                        <div class="number" id="totalScripts">0</div>
                        <div class="label">话术总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="stat-card dialogues">
                        <i class="bi bi-chat-dots" style="font-size: 2rem;"></i>
                        <div class="number" id="totalDialogues">0</div>
                        <div class="label">对话总数</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="stat-card voices">
                        <i class="bi bi-volume-up" style="font-size: 2rem;"></i>
                        <div class="number" id="totalVoices">0</div>
                        <div class="label">语音总数</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">用户注册趋势</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userRegistrationChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">用户活跃度</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userActivityChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">话术使用统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="scriptUsageChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">系统资源使用</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="resourceUsageChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">数据库统计</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">数据库大小</h6>
                                        <p class="card-text fs-4" id="dbSize">0 MB</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">表数量</h6>
                                        <p class="card-text fs-4" id="tableCount">0</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">记录总数</h6>
                                        <p class="card-text fs-4" id="recordCount">0</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">索引数量</h6>
                                        <p class="card-text fs-4" id="indexCount">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 图表实例
        let userRegistrationChart;
        let userActivityChart;
        let scriptUsageChart;
        let resourceUsageChart;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            initCharts();
            
            // 加载统计数据
            loadStats();
            
            // 刷新按钮事件
            document.getElementById('refreshBtn').addEventListener('click', loadStats);
        });
        
        function initCharts() {
            // 用户注册趋势图
            const userRegistrationCtx = document.getElementById('userRegistrationChart').getContext('2d');
            userRegistrationChart = new Chart(userRegistrationCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '注册用户数',
                        data: [],
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 用户活跃度图
            const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
            userActivityChart = new Chart(userActivityCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: '活跃用户数',
                        data: [],
                        backgroundColor: '#28a745',
                        borderColor: '#28a745',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            // 话术使用统计图
            const scriptUsageCtx = document.getElementById('scriptUsageChart').getContext('2d');
            scriptUsageChart = new Chart(scriptUsageCtx, {
                type: 'pie',
                data: {
                    labels: [],
                    datasets: [{
                        label: '使用次数',
                        data: [],
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#dc3545',
                            '#ffc107',
                            '#17a2b8',
                            '#6c757d',
                            '#6f42c1',
                            '#fd7e14',
                            '#20c997',
                            '#e83e8c'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
            
            // 系统资源使用图
            const resourceUsageCtx = document.getElementById('resourceUsageChart').getContext('2d');
            resourceUsageChart = new Chart(resourceUsageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['CPU', '内存', '磁盘', '网络'],
                    datasets: [{
                        label: '使用率',
                        data: [0, 0, 0, 0],
                        backgroundColor: [
                            '#007bff',
                            '#28a745',
                            '#dc3545',
                            '#ffc107'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        title: {
                            display: false
                        }
                    }
                }
            });
        }
        
        function loadStats() {
            // 加载基本统计数据
            fetch('/admin/api/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const stats = data.数据;
                        
                        // 更新基本统计数据
                        document.getElementById('totalUsers').textContent = stats.user_count || 0;
                        document.getElementById('totalScripts').textContent = stats.script_count || 0;
                        document.getElementById('totalDialogues').textContent = stats.dialogue_count || 0;
                        document.getElementById('totalVoices').textContent = stats.voice_count || 0;
                        
                        // 更新数据库统计
                        document.getElementById('dbSize').textContent = formatFileSize(stats.db_size || 0);
                        document.getElementById('tableCount').textContent = stats.table_count || 0;
                        document.getElementById('recordCount').textContent = stats.record_count || 0;
                        document.getElementById('indexCount').textContent = stats.index_count || 0;
                        
                        // 更新图表数据
                        updateCharts(stats);
                    } else {
                        console.error('加载统计数据失败:', data.信息);
                    }
                })
                .catch(error => {
                    console.error('加载统计数据出错:', error);
                });
        }
        
        function updateCharts(stats) {
            // 更新用户注册趋势图
            if (stats.user_registration_trend) {
                const trend = stats.user_registration_trend;
                userRegistrationChart.data.labels = trend.labels || [];
                userRegistrationChart.data.datasets[0].data = trend.data || [];
                userRegistrationChart.update();
            }
            
            // 更新用户活跃度图
            if (stats.user_activity) {
                const activity = stats.user_activity;
                userActivityChart.data.labels = activity.labels || [];
                userActivityChart.data.datasets[0].data = activity.data || [];
                userActivityChart.update();
            }
            
            // 更新话术使用统计图
            if (stats.script_usage) {
                const usage = stats.script_usage;
                scriptUsageChart.data.labels = usage.labels || [];
                scriptUsageChart.data.datasets[0].data = usage.data || [];
                scriptUsageChart.update();
            }
            
            // 更新系统资源使用图
            if (stats.resource_usage) {
                const usage = stats.resource_usage;
                resourceUsageChart.data.datasets[0].data = [
                    usage.cpu || 0,
                    usage.memory || 0,
                    usage.disk || 0,
                    usage.network || 0
                ];
                resourceUsageChart.update();
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
