#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全修复数据库问题的脚本
解决所有已知的数据库和导入问题
"""

import os
import re

def fix_all_imports():
    """修复所有导入问题"""
    print("🔧 修复所有导入问题...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 确保在文件开头有正确的导入
        import_lines = [
            'from user_manager import get_sqlite_connection',
            'from user_manager import get_db_connection',
            'from user_manager import add_log',
        ]
        
        # 检查每个导入是否存在
        for import_line in import_lines:
            if import_line not in content:
                # 在第一个import语句后添加
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if line.startswith('import ') and 'os' in line:
                        lines.insert(i + 1, import_line)
                        content = '\n'.join(lines)
                        print(f"✅ 添加导入: {import_line}")
                        break
        
        # 替换所有直接的数据库连接
        replacements = [
            (r'sqlite3\.connect\([^)]+\)', 'get_sqlite_connection()'),
            (r'import sqlite3\s*\n\s*conn = sqlite3\.connect\([^)]+\)', 'conn = get_sqlite_connection()'),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 导入问题修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复导入失败: {str(e)}")
        return False

def add_database_init_function():
    """添加完整的数据库初始化函数"""
    print("\n🔧 添加完整的数据库初始化函数...")
    
    try:
        with open('server.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 完整的数据库初始化函数
        init_function = '''
def init_all_database_tables():
    """初始化所有数据库表"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        cursor = conn.cursor()
        
        # 创建client_updates表（包含所有必要的列）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 检查并添加缺失的列
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0",
            "is_folder_update": "INTEGER DEFAULT 0"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
        
        # 创建API配置表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("所有数据库表初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库表初始化失败: {str(e)}")
        return False
'''
        
        # 检查函数是否已存在
        if 'def init_all_database_tables():' not in content:
            # 在main函数之前插入
            main_pattern = r'(if __name__ == "__main__":)'
            if re.search(main_pattern, content):
                content = re.sub(main_pattern, init_function + r'\n\1', content)
                print("✅ 已添加完整的数据库初始化函数")
            else:
                # 在文件末尾添加
                content += init_function
                print("✅ 已在文件末尾添加完整的数据库初始化函数")
        else:
            print("ℹ️  数据库初始化函数已存在")
        
        # 在服务器启动前添加初始化调用
        if 'init_all_database_tables()' not in content:
            startup_patterns = [
                (r'(logger\.info\("使用 waitress 启动服务器\.\.\."\))', r'init_all_database_tables()\n    \1'),
                (r'(app\.run\()', r'init_all_database_tables()\n    \1'),
                (r'(serve\(app)', r'init_all_database_tables()\n    \1'),
            ]
            
            for pattern, replacement in startup_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    print("✅ 已添加启动时数据库初始化调用")
                    break
        
        with open('server.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ 添加数据库初始化函数失败: {str(e)}")
        return False

def create_enhanced_fix_script():
    """创建增强的修复脚本"""
    print("\n🔧 创建增强的修复脚本...")
    
    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的数据库修复脚本
修复所有已知的数据库问题
"""

import os
import sys
import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_database_path():
    """获取数据库路径"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        
        # 尝试多个可能的数据库位置
        possible_paths = [
            os.path.join(base_path, "server_data.db"),
            os.path.join(os.getcwd(), "server_data.db"),
            os.path.join(base_path, "_internal", "server_data.db"),
            # 在临时目录中查找
            os.path.join(os.environ.get('TEMP', ''), "_MEI*", "server_data.db"),
        ]
        
        # 检查现有数据库
        import glob
        for pattern in possible_paths:
            if '*' in pattern:
                matches = glob.glob(pattern)
                if matches:
                    return matches[0]
            elif os.path.exists(pattern):
                return pattern
        
        # 如果都不存在，使用当前目录
        return os.path.join(os.getcwd(), "server_data.db")
        
    except Exception:
        return os.path.join(os.getcwd(), "server_data.db")

def fix_database_structure():
    """修复数据库结构"""
    db_path = get_database_path()
    logger.info(f"修复数据库: {db_path}")
    
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        conn = sqlite3.connect(db_path, timeout=30.0)
        cursor = conn.cursor()
        
        # 创建client_updates表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 检查现有表结构
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        logger.info(f"现有列: {existing_columns}")
        
        # 添加缺失的列
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0", 
            "is_folder_update": "INTEGER DEFAULT 0",
            "file_path": "TEXT",
            "file_size": "INTEGER",
            "file_hash": "TEXT",
            "download_count": "INTEGER DEFAULT 0",
            "status": "TEXT DEFAULT 'pending'"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"✅ 添加列: {column}")
                    print(f"✅ 添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
                    print(f"⚠️  添加列 {column} 失败: {str(e)}")
        
        # 创建其他必要的表
        tables = {
            "api_configs": """
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                api_name TEXT UNIQUE NOT NULL,
                api_path TEXT NOT NULL,
                api_title TEXT NOT NULL,
                api_description TEXT,
                response_content TEXT NOT NULL,
                is_enabled INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "logs": """
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                ip TEXT,
                action TEXT NOT NULL,
                details TEXT,
                time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            "live_status": """
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                login_time TIMESTAMP,
                last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                voice_play_time INTEGER DEFAULT 0,
                current_script TEXT,
                online_count INTEGER DEFAULT 0,
                danmaku TEXT,
                danmaku_history TEXT,
                voice_history TEXT,
                obs_source TEXT,
                token TEXT,
                ip TEXT,
                machine_code TEXT,
                is_online INTEGER DEFAULT 1
            )
            """,
            "tokens": """
            CREATE TABLE IF NOT EXISTS tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                token TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1,
                ip TEXT,
                machine_code TEXT
            )
            """
        }
        
        for table_name, create_sql in tables.items():
            cursor.execute(create_sql)
            logger.info(f"✅ 确保表存在: {table_name}")
            print(f"✅ 确保表存在: {table_name}")
        
        conn.commit()
        conn.close()
        
        logger.info("数据库结构修复完成")
        print("✅ 数据库结构修复完成！")
        print(f"📁 数据库位置: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"数据库修复失败: {str(e)}")
        print(f"❌ 数据库修复失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始修复数据库结构...")
    print("=" * 50)
    
    success = fix_database_structure()
    
    print("=" * 50)
    if success:
        print("🎉 数据库修复完成！")
        print("💡 现在可以正常运行服务器了")
        print("💡 所有已知的数据库问题都已修复")
    else:
        print("❌ 数据库修复失败！")
        print("💡 请检查错误信息并重试")
    
    input("\\n按回车键退出...")
'''
    
    with open('fix_database_enhanced.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    print("✅ 已创建增强的数据库修复脚本: fix_database_enhanced.py")
    return True

def main():
    """主函数"""
    print("🚀 完全修复数据库问题")
    print("=" * 60)
    
    try:
        # 1. 修复所有导入问题
        if not fix_all_imports():
            return False
        
        # 2. 添加完整的数据库初始化函数
        if not add_database_init_function():
            return False
        
        # 3. 创建增强的修复脚本
        if not create_enhanced_fix_script():
            return False
        
        print("\n" + "=" * 60)
        print("🎉 完全修复完成！")
        print("\n📝 修复内容:")
        print("✅ 修复了所有导入问题")
        print("✅ 添加了完整的数据库初始化")
        print("✅ 创建了增强的修复脚本")
        print("✅ 解决了数据库表结构问题")
        
        print("\n💡 下一步:")
        print("1. 重新打包程序")
        print("2. 如果还有问题，运行 fix_database_enhanced.py")
        print("3. 全面测试所有功能")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修复过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("按回车键退出...")
        sys.exit(1)
