服务器更新指南（最终版）
====================

1. 将更新包上传到服务器
   - 将 "AI主播系统_更新包_1.9.0_最终版.zip" 上传到服务器的 "/static/downloads/" 目录

2. 更新数据库中的更新信息
   - 使用以下SQL语句更新数据库中的更新信息：

```sql
UPDATE client_updates 
SET version = '1.9.0', 
    description = '1. 修复了更新下载速度慢的问题\n2. 优化了更新界面\n3. 增强了系统稳定性\n4. 改进了快速下载模块\n5. 修复了UI显示问题\n6. 修复了更新后重复提示更新的问题\n7. 优化了版本管理机制', 
    download_url = '/static/downloads/AI主播系统_更新包_1.9.0_最终版.zip',
    fast_download_url = '/api/fast-download/AI主播系统_更新包_1.9.0_最终版.zip'
WHERE is_current = 1;
```

   - 或者使用以下Python代码更新数据库：

```python
import sqlite3

conn = sqlite3.connect('server_data.db')
cursor = conn.cursor()

cursor.execute('''
UPDATE client_updates 
SET version = ?, 
    description = ?, 
    download_url = ?,
    fast_download_url = ?
WHERE is_current = 1
''', (
    '1.9.0', 
    '1. 修复了更新下载速度慢的问题\n2. 优化了更新界面\n3. 增强了系统稳定性\n4. 改进了快速下载模块\n5. 修复了UI显示问题\n6. 修复了更新后重复提示更新的问题\n7. 优化了版本管理机制', 
    '/static/downloads/AI主播系统_更新包_1.9.0_最终版.zip',
    '/api/fast-download/AI主播系统_更新包_1.9.0_最终版.zip'
))

conn.commit()
conn.close()
print('更新成功')
```

3. 重启服务器
   - 重启服务器以使更新生效

4. 测试更新
   - 使用旧版本的客户端测试更新功能
   - 确保客户端能够检测到新版本并成功下载安装更新
   - 确保更新后重新登录时显示正确的更新成功消息，并且不再提示更新

5. 重要说明
   - 此版本修复了更新后重复提示更新的问题
   - 更新系统现在会将版本号保存在config.json文件中
   - 只有当服务器版本与config.json中的版本不一致时才会进行更新
   - 更新完成后会自动更新config.json中的版本号
