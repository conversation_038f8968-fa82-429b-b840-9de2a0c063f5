/* 管理员后台样式 */
body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: #2196F3;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.header h1 {
    margin: 0;
    font-size: 24px;
}

.header .user-info {
    display: flex;
    align-items: center;
}

.header .user-info span {
    margin-right: 15px;
}

.header .logout-btn {
    background-color: #F44336;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.sidebar {
    width: 250px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 70px;
    bottom: 0;
    left: 0;
    overflow-y: auto;
}

.sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar li {
    border-bottom: 1px solid #eee;
}

.sidebar a {
    display: block;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.sidebar a:hover, .sidebar a.active {
    background-color: #e3f2fd;
    color: #2196F3;
}

.sidebar a i {
    margin-right: 10px;
}

.main-content {
    margin-left: 250px;
    padding: 20px;
}

.card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background-color: #f9f9f9;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.card-body {
    padding: 20px;
}

.btn {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    border: none;
    transition: background-color 0.3s;
}

.btn-primary {
    background-color: #2196F3;
    color: white;
}

.btn-success {
    background-color: #4CAF50;
    color: white;
}

.btn-danger {
    background-color: #F44336;
    color: white;
}

.btn-warning {
    background-color: #FFC107;
    color: #333;
}

.btn:hover {
    opacity: 0.9;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    background-color: #f9f9f9;
    font-weight: bold;
}

.table tr:hover {
    background-color: #f5f5f5;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 5px;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
    text-decoration: none;
    border: 1px solid #ddd;
}

.pagination a.active {
    background-color: #2196F3;
    color: white;
    border-color: #2196F3;
}

.pagination a:hover:not(.active) {
    background-color: #f5f5f5;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 4px;
    width: 50%;
    max-width: 500px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
}

.modal-header {
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.modal-close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.modal-footer {
    padding-top: 15px;
    border-top: 1px solid #eee;
    margin-top: 15px;
    text-align: right;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.alert-success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.alert-danger {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.alert-warning {
    background-color: #fcf8e3;
    color: #8a6d3b;
    border: 1px solid #faebcc;
}

.alert-info {
    background-color: #d9edf7;
    color: #31708f;
    border: 1px solid #bce8f1;
}

.dashboard-stats {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    margin: 10px;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card.users {
    background-color: #e3f2fd;
    color: #2196F3;
}

.stat-card.cards {
    background-color: #e8f5e9;
    color: #4CAF50;
}

.stat-card.expired {
    background-color: #ffebee;
    color: #F44336;
}

.stat-card.logs {
    background-color: #fff8e1;
    color: #FFC107;
}

.stat-card h3 {
    font-size: 16px;
    margin-top: 0;
}

.stat-card .number {
    font-size: 36px;
    font-weight: bold;
    margin: 10px 0;
}

/* 登录页面样式 */
.login-container {
    max-width: 400px;
    margin: 100px auto;
    padding: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2196F3;
}

.login-container .form-group {
    margin-bottom: 20px;
}

.login-container .btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: static;
        margin-bottom: 20px;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .modal-content {
        width: 90%;
    }
    
    .dashboard-stats {
        flex-direction: column;
    }
}
