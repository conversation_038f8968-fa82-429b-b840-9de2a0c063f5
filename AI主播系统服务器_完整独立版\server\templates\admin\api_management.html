<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API管理</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        .container {
            max-width: 1400px;
            padding: 0;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .table th {
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .btn-refresh {
            margin-bottom: 20px;
        }
        .api-path {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.9em;
        }
        .status-badge {
            font-size: 0.8em;
        }
        .json-editor {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .table-responsive {
            max-height: 600px;
            overflow-y: auto;
        }
        .modal-lg {
            max-width: 800px;
        }
        .form-label {
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
            <li><a href="/admin/api_management" class="active"><i class="fas fa-code"></i> API管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>API接口管理</h1>
                <div>
                    <button id="addApiBtn" class="btn btn-success me-2">
                        <i class="bi bi-plus-circle"></i> 新增API
                    </button>
                    <button id="refreshBtn" class="btn btn-primary me-2">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-code"></i> API接口列表
                        <small class="text-light ms-2">管理系统API接口的返回内容</small>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>接口名称</th>
                                    <th>接口路径</th>
                                    <th>接口标题</th>
                                    <th>描述</th>
                                    <th>状态</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="apiTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增API配置模态框 -->
    <div class="modal fade" id="addApiModal" tabindex="-1" aria-labelledby="addApiModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addApiModalLabel">新增API配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addApiForm">
                        <div class="mb-3">
                            <label for="addApiName" class="form-label">API名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addApiName" required>
                            <div class="form-text">只能包含字母、数字和下划线，用于内部标识</div>
                        </div>

                        <div class="mb-3">
                            <label for="addApiPath" class="form-label">API路径 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addApiPath" placeholder="/api/your_api_name" required>
                            <div class="form-text">API的访问路径，如：/api/custom_api</div>
                        </div>

                        <div class="mb-3">
                            <label for="addApiTitle" class="form-label">API标题 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="addApiTitle" required>
                            <div class="form-text">API的显示名称</div>
                        </div>

                        <div class="mb-3">
                            <label for="addApiDescription" class="form-label">API描述</label>
                            <textarea class="form-control" id="addApiDescription" rows="2"></textarea>
                            <div class="form-text">API的功能描述</div>
                        </div>

                        <div class="mb-3">
                            <label for="addApiEnabled" class="form-label">启用状态</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="addApiEnabled" checked>
                                <label class="form-check-label" for="addApiEnabled">启用此API接口</label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="addResponseContent" class="form-label">返回内容 (JSON格式) <span class="text-danger">*</span></label>
                            <textarea class="form-control json-editor" id="addResponseContent" rows="15" required></textarea>
                            <div class="form-text">请输入有效的JSON格式数据</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" id="createApiBtn">创建API</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑API配置模态框 -->
    <div class="modal fade" id="editApiModal" tabindex="-1" aria-labelledby="editApiModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editApiModalLabel">编辑API配置</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editApiForm">
                        <input type="hidden" id="editApiId">
                        
                        <div class="mb-3">
                            <label for="editApiName" class="form-label">接口名称</label>
                            <input type="text" class="form-control" id="editApiName" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editApiPath" class="form-label">接口路径</label>
                            <input type="text" class="form-control" id="editApiPath" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editApiTitle" class="form-label">接口标题</label>
                            <input type="text" class="form-control" id="editApiTitle" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editApiDescription" class="form-label">接口描述</label>
                            <textarea class="form-control" id="editApiDescription" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editApiEnabled" class="form-label">启用状态</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="editApiEnabled">
                                <label class="form-check-label" for="editApiEnabled">启用此API接口</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="editResponseContent" class="form-label">返回内容 (JSON格式)</label>
                            <textarea class="form-control json-editor" id="editResponseContent" rows="15" required></textarea>
                            <div class="form-text">请输入有效的JSON格式数据</div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveApiBtn">保存配置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/admin.js"></script>
    <script>
        let apiConfigs = [];

        // 退出登录功能
        document.getElementById('logout-btn').addEventListener('click', function() {
            fetch('/admin/logout', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    window.location.href = '/admin';
                } else {
                    alert('退出登录失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('退出登录出错:', error);
                alert('退出登录出错，请重试');
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // 加载API配置列表
            loadApiConfigs();

            // 刷新按钮事件
            document.getElementById('refreshBtn').addEventListener('click', loadApiConfigs);

            // 新增API按钮事件
            document.getElementById('addApiBtn').addEventListener('click', showAddApiModal);

            // 创建API按钮事件
            document.getElementById('createApiBtn').addEventListener('click', createApiConfig);

            // 保存API配置按钮事件
            document.getElementById('saveApiBtn').addEventListener('click', saveApiConfig);

            // 设置默认JSON内容
            document.getElementById('addResponseContent').value = JSON.stringify({
                "status": "success",
                "message": "API调用成功",
                "data": {
                    "title": "示例标题",
                    "content": "这是示例内容"
                }
            }, null, 2);
        });

        function loadApiConfigs() {
            fetch('/admin/api/configs')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        apiConfigs = data.数据;
                        renderApiTable();
                    } else {
                        alert('加载API配置失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('加载API配置出错:', error);
                    alert('加载API配置出错，请重试');
                });
        }

        function renderApiTable() {
            const tbody = document.getElementById('apiTableBody');
            tbody.innerHTML = '';

            apiConfigs.forEach(config => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${config.id}</td>
                    <td>${config.api_name}</td>
                    <td><code class="api-path">${config.api_path}</code></td>
                    <td>${config.api_title}</td>
                    <td>${config.api_description || '-'}</td>
                    <td>
                        <span class="badge ${config.is_enabled ? 'bg-success' : 'bg-danger'} status-badge">
                            ${config.is_enabled ? '启用' : '禁用'}
                        </span>
                    </td>
                    <td>${formatDateTime(config.updated_at)}</td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="editApiConfig(${config.id})">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-info ms-1" onclick="testApiConfig('${config.api_path}')">
                            <i class="bi bi-play"></i> 测试
                        </button>
                        <button class="btn btn-sm btn-danger ms-1" onclick="deleteApiConfig(${config.id}, '${config.api_name}')">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function editApiConfig(configId) {
            const config = apiConfigs.find(c => c.id === configId);
            if (!config) {
                alert('配置不存在');
                return;
            }

            // 填充表单
            document.getElementById('editApiId').value = config.id;
            document.getElementById('editApiName').value = config.api_name;
            document.getElementById('editApiPath').value = config.api_path;
            document.getElementById('editApiTitle').value = config.api_title;
            document.getElementById('editApiDescription').value = config.api_description || '';
            document.getElementById('editApiEnabled').checked = config.is_enabled;
            
            // 格式化JSON内容
            try {
                const formattedJson = JSON.stringify(JSON.parse(config.response_content), null, 2);
                document.getElementById('editResponseContent').value = formattedJson;
            } catch (e) {
                document.getElementById('editResponseContent').value = config.response_content;
            }

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('editApiModal'));
            modal.show();
        }

        function saveApiConfig() {
            const configId = document.getElementById('editApiId').value;
            const apiTitle = document.getElementById('editApiTitle').value;
            const apiDescription = document.getElementById('editApiDescription').value;
            const responseContent = document.getElementById('editResponseContent').value;
            const isEnabled = document.getElementById('editApiEnabled').checked;

            // 验证JSON格式
            try {
                JSON.parse(responseContent);
            } catch (e) {
                alert('返回内容不是有效的JSON格式，请检查');
                return;
            }

            const data = {
                api_title: apiTitle,
                api_description: apiDescription,
                response_content: responseContent,
                is_enabled: isEnabled ? 1 : 0
            };

            fetch(`/admin/api/configs/${configId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('API配置保存成功');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editApiModal'));
                    modal.hide();
                    // 重新加载列表
                    loadApiConfigs();
                } else {
                    alert('保存API配置失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('保存API配置出错:', error);
                alert('保存API配置出错，请重试');
            });
        }

        function testApiConfig(apiPath) {
            // 在新窗口中打开API接口进行测试
            const testUrl = window.location.origin + apiPath;
            window.open(testUrl, '_blank');
        }

        function showAddApiModal() {
            // 清空表单
            document.getElementById('addApiForm').reset();
            document.getElementById('addApiEnabled').checked = true;

            // 设置默认JSON内容
            document.getElementById('addResponseContent').value = JSON.stringify({
                "status": "success",
                "message": "API调用成功",
                "data": {
                    "title": "示例标题",
                    "content": "这是示例内容"
                }
            }, null, 2);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('addApiModal'));
            modal.show();
        }

        function createApiConfig() {
            const apiName = document.getElementById('addApiName').value.trim();
            const apiPath = document.getElementById('addApiPath').value.trim();
            const apiTitle = document.getElementById('addApiTitle').value.trim();
            const apiDescription = document.getElementById('addApiDescription').value.trim();
            const responseContent = document.getElementById('addResponseContent').value.trim();
            const isEnabled = document.getElementById('addApiEnabled').checked;

            // 验证必填字段
            if (!apiName) {
                alert('API名称不能为空');
                return;
            }
            if (!apiPath) {
                alert('API路径不能为空');
                return;
            }
            if (!apiTitle) {
                alert('API标题不能为空');
                return;
            }
            if (!responseContent) {
                alert('返回内容不能为空');
                return;
            }

            // 验证API名称格式
            if (!/^[a-zA-Z0-9_]+$/.test(apiName)) {
                alert('API名称只能包含字母、数字和下划线');
                return;
            }

            // 验证JSON格式
            try {
                JSON.parse(responseContent);
            } catch (e) {
                alert('返回内容不是有效的JSON格式，请检查');
                return;
            }

            const data = {
                api_name: apiName,
                api_path: apiPath,
                api_title: apiTitle,
                api_description: apiDescription,
                response_content: responseContent,
                is_enabled: isEnabled ? 1 : 0
            };

            fetch('/admin/api/configs', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('API配置创建成功');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('addApiModal'));
                    modal.hide();
                    // 重新加载列表
                    loadApiConfigs();
                } else {
                    alert('创建API配置失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('创建API配置出错:', error);
                alert('创建API配置出错，请重试');
            });
        }

        function deleteApiConfig(configId, apiName) {
            if (!confirm(`确定要删除API配置 "${apiName}" 吗？\n\n删除后将无法恢复，且对应的API接口将立即停止服务。`)) {
                return;
            }

            fetch(`/admin/api/configs/${configId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('API配置删除成功');
                    // 重新加载列表
                    loadApiConfigs();
                } else {
                    alert('删除API配置失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('删除API配置出错:', error);
                alert('删除API配置出错，请重试');
            });
        }

        function formatDateTime(dateTimeStr) {
            if (!dateTimeStr) return '-';
            try {
                const date = new Date(dateTimeStr);
                return date.toLocaleString('zh-CN');
            } catch (e) {
                return dateTimeStr;
            }
        }
    </script>
</body>
</html>
