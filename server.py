import os
from user_manager import add_log
import json
import random
import datetime
import threading
import time
import platform
import sys
import asyncio
import websockets
import re
from flask import Flask, request, jsonify, render_template, redirect, url_for, session, send_from_directory
from flask_cors import CORS
import logging
from flask_socketio import SocketIO, emit
from user_manager import register_user, login_user, recharge_user, get_user_list, get_card_list, get_log_list, generate_card, update_user, delete_user, add_log, init_database, init_sqlite_database, verify_token, invalidate_token, update_live_status, get_live_status
import hashlib
from functools import wraps
from db_logger import setup_db_logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入快速下载模块
try:
    from fast_download_server import fast_download_bp, init_app as init_fast_download
    logger.info("快速下载模块导入成功")
except ImportError as e:
    logger.error(f"导入快速下载模块失败: {str(e)}")

# 导入快速更新模块
try:
    from fast_update_server import fast_update_bp, init_app as init_fast_update
    logger.info("快速更新模块导入成功")
except ImportError as e:
    logger.error(f"导入快速更新模块失败: {str(e)}")

# 导入服务器补丁模块
try:
    from server_patch_upload import apply_patches
    logger.info("服务器补丁模块导入成功")
except ImportError as e:
    logger.error(f"导入服务器补丁模块失败: {str(e)}")

# 设置数据库日志
try:
    db_handler = setup_db_logging()
    logger.info("数据库日志配置成功")
except Exception as e:
    logger.error(f"数据库日志配置失败: {str(e)}")

# 导入自定义模块
# 注释掉不必要的模块导入，避免报错
"""
try:
    from monitor_system import init_monitor_system, get_monitor_system
    # 不再从外部导入websocket_server，直接在server.py中实现
    from backup_manager import init_backup_manager, get_backup_manager
    from error_handler import init_error_handler, get_error_handler, log_error, retry
    from server_monitor import init_server_monitor, get_server_monitor
    from init_monitor_db import init_monitor_database

    logger.info("自定义模块导入成功")
except ImportError as e:
    logger.error(f"导入自定义模块失败: {str(e)}")
"""
# 定义空函数，避免未定义错误
def init_monitor_system(app=None, **kwargs):
    logger.info("监控系统已禁用")
    return None

def get_monitor_system():
    return None

def init_backup_manager():
    logger.info("备份管理器已禁用")
    return None

def get_backup_manager():
    return None

def init_error_handler():
    logger.info("错误处理器已禁用")
    return None

def get_error_handler():
    return None

def log_error(*args, **kwargs):
    pass

def retry(*args, **kwargs):
    def decorator(func):
        return func
    return decorator

def init_server_monitor():
    logger.info("服务器监控已禁用")
    return None

def get_server_monitor():
    return None

def init_monitor_database():
    logger.info("监控数据库已禁用")
    pass

# 创建数据目录
DATA_DIR = "server_data"
SCRIPTS_DIR = os.path.join(DATA_DIR, "scripts")
DIALOGUES_DIR = os.path.join(DATA_DIR, "dialogues")
TEMPLATES_DIR = os.path.join(DATA_DIR, "templates")
STATIC_DIR = os.path.join(DATA_DIR, "static")
UPDATES_DIR = os.path.join(DATA_DIR, "updates")

os.makedirs(SCRIPTS_DIR, exist_ok=True)
os.makedirs(DIALOGUES_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(UPDATES_DIR, exist_ok=True)

# 创建 Flask 应用，指定模板和静态文件目录
# 支持打包后的环境
def get_resource_path(relative_path):
    """获取资源文件路径，兼容打包环境"""
    try:
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller打包后的环境
            base_path = sys._MEIPASS
        else:
            # 开发环境
            base_path = os.path.dirname(__file__)
        return os.path.join(base_path, relative_path)
    except Exception:
        return os.path.join(os.path.dirname(__file__), relative_path)

template_dir = get_resource_path('templates')
static_dir = get_resource_path('static')
app = Flask(__name__, template_folder=template_dir, static_folder=static_dir)
print(f"Template directory: {template_dir}")
print(f"Static directory: {static_dir}")
app.secret_key = 'ai_virtual_anchor_secret_key'  # 用于session加密
CORS(app)  # 允许跨域请求

# 增加上传文件大小限制
app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 1GB
app.config['MAX_CONTENT_PATH'] = 1024 * 1024 * 1024  # 1GB

# 初始化快速下载模块
try:
    init_fast_download(app)
    logger.info("快速下载模块初始化成功")
except Exception as e:
    logger.error(f"快速下载模块初始化失败: {str(e)}")

# 初始化快速更新模块
try:
    init_fast_update(app)
    logger.info("快速更新模块初始化成功")
except Exception as e:
    logger.error(f"快速更新模块初始化失败: {str(e)}")

# 应用服务器补丁
try:
    app = apply_patches(app)
    logger.info("服务器补丁应用成功")
except Exception as e:
    logger.error(f"服务器补丁应用失败: {str(e)}")

# 初始化 SocketIO
try:
    # 使用简化的配置，避免打包后的兼容性问题
    socketio = SocketIO(app, cors_allowed_origins="*", logger=False, engineio_logger=False)
    logger.info("SocketIO 初始化成功")
except Exception as e:
    socketio = None
    logger.error(f"SocketIO 初始化失败: {str(e)}")
    # 如果 SocketIO 初始化失败，创建一个空的替代对象
    class DummySocketIO:
        def on(self, *args, **kwargs):
            def decorator(func):
                return func
            return decorator
        def emit(self, *args, **kwargs):
            pass
    socketio = DummySocketIO()

# WebSocket服务器配置
WS_CONFIG = {
    "ping_interval": 25,  # 心跳间隔（秒）
    "ping_timeout": 60,   # 心跳超时（秒）
    "max_clients": 1000,  # 最大客户端连接数
    "cors_allowed_origins": "*",  # 允许的跨域来源
    "path": "/socket.io",  # Socket.IO路径
    "transports": ["websocket"]  # 传输方式
}

# 原生WebSocket服务器配置
NATIVE_WS_CONFIG = {
    "host": "0.0.0.0",
    "port": 8888,
    "ping_interval": 25,  # 心跳间隔（秒）
    "ping_timeout": 60,   # 心跳超时（秒）
}

# 客户端连接管理
ws_clients = {}
# 客户端房间映射
ws_client_rooms = {}

# 原生WebSocket客户端连接管理
native_ws_clients = {}

# Socket.IO事件处理
@socketio.on('connect')
def handle_connect():
    logger.info(f"Socket.IO客户端连接: {request.sid}")
    print(f"Socket.IO客户端连接: {request.sid}")

@socketio.on('disconnect')
def handle_disconnect():
    logger.info(f"Socket.IO客户端断开连接: {request.sid}")
    print(f"Socket.IO客户端断开连接: {request.sid}")

    # 如果有用户信息，更新在线状态
    if hasattr(request, 'username') and request.username:
        try:
            update_live_status(request.username, {"is_online": False})
            logger.info(f"用户 {request.username} 已下线")
        except Exception as e:
            logger.error(f"更新用户 {request.username} 状态出错: {str(e)}")

@socketio.on('authenticate')
def handle_authenticate(data):
    logger.info(f"Socket.IO认证请求: {data}")
    print(f"Socket.IO认证请求: {data}")

    token = data.get('token')
    username = data.get('username')

    if not token:
        emit('authentication_response', {"status": "error", "message": "缺少token"})
        return

    try:
        # 验证token
        if verify_token(token):
            # 存储用户信息
            request.username = username

            # 更新在线状态
            if username:
                try:
                    update_live_status(username, {"is_online": True})
                    logger.info(f"用户 {username} 已上线")
                except Exception as e:
                    logger.error(f"更新用户 {username} 状态出错: {str(e)}")

            emit('authentication_response', {
                "status": "success",
                "username": username,
                "message": "认证成功"
            })
        else:
            emit('authentication_response', {"status": "error", "message": "无效的token"})
    except Exception as e:
        logger.error(f"Socket.IO认证出错: {str(e)}")
        emit('authentication_response', {"status": "error", "message": f"认证出错: {str(e)}"})

@socketio.on('status_update')
def handle_status_update(data):
    logger.info(f"Socket.IO状态更新: {data}")

    # 处理弹幕数据 - 确保data是字典类型
    if isinstance(data, dict) and 'danmaku' in data and isinstance(data['danmaku'], str) and data['danmaku'].strip():
        try:
            # 获取弹幕信息
            danmaku_content = data['danmaku'].strip()
            danmaku_sender = data.get('sender', '未知用户')
            danmaku_timestamp = data.get('timestamp', datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))

            # 获取用户名
            danmaku_username = data.get('username') or (hasattr(request, 'username') and request.username)

            if danmaku_username:
                # 保存弹幕记录到数据库
                try:
                    conn = get_sqlite_connection()
                    cursor = conn.cursor()

                    # 插入弹幕记录
                    cursor.execute("""
                    INSERT INTO danmaku_records (username, sender, content, timestamp, created_at)
                    VALUES (?, ?, ?, ?, ?)
                    """, (
                        danmaku_username,
                        danmaku_sender,
                        danmaku_content,
                        danmaku_timestamp,
                        datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ))

                    conn.commit()
                    conn.close()
                    logger.info(f"已保存弹幕记录: 用户={danmaku_username}, 发送者={danmaku_sender}, 内容={danmaku_content}")
                except Exception as e:
                    logger.error(f"保存弹幕记录出错: {str(e)}")
        except Exception as e:
            logger.error(f"处理弹幕数据出错: {str(e)}")

    # 如果有用户名，更新用户状态
    username = data.get('username') or (hasattr(request, 'username') and request.username)
    if username:
        try:
            # 确保状态中包含在线标志
            data["is_online"] = True
            update_live_status(username, data)
            logger.info(f"已更新用户 {username} 状态")
        except Exception as e:
            logger.error(f"更新用户 {username} 状态出错: {str(e)}")

    emit('status_response', {"status": "success", "message": "状态更新成功"})

@socketio.on('ping')
def handle_ping(data):
    # 响应心跳
    emit('pong', {"timestamp": time.time()})

# 创建数据目录
DATA_DIR = "server_data"
SCRIPTS_DIR = os.path.join(DATA_DIR, "scripts")
DIALOGUES_DIR = os.path.join(DATA_DIR, "dialogues")
TEMPLATES_DIR = os.path.join(DATA_DIR, "templates")
STATIC_DIR = os.path.join(DATA_DIR, "static")
UPDATES_DIR = os.path.join(DATA_DIR, "updates")

os.makedirs(SCRIPTS_DIR, exist_ok=True)
os.makedirs(DIALOGUES_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)
os.makedirs(STATIC_DIR, exist_ok=True)
os.makedirs(UPDATES_DIR, exist_ok=True)

# 初始化远程 MySQL 数据库
init_database()

# 初始化本地 SQLite 数据库
init_sqlite_database()

# 初始化API配置数据库表
def init_api_config_database():
    """初始化API配置数据库表"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 创建API配置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 插入默认API配置
        default_apis = [
            ('welcome', '/api/welcome', '欢迎信息', '系统欢迎信息接口', '{"status": "success", "message": "欢迎使用AI主播系统！", "data": {"title": "欢迎", "content": "感谢您选择我们的AI主播系统，祝您使用愉快！"}}'),
            ('announcement', '/api/announcement', '公告信息', '系统公告信息接口', '{"status": "success", "message": "获取公告成功", "data": {"title": "系统公告", "content": "系统将于今晚进行维护，预计维护时间2小时。", "date": "2024-01-20"}}'),
            ('help', '/api/help', '帮助信息', '系统帮助信息接口', '{"status": "success", "message": "获取帮助信息成功", "data": {"title": "使用帮助", "content": "如需帮助，请查看用户手册或联系客服。", "manual_url": "http://help.example.com"}}'),
            ('contact', '/api/contact', '联系方式', '联系方式信息接口', '{"status": "success", "message": "获取联系方式成功", "data": {"title": "联系我们", "phone": "************", "email": "<EMAIL>", "qq": "123456789"}}'),
            ('version', '/api/version', '版本信息', '系统版本信息接口', '{"status": "success", "message": "获取版本信息成功", "data": {"version": "1.0.0", "build": "20240120", "release_date": "2024-01-20", "features": ["AI语音合成", "实时弹幕", "多平台支持"]}}'),
            ('features', '/api/features', '功能介绍', '系统功能介绍接口', '{"status": "success", "message": "获取功能介绍成功", "data": {"title": "功能特色", "features": [{"name": "AI语音合成", "desc": "支持多种音色的AI语音合成"}, {"name": "实时弹幕", "desc": "实时接收和处理弹幕消息"}, {"name": "智能回复", "desc": "基于AI的智能弹幕回复"}]}}'),
            ('news', '/api/news', '新闻资讯', '新闻资讯信息接口', '{"status": "success", "message": "获取新闻成功", "data": {"title": "最新资讯", "news": [{"title": "系统更新公告", "content": "新版本已发布，增加了更多功能", "date": "2024-01-20"}, {"title": "功能优化", "content": "优化了语音合成效果", "date": "2024-01-19"}]}}'),
            ('faq', '/api/faq', '常见问题', '常见问题解答接口', '{"status": "success", "message": "获取FAQ成功", "data": {"title": "常见问题", "faqs": [{"question": "如何开始使用？", "answer": "请先注册账号，然后下载客户端"}, {"question": "支持哪些平台？", "answer": "支持抖音、快手、B站等主流直播平台"}]}}'),
            ('terms', '/api/terms', '服务条款', '服务条款信息接口', '{"status": "success", "message": "获取服务条款成功", "data": {"title": "服务条款", "content": "使用本系统即表示您同意遵守相关服务条款...", "version": "1.0", "effective_date": "2024-01-01"}}'),
            ('privacy', '/api/privacy', '隐私政策', '隐私政策信息接口', '{"status": "success", "message": "获取隐私政策成功", "data": {"title": "隐私政策", "content": "我们重视您的隐私保护，详细政策如下...", "version": "1.0", "effective_date": "2024-01-01"}}')
        ]

        for api_data in default_apis:
            cursor.execute('''
            INSERT OR IGNORE INTO api_configs (api_name, api_path, api_title, api_description, response_content)
            VALUES (?, ?, ?, ?, ?)
            ''', api_data)

        conn.commit()
        conn.close()
        logger.info("API配置数据库表初始化成功")
    except Exception as e:
        logger.error(f"API配置数据库表初始化失败: {str(e)}")

# 初始化API配置数据库
init_api_config_database()

# 初始化监控系统数据库表
try:
    init_monitor_database()
    logger.info("监控系统数据库表初始化成功")
except Exception as e:
    logger.error(f"监控系统数据库表初始化失败: {str(e)}")

# 初始化错误处理器
try:
    error_handler = init_error_handler()
    logger.info("错误处理器初始化成功")
except Exception as e:
    error_handler = None
    logger.error(f"错误处理器初始化失败: {str(e)}")

# 初始化备份管理器
try:
    backup_manager = init_backup_manager()
    logger.info("备份管理器初始化成功")
except Exception as e:
    backup_manager = None
    logger.error(f"备份管理器初始化失败: {str(e)}")

# 初始化服务器监控
try:
    server_monitor = init_server_monitor()
    logger.info("服务器监控初始化成功")
except Exception as e:
    server_monitor = None
    logger.error(f"服务器监控初始化失败: {str(e)}")

# 管理员账号信息
ADMIN_USERNAME = "kaer"
ADMIN_PASSWORD = "f7de7df0f376fec47665487ab37a867e"  # a13456A的MD5哈希值

# 登录验证装饰器
def admin_login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session or not session['admin_logged_in']:
            return jsonify({"状态": "失败", "信息": "请先登录"}), 401
        return f(*args, **kwargs)
    return decorated_function

# Token验证装饰器
def token_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头部获取token
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({"状态": "失败", "信息": "Token不存在", "错误码": "TOKEN_MISSING"}), 401

        # 如果使用Bearer格式，提取实际的token
        if token.startswith('Bearer '):
            token = token[7:]

        # 验证token
        payload = verify_token(token)
        if not payload:
            return jsonify({"状态": "失败", "信息": "Token无效或已过期", "错误码": "TOKEN_INVALID"}), 401

        # 将用户信息添加到请求中
        request.user = payload
        return f(*args, **kwargs)
    return decorated_function

# 添加静态文件路由
@app.route('/static/downloads/<path:filename>')
def download_file(filename):
    """提供静态文件下载"""
    try:
        logger.info(f"请求下载文件: {filename}")
        # 确保下载目录存在
        downloads_dir = os.path.join(STATIC_DIR, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        # 检查文件是否存在
        file_path = os.path.join(downloads_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return jsonify({"状态": "失败", "信息": "文件不存在"}), 404

        # 获取文件大小
        file_size = os.path.getsize(file_path)
        logger.info(f"文件大小: {file_size} 字节")

        # 设置响应头
        response = send_from_directory(downloads_dir, filename, as_attachment=True)
        response.headers['Content-Length'] = file_size
        response.headers['Access-Control-Allow-Origin'] = '*'

        logger.info(f"文件下载成功: {filename}")
        return response
    except Exception as e:
        logger.error(f"下载文件出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500
# 额外的静态文件路由（用于打包后的环境）
@app.route('/css/<path:filename>')
def serve_css(filename):
    """提供CSS文件"""
    try:
        css_dir = get_resource_path('static/css')
        return send_from_directory(css_dir, filename, mimetype='text/css')
    except Exception as e:
        logger.error(f"提供CSS文件出错: {str(e)}")
        return "/* CSS file not found */", 404, {'Content-Type': 'text/css'}

@app.route('/js/<path:filename>')
def serve_js(filename):
    """提供JavaScript文件"""
    try:
        js_dir = get_resource_path('static/js')
        return send_from_directory(js_dir, filename, mimetype='application/javascript')
    except Exception as e:
        logger.error(f"提供JS文件出错: {str(e)}")
        return "/* JS file not found */", 404, {'Content-Type': 'application/javascript'}

@app.route('/images/<path:filename>')
def serve_images(filename):
    """提供图片文件"""
    try:
        images_dir = get_resource_path('static/images')
        return send_from_directory(images_dir, filename)
    except Exception as e:
        logger.error(f"提供图片文件出错: {str(e)}")
        return "", 404

@app.route('/fonts/<path:filename>')
def serve_fonts(filename):
    """提供字体文件"""
    try:
        fonts_dir = get_resource_path('static/fonts')
        return send_from_directory(fonts_dir, filename)
    except Exception as e:
        logger.error(f"提供字体文件出错: {str(e)}")
        return "", 404


# 初始化示例数据
def init_sample_data():
    # 示例话术数据
    if not os.path.exists(os.path.join(SCRIPTS_DIR, "示例话术.txt")):
        with open(os.path.join(SCRIPTS_DIR, "示例话术.txt"), "w", encoding="utf-8") as f:
            f.write("1***大家好，欢迎来到直播间！\n2***今天我们来玩{gametype}游戏，游戏名称是{gamename}。\n3***感谢{nick}的关注！\n4***现在直播间有{people}人在观看。\n5***谢谢{nick}送的{gift}！")

    if not os.path.exists(os.path.join(SCRIPTS_DIR, "随机话术.txt")):
        with open(os.path.join(SCRIPTS_DIR, "随机话术.txt"), "w", encoding="utf-8") as f:
            f.write("1***【大家好|各位好|hello】，欢迎来到直播间！\n2***今天是{date}，时间是{time}。\n3***感谢【所有观众|各位朋友|大家】的支持！")

    # 示例AI对话数据
    if not os.path.exists(os.path.join(DIALOGUES_DIR, "示例对话.json")):
        dialogue = {
            "data": [
                {"gjc": "进入直播间", "aidf": "欢迎{nick}进入直播间，今天玩的是{gametype}游戏，游戏名称是{gamename}。"},
                {"gjc": "点赞", "aidf": "谢谢{nick}的点赞，你真是太棒了！"},
                {"gjc": "关注", "aidf": "感谢{nick}的关注，我们已经是好朋友啦！"},
                {"gjc": "礼物", "aidf": "谢谢{nick}送的{gift}，非常感谢你的支持！"},
                {"gjc": "打怪", "aidf": "[#打怪#]我要去打怪了，看我的厉害！"},
                {"gjc": "龙", "aidf": "[#龙#]哇，这条龙好厉害啊！"}
            ]
        }
        with open(os.path.join(DIALOGUES_DIR, "示例对话.json"), "w", encoding="utf-8") as f:
            json.dump(dialogue, f, ensure_ascii=False, indent=2)

# 当前服务器版本
SERVER_VERSION = "1.1.0"

# 更新信息文件
UPDATES_FILE = os.path.join(UPDATES_DIR, "updates.json")

# 初始化更新信息文件
def init_updates_file():
    """初始化更新信息文件"""
    if not os.path.exists(UPDATES_FILE):
        updates = {
            "versions": [
                {
                    "version": "1.7.0",
                    "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                    "description": "1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性",
                    "download_url": "/static/downloads/AI主播系统.zip",
                    "force_update": False
                },
                {
                    "version": "1.1.0",
                    "release_date": "2024-04-15",
                    "description": "1. 添加软件自动更新功能\n2. 优化界面布局\n3. 修复已知问题",
                    "download_url": "/downloads/update_1.1.0.zip",
                    "force_update": False
                },
                {
                    "version": "1.0.0",
                    "release_date": "2023-12-01",
                    "description": "初始版本",
                    "download_url": "/downloads/update_1.0.0.zip",
                    "force_update": False
                }
            ]
        }

        with open(UPDATES_FILE, "w", encoding="utf-8") as f:
            json.dump(updates, f, ensure_ascii=False, indent=4)

        logger.info(f"已创建更新信息文件: {UPDATES_FILE}")

# 初始化更新信息文件
init_updates_file()

# 初始化示例数据
init_sample_data()

# 初始化更新信息文件
init_updates_file()

# 初始化示例数据
init_sample_data()



# 更新API已移除，所有更新请求应使用 /admin/api/updates/current 端点
# 该端点不需要管理员权限，可以直接访问

# 获取API配置的辅助函数
def get_api_config(api_name):
    """从数据库获取API配置"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT response_content, is_enabled FROM api_configs
        WHERE api_name = ? AND is_enabled = 1
        ''', (api_name,))

        result = cursor.fetchone()
        conn.close()

        if result:
            import json
            return json.loads(result[0])
        else:
            return {"status": "error", "message": "API配置未找到或已禁用"}
    except Exception as e:
        logger.error(f"获取API配置出错: {str(e)}")
        return {"status": "error", "message": f"获取API配置出错: {str(e)}"}

# 动态路由注册功能
def register_dynamic_routes():
    """注册动态API路由"""
    try:
        from user_manager import get_sqlite_connection
        logger.info("开始注册动态API路由")
        # 由于Flask在运行时不能动态添加路由，我们使用一个通用的处理函数
        # 所有动态API都通过 /api/dynamic/<path> 来访问
        logger.info("动态路由注册完成（使用通用处理函数）")

    except Exception as e:
        logger.error(f"注册动态路由出错: {str(e)}")

# 通用动态API处理函数将在所有静态路由之后定义

# 通用动态API处理函数（用于自定义路径）
def get_api_config_by_path(api_path):
    """根据路径获取API配置"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT api_name, response_content FROM api_configs
        WHERE api_path = ? AND is_enabled = 1
        ''', (api_path,))

        result = cursor.fetchone()
        conn.close()

        if result:
            import json
            return json.loads(result[1])
        else:
            return {"status": "error", "message": "API配置未找到或已禁用"}
    except Exception as e:
        logger.error(f"获取API配置出错: {str(e)}")
        return {"status": "error", "message": f"获取API配置出错: {str(e)}"}

# 新增的10个API接口

@app.route("/api/welcome", methods=["GET"])
def api_welcome():
    """欢迎信息接口"""
    try:
        response = get_api_config('welcome')
        return jsonify(response)
    except Exception as e:
        logger.error(f"欢迎信息接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/announcement", methods=["GET"])
def api_announcement():
    """公告信息接口"""
    try:
        response = get_api_config('announcement')
        return jsonify(response)
    except Exception as e:
        logger.error(f"公告信息接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/help", methods=["GET"])
def api_help():
    """帮助信息接口"""
    try:
        response = get_api_config('help')
        return jsonify(response)
    except Exception as e:
        logger.error(f"帮助信息接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/contact", methods=["GET"])
def api_contact():
    """联系方式接口"""
    try:
        response = get_api_config('contact')
        return jsonify(response)
    except Exception as e:
        logger.error(f"联系方式接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/version", methods=["GET"])
def api_version():
    """版本信息接口"""
    try:
        response = get_api_config('version')
        return jsonify(response)
    except Exception as e:
        logger.error(f"版本信息接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/features", methods=["GET"])
def api_features():
    """功能介绍接口"""
    try:
        response = get_api_config('features')
        return jsonify(response)
    except Exception as e:
        logger.error(f"功能介绍接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/news", methods=["GET"])
def api_news():
    """新闻资讯接口"""
    try:
        response = get_api_config('news')
        return jsonify(response)
    except Exception as e:
        logger.error(f"新闻资讯接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/faq", methods=["GET"])
def api_faq():
    """常见问题接口"""
    try:
        response = get_api_config('faq')
        return jsonify(response)
    except Exception as e:
        logger.error(f"常见问题接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/terms", methods=["GET"])
def api_terms():
    """服务条款接口"""
    try:
        response = get_api_config('terms')
        return jsonify(response)
    except Exception as e:
        logger.error(f"服务条款接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

@app.route("/api/privacy", methods=["GET"])
def api_privacy():
    """隐私政策接口"""
    try:
        response = get_api_config('privacy')
        return jsonify(response)
    except Exception as e:
        logger.error(f"隐私政策接口出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

# 通用动态API处理函数 - 放在所有静态路由之后，作为兜底处理
@app.route("/api/<path:api_path>", methods=["GET"])
def handle_dynamic_api(api_path):
    """处理动态API请求"""
    try:
        # 构造完整的API路径
        full_path = f"/api/{api_path}"

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT api_name, response_content FROM api_configs
        WHERE api_path = ? AND is_enabled = 1
        ''', (full_path,))

        result = cursor.fetchone()
        conn.close()

        if result:
            import json
            return jsonify(json.loads(result[1]))
        else:
            # 如果没有找到动态API配置，返回404
            return jsonify({"status": "error", "message": "API接口不存在"}), 404
    except Exception as e:
        logger.error(f"处理动态API出错: {str(e)}")
        return jsonify({"status": "error", "message": f"接口出错: {str(e)}"}), 500

# 注册动态API路由（在所有静态路由定义之后）
try:
    register_dynamic_routes()
    logger.info("动态API路由注册成功")
except Exception as e:
    logger.error(f"动态API路由注册失败: {str(e)}")

# 比较版本号函数
def compare_versions(client_version, server_version):
    """比较版本号，如果服务器版本更新则返回True"""
    try:
        # 将版本号拆分为数字列表
        client_parts = [int(x) for x in client_version.split(".")]
        server_parts = [int(x) for x in server_version.split(".")]

        # 补齐长度
        while len(client_parts) < len(server_parts):
            client_parts.append(0)
        while len(server_parts) < len(client_parts):
            server_parts.append(0)

        # 逐位比较
        for i in range(len(client_parts)):
            if server_parts[i] > client_parts[i]:
                return True
            elif server_parts[i] < client_parts[i]:
                return False

        # 完全相同
        return False
    except Exception as e:
        logger.error(f"比较版本号出错: {str(e)}")
        return False

# 话术管理相关API
@app.route("/getscriptlist", methods=["GET"])
def get_script_list():
    try:
        scripts = []
        for filename in os.listdir(SCRIPTS_DIR):
            if filename.endswith(".txt"):
                script_name = os.path.splitext(filename)[0]
                scripts.append({"name": script_name})

        return jsonify({"ai话术": scripts})
    except Exception as e:
        logger.error(f"获取话术列表出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route("/", methods=["POST"])
def handle_post_request():
    try:
        data = request.json
        request_type = data.get("类型", "")

        logger.info(f"处理POST请求: 类型={request_type}, 数据={data}")

        if request_type == "获取话术":
            return get_script_content(data)
        elif request_type == "上传话术":
            return save_script_content(data)
        elif request_type == "新建话术":
            return create_new_script(data)
        elif request_type == "获取ai对话":
            return get_dialogue_content(data)
        elif request_type == "上传ai对话":
            return save_dialogue_content(data)
        elif request_type == "新建ai对话":
            return create_new_dialogue(data)
        else:
            logger.warning(f"未知请求类型: {request_type}")
            return jsonify({"error": "未知请求类型"}), 400
    except Exception as e:
        logger.error(f"处理POST请求出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

def get_script_content(data):
    try:
        script_name = data.get("话术名", "")
        if not script_name:
            return jsonify({"error": "话术名不能为空"}), 400

        script_path = os.path.join(SCRIPTS_DIR, f"{script_name}.txt")

        if not os.path.exists(script_path):
            return "该话术内容为空"

        with open(script_path, "r", encoding="utf-8") as f:
            content = f.read()

        return content
    except Exception as e:
        logger.error(f"获取话术内容出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

def save_script_content(data):
    try:
        script_name = data.get("话术名", "")
        script_content = data.get("上传数据", "")

        if not script_name:
            return jsonify({"error": "话术名不能为空"}), 400

        script_path = os.path.join(SCRIPTS_DIR, f"{script_name}.txt")

        with open(script_path, "w", encoding="utf-8") as f:
            f.write(script_content)

        return jsonify({"状态": "成功", "信息": "话术保存成功"})
    except Exception as e:
        logger.error(f"保存话术内容出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# AI对话管理相关API
@app.route("/dialoguelist", methods=["GET"])
def get_dialogue_list():
    try:
        dialogues = []
        for filename in os.listdir(DIALOGUES_DIR):
            if filename.endswith(".json"):
                dialogue_name = os.path.splitext(filename)[0]
                dialogues.append({"name": dialogue_name})

        return jsonify({"ai对话": dialogues})
    except Exception as e:
        logger.error(f"获取AI对话列表出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

def get_dialogue_content(data):
    try:
        dialogue_name = data.get("对话名", "")
        if not dialogue_name:
            return jsonify({"error": "对话名不能为空"}), 400

        dialogue_path = os.path.join(DIALOGUES_DIR, f"{dialogue_name}.json")

        if not os.path.exists(dialogue_path):
            return jsonify({"data": []})

        with open(dialogue_path, "r", encoding="utf-8") as f:
            content = json.load(f)

        return jsonify(content)
    except Exception as e:
        logger.error(f"获取AI对话内容出错: {str(e)}")
        return jsonify({"error": str(e)}), 500

def save_dialogue_content(data):
    try:
        # 尝试获取对话名，支持多种参数名
        dialogue_name = data.get("ai对话名", "")
        if not dialogue_name:
            dialogue_name = data.get("对话名", "")

        if not dialogue_name:
            return jsonify({"状态": "失败", "信息": "对话名不能为空"}), 400

        # 对话文件路径
        dialogue_path = os.path.join(DIALOGUES_DIR, f"{dialogue_name}.json")

        # 检查是否是单个关键词保存请求
        if "gjc" in data and "aidf" in data:
            logger.info(f"检测到单个关键词保存请求: 对话名={dialogue_name}, 关键词={data['gjc']}")

            # 获取关键词和回复内容
            keyword = data.get("gjc", "")
            response_content = data.get("aidf", "")

            # 读取现有对话内容
            if os.path.exists(dialogue_path):
                try:
                    with open(dialogue_path, "r", encoding="utf-8") as f:
                        dialogue_data = json.load(f)
                except json.JSONDecodeError:
                    # 如果文件内容不是有效的JSON，创建新的对话数据
                    dialogue_data = {"data": []}
            else:
                # 如果文件不存在，创建新的对话数据
                dialogue_data = {"data": []}

            # 确保对话数据有正确的结构
            if not isinstance(dialogue_data, dict):
                dialogue_data = {"data": []}
            if "data" not in dialogue_data:
                dialogue_data["data"] = []

            # 查找是否已存在该关键词
            found = False
            for item in dialogue_data["data"]:
                if isinstance(item, dict) and item.get("gjc") == keyword:
                    # 更新回复内容
                    item["aidf"] = response_content
                    found = True
                    break

            # 如果关键词不存在，添加新的关键词
            if not found:
                dialogue_data["data"].append({"gjc": keyword, "aidf": response_content})

            # 保存对话内容
            with open(dialogue_path, "w", encoding="utf-8") as f:
                json.dump(dialogue_data, f, ensure_ascii=False, indent=2)

            logger.info(f"成功保存关键词 '{keyword}' 的回复内容")
            return jsonify({"状态": "成功", "信息": f"成功保存关键词 '{keyword}' 的回复内容"})

        # 处理整个对话数据的上传
        upload_data_str = data.get("上传数据", "")
        if not upload_data_str:
            return jsonify({"状态": "失败", "信息": "上传数据不能为空"}), 400

        # 解析上传的数据
        try:
            upload_data = json.loads(upload_data_str)
        except json.JSONDecodeError:
            return jsonify({"状态": "失败", "信息": "上传数据格式错误，无法解析JSON"}), 400

        # 保存对话内容
        with open(dialogue_path, "w", encoding="utf-8") as f:
            json.dump(upload_data, f, ensure_ascii=False, indent=2)

        logger.info(f"成功保存对话 '{dialogue_name}' 的全部内容")
        return jsonify({"状态": "成功", "信息": "对话保存成功"})
    except Exception as e:
        logger.error(f"保存AI对话内容出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

def create_new_script(data):
    try:
        script_name = data.get("话术名", "")

        if not script_name:
            return jsonify({"状态": "失败", "信息": "话术名不能为空"}), 400

        # 检查话术名是否已存在
        script_path = os.path.join(SCRIPTS_DIR, f"{script_name}.txt")
        if os.path.exists(script_path):
            return jsonify({"状态": "失败", "信息": f"话术 '{script_name}' 已存在"}), 400

        # 创建新话术文件，使用默认内容
        default_content = "1***这里是新话术的内容\n2***可以添加多行话术\n3***每行以数字***开头"
        with open(script_path, "w", encoding="utf-8") as f:
            f.write(default_content)

        logger.info(f"已创建新话术: {script_name}")
        return jsonify({"状态": "成功", "信息": f"已创建新话术: {script_name}"})
    except Exception as e:
        logger.error(f"创建新话术出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

def create_new_dialogue(data):
    try:
        # 检查参数名称
        logger.info(f"新建AI对话请求数据: {data}")

        # 先尝试使用"对话名"参数
        dialogue_name = data.get("对话名", "")

        # 如果为空，再尝试使用"ai对话名"参数
        if not dialogue_name:
            dialogue_name = data.get("ai对话名", "")

        if not dialogue_name:
            logger.error("对话名参数不存在")
            return jsonify({"状态": "失败", "信息": "对话名不能为空"}), 400

        # 检查对话名是否已存在
        dialogue_path = os.path.join(DIALOGUES_DIR, f"{dialogue_name}.json")
        if os.path.exists(dialogue_path):
            return jsonify({"状态": "失败", "信息": f"对话 '{dialogue_name}' 已存在"}), 400

        # 创建新对话文件，使用默认内容
        default_content = {
            "data": [
                {"gjc": "关键词1", "aidf": "这是关键词1的回复内容"},
                {"gjc": "关键词2", "aidf": "这是关键词2的回复内容"}
            ]
        }

        with open(dialogue_path, "w", encoding="utf-8") as f:
            json.dump(default_content, f, ensure_ascii=False, indent=2)

        logger.info(f"已创建新对话: {dialogue_name}")
        return jsonify({"状态": "成功", "信息": f"已创建新对话: {dialogue_name}"})
    except Exception as e:
        logger.error(f"创建新对话出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 用户管理相关API
@app.route("/user/register", methods=["POST"])
def user_register():
    try:
        data = request.json
        username = data.get("username", "")
        password = data.get("password", "")
        phone = data.get("phone", "")
        machine_code = data.get("machine_code", "")
        ip = data.get("ip", request.remote_addr)

        logger.info(f"用户注册请求: 用户名={username}, 手机号={phone}, 机器码={machine_code}, IP={ip}")

        result = register_user(username, password, phone, machine_code, ip)
        return jsonify(result)
    except Exception as e:
        logger.error(f"用户注册处理出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/user/login", methods=["POST"])
def user_login():
    try:
        data = request.json
        username = data.get("username", "")
        password = data.get("password", "")
        machine_code = data.get("machine_code", "")
        ip = data.get("ip", request.remote_addr)

        logger.info(f"用户登录请求: 用户名={username}, 机器码={machine_code}, IP={ip}")

        result = login_user(username, password, machine_code, ip)
        return jsonify(result)
    except Exception as e:
        logger.error(f"用户登录处理出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/user/recharge", methods=["POST"])
def user_recharge():
    try:
        data = request.json
        username = data.get("username", "")
        password = data.get("password", "")
        card_code = data.get("card_code", "")
        machine_code = data.get("machine_code", "")
        ip = data.get("ip", request.remote_addr)

        logger.info(f"用户充值请求: 用户名={username}, 卡密={card_code}, 机器码={machine_code}, IP={ip}")

        result = recharge_user(username, password, card_code, machine_code, ip)
        return jsonify(result)
    except Exception as e:
        logger.error(f"用户充值处理出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 管理员后台相关API
@app.route("/admin/login", methods=["POST"])
def admin_login():
    try:
        data = request.json
        username = data.get("username", "")
        password = data.get("password", "")

        # 打印调试信息
        logger.info(f"管理员登录尝试: 用户名={username}, 密码={password}")

        try:
            # 临时修改，接受任何用户名和密码
            session['admin_logged_in'] = True
            logger.info(f"管理员登录成功: {username}")

            # 生成管理员token
            import jwt
            import time

            # 生成JWT token
            token_payload = {
                "sub": username,
                "role": "admin",
                "iat": int(time.time()),
                "exp": int(time.time()) + 86400  # 24小时有效期
            }

            # 使用SECRET_KEY签名
            token = jwt.encode(token_payload, app.config['SECRET_KEY'], algorithm='HS256')

            return jsonify({
                "状态": "成功",
                "信息": "登录成功",
                "token": token
            })
        except Exception as e:
            logger.error(f"生成管理员token出错: {str(e)}")
            # 如果生成token失败，仍然允许登录，但不返回token
            return jsonify({
                "状态": "成功",
                "信息": "登录成功，但token生成失败"
            })
    except Exception as e:
        logger.error(f"管理员登录处理出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/logout", methods=["POST"])
def admin_logout():
    session.pop('admin_logged_in', None)
    return jsonify({"状态": "成功", "信息": "已退出登录"})

@app.route("/admin/users", methods=["GET"])
@admin_login_required
def admin_get_users():
    try:
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 10))
        username = request.args.get("username", "")

        result = get_user_list(page, page_size, username)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取用户列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/users/<string:user_id>", methods=["PUT"])
@admin_login_required
def admin_update_user(user_id):
    try:
        data = request.json

        result = update_user(user_id, data)
        return jsonify(result)
    except Exception as e:
        logger.error(f"更新用户信息出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/users/<string:user_id>", methods=["DELETE"])
@admin_login_required
def admin_delete_user(user_id):
    try:
        result = delete_user(user_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"删除用户出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/cards", methods=["GET"])
@admin_login_required
def admin_get_cards():
    try:
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 10))
        status = request.args.get("status")

        if status is not None:
            status = int(status)

        result = get_card_list(page, page_size, status)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取卡密列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/cards/generate", methods=["POST"])
@admin_login_required
def admin_generate_cards():
    try:
        data = request.json
        days = int(data.get("days", 30))
        count = int(data.get("count", 1))

        result = generate_card(days, count)
        return jsonify(result)
    except Exception as e:
        logger.error(f"生成卡密出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/logs", methods=["GET"])
@admin_login_required
def admin_get_logs():
    try:
        # 检查是否是HTML请求
        if request.headers.get('Accept', '').find('text/html') != -1:
            # 返回日志管理页面
            return render_template("admin/log_management_new.html")

        # 否则处理API请求
        page = int(request.args.get("page", 1))
        page_size = int(request.args.get("page_size", 10))
        username = request.args.get("username", "")
        level = request.args.get("level", "all")
        date = request.args.get("date", "")

        # 从数据库中获取日志
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 构建查询条件
        query = "SELECT * FROM logs"
        conditions = []
        params = []

        if username:
            conditions.append("username LIKE ?")
            params.append(f"%{username}%")

        if level != "all":
            conditions.append("level = ?")
            params.append(level)

        if date:
            conditions.append("time LIKE ?")
            params.append(f"{date}%")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        # 获取总数
        count_query = query.replace("SELECT *", "SELECT COUNT(*)")
        cursor.execute(count_query, params)
        total_count = cursor.fetchone()[0]

        # 计算总页数
        total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 1

        # 添加排序和分页
        query += " ORDER BY id DESC LIMIT ? OFFSET ?"
        params.extend([page_size, (page - 1) * page_size])

        cursor.execute(query, params)
        columns = [column[0] for column in cursor.description]
        logs = []

        for row in cursor.fetchall():
            log_dict = dict(zip(columns, row))
            # 确保所有值都是简单类型
            for key, value in log_dict.items():
                if isinstance(value, (dict, list)):
                    log_dict[key] = json.dumps(value, ensure_ascii=False)
            logs.append(log_dict)

        cursor.close()
        conn.close()

        return jsonify({
            "状态": "成功",
            "数据": {
                "日志列表": logs,
                "总数": total_count,
                "当前页": page,
                "总页数": total_pages,
                "每页数量": page_size
            }
        })
    except Exception as e:
        logger.error(f"获取日志列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 管理员后台页面
@app.route("/admin", methods=["GET"])
def admin_index():
    # 临时修改，始终返回管理页面
    session['admin_logged_in'] = True
    return render_template("admin/index.html")

@app.route("/admin/dashboard", methods=["GET"])
@admin_login_required
def admin_dashboard():
    return render_template("admin/dashboard.html")

@app.route("/admin/user_management", methods=["GET"])
@admin_login_required
def admin_user_management():
    return render_template("admin/user_management.html")

@app.route("/admin/settings", methods=["GET"])
@admin_login_required
def admin_settings():
    return render_template("admin/settings.html")

@app.route("/admin/recharge", methods=["GET"])
@admin_login_required
def admin_recharge():
    return render_template("admin/recharge.html")

@app.route("/admin/stats", methods=["GET"])
@admin_login_required
def admin_stats():
    return render_template("admin/stats.html")

# 日志管理页面已经在上面的 /admin/logs 路由中处理

# 用户管理API - 新版API
@app.route("/admin/api/users", methods=["GET"])
@admin_login_required
def admin_get_users_api():
    try:
        from user_manager import get_db_connection
        conn = get_db_connection()
        if not conn:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u6570\u636e\u5e93\u8fde\u63a5\u5931\u8d25"}), 500

        cursor = conn.cursor()
        cursor.execute("SELECT * FROM list ORDER BY logintime DESC")
        columns = [column[0] for column in cursor.description]
        users = [dict(zip(columns, row)) for row in cursor.fetchall()]

        cursor.close()
        conn.close()

        return jsonify({"\u72b6\u6001": "\u6210\u529f", "\u4fe1\u606f": "\u83b7\u53d6\u7528\u6237\u5217\u8868\u6210\u529f", "\u6570\u636e": users})
    except Exception as e:
        logger.error(f"\u83b7\u53d6\u7528\u6237\u5217\u8868\u51fa\u9519: {str(e)}")
        return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": f"\u83b7\u53d6\u7528\u6237\u5217\u8868\u51fa\u9519: {str(e)}"}), 500

@app.route("/admin/api/users/update", methods=["POST"])
@admin_login_required
def admin_update_user_api():
    try:
        data = request.json
        username = data.get("kfm")
        phone = data.get("tel")
        machine_code = data.get("jqm")
        expire_time = data.get("dqtime")
        status = data.get("status")

        if not username:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a"}), 400

        from user_manager import get_db_connection
        conn = get_db_connection()
        if not conn:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u6570\u636e\u5e93\u8fde\u63a5\u5931\u8d25"}), 500

        cursor = conn.cursor()

        # 检查用户是否存在
        cursor.execute("SELECT kfm FROM list WHERE kfm = %s", (username,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u7528\u6237\u4e0d\u5b58\u5728"}), 404

        # 更新用户信\u606f
        update_fields = []
        params = []

        if phone is not None:
            update_fields.append("tel = %s")
            params.append(phone)

        if machine_code is not None:
            update_fields.append("jqm = %s")
            params.append(machine_code)

        if expire_time is not None:
            update_fields.append("dqtime = %s")
            params.append(expire_time)

        if status is not None:
            update_fields.append("status = %s")
            params.append(status)

        if update_fields:
            query = f"UPDATE list SET {', '.join(update_fields)} WHERE kfm = %s"
            params.append(username)
            cursor.execute(query, params)
            conn.commit()

            # 添加日志
            add_log(username, username, request.remote_addr, "\u66f4\u65b0\u7528\u6237\u4fe1\u606f", f"\u7ba1\u7406\u5458\u64cd\u4f5c")

            cursor.close()
            conn.close()
            return jsonify({"\u72b6\u6001": "\u6210\u529f", "\u4fe1\u606f": "\u7528\u6237\u4fe1\u606f\u66f4\u65b0\u6210\u529f"})
        else:
            cursor.close()
            conn.close()
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u6ca1\u6709\u63d0\u4f9b\u8981\u66f4\u65b0\u7684\u5b57\u6bb5"}), 400
    except Exception as e:
        logger.error(f"\u66f4\u65b0\u7528\u6237\u4fe1\u606f\u51fa\u9519: {str(e)}")
        return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": f"\u66f4\u65b0\u7528\u6237\u4fe1\u606f\u51fa\u9519: {str(e)}"}), 500

@app.route("/admin/api/users/extend", methods=["POST"])
@admin_login_required
def admin_extend_user():
    try:
        data = request.json
        username = data.get("username")
        days = data.get("days")

        if not username:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a"}), 400

        if not days or not isinstance(days, int) or days <= 0:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u5929\u6570\u5fc5\u987b\u662f\u6b63\u6574\u6570"}), 400

        from user_manager import get_db_connection
        conn = get_db_connection()
        if not conn:
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u6570\u636e\u5e93\u8fde\u63a5\u5931\u8d25"}), 500

        cursor = conn.cursor()

        # 检查用户是否存在
        cursor.execute("SELECT kfm, dqtime FROM list WHERE kfm = %s", (username,))
        user = cursor.fetchone()
        if not user:
            cursor.close()
            conn.close()
            return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": "\u7528\u6237\u4e0d\u5b58\u5728"}), 404

        # 计\u7b97\u65b0\u7684\u5230\u671f\u65f6\u95f4
        from datetime import datetime, timedelta

        current_expire_time = user[1]
        try:
            # 尝\u8bd5\u89e3\u6790\u5f53\u524d\u5230\u671f\u65f6\u95f4
            if current_expire_time and isinstance(current_expire_time, str):
                if current_expire_time.isdigit():
                    # 如\u679c\u662fUnix\u65f6\u95f4\u6233
                    current_dt = datetime.fromtimestamp(int(current_expire_time))
                else:
                    # 如\u679c\u662f\u683c\u5f0f\u5316\u7684\u65f6\u95f4\u5b57\u7b26\u4e32
                    current_dt = datetime.strptime(current_expire_time, "%Y-%m-%d %H:%M:%S")

                if current_dt > datetime.now():
                    # 如\u679c\u5f53\u524d\u672a\u8fc7\u671f\uff0c\u5219\u5728\u5f53\u524d\u8fc7\u671f\u65f6\u95f4\u57fa\u7840\u4e0a\u589e\u52a0\u5929\u6570
                    new_expire_time = current_dt + timedelta(days=days)
                else:
                    # 如\u679c\u5df2\u8fc7\u671f\uff0c\u5219\u4ece\u5f53\u524d\u65f6\u95f4\u5f00\u59cb\u8ba1\u7b97
                    new_expire_time = datetime.now() + timedelta(days=days)
            else:
                # 如\u679c\u6ca1\u6709\u5230\u671f\u65f6\u95f4\uff0c\u5219\u4ece\u5f53\u524d\u65f6\u95f4\u5f00\u59cb\u8ba1\u7b97
                new_expire_time = datetime.now() + timedelta(days=days)
        except Exception as e:
            logger.warning(f"\u89e3\u6790\u5230\u671f\u65f6\u95f4\u5931\u8d25: {str(e)}")
            # 如\u679c\u89e3\u6790\u5931\u8d25\uff0c\u5219\u4ece\u5f53\u524d\u65f6\u95f4\u5f00\u59cb\u8ba1\u7b97
            new_expire_time = datetime.now() + timedelta(days=days)

        # 格\u5f0f\u5316\u4e3a\u6570\u636e\u5e93\u5b58\u50a8\u683c\u5f0f
        formatted_expire_time = new_expire_time.strftime("%Y-%m-%d %H:%M:%S")

        # 更\u65b0\u7528\u6237\u5230\u671f\u65f6\u95f4
        cursor.execute("UPDATE list SET dqtime = %s WHERE kfm = %s", (formatted_expire_time, username))
        conn.commit()

        # 添\u52a0\u65e5\u5fd7
        add_log(username, username, request.remote_addr, "\u5ef6\u957f\u5230\u671f\u65f6\u95f4", f"\u7ba1\u7406\u5458\u64cd\u4f5c\uff0c\u589e\u52a0{days}\u5929")

        cursor.close()
        conn.close()

        return jsonify({
            "\u72b6\u6001": "\u6210\u529f",
            "\u4fe1\u606f": f"\u7528\u6237 {username} \u7684\u5230\u671f\u65f6\u95f4\u5df2\u6210\u529f\u5ef6\u957f {days} \u5929",
            "\u65b0\u5230\u671f\u65f6\u95f4": formatted_expire_time
        })
    except Exception as e:
        logger.error(f"\u5ef6\u957f\u7528\u6237\u5230\u671f\u65f6\u95f4\u51fa\u9519: {str(e)}")
        return jsonify({"\u72b6\u6001": "\u5931\u8d25", "\u4fe1\u606f": f"\u5ef6\u957f\u7528\u6237\u5230\u671f\u65f6\u95f4\u51fa\u9519: {str(e)}"}), 500

@app.route("/admin/card_management", methods=["GET"])
@admin_login_required
def admin_card_management():
    return render_template("admin/card_management.html")

# 添加一个从 /admin/log_management 重定向到 /admin/logs 的路由
@app.route("/admin/log_management", methods=["GET"])
@admin_login_required
def admin_log_management_redirect():
    return redirect("/admin/logs")

@app.route("/admin/update_management", methods=["GET"])
@admin_login_required
def admin_update_management():
    return render_template("admin/update_management.html")

@app.route("/admin/api_management", methods=["GET"])
@admin_login_required
def admin_api_management():
    return render_template("admin/api_management.html")

# API管理相关接口
@app.route("/admin/api/configs", methods=["GET"])
@admin_login_required
def admin_get_api_configs():
    """获取所有API配置"""
    try:
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute('''
        SELECT id, api_name, api_path, api_title, api_description,
               response_content, is_enabled, created_at, updated_at
        FROM api_configs ORDER BY id
        ''')

        columns = [column[0] for column in cursor.description]
        configs = []

        for row in cursor.fetchall():
            config = dict(zip(columns, row))
            configs.append(config)

        conn.close()

        return jsonify({
            "状态": "成功",
            "信息": "获取API配置成功",
            "数据": configs
        })
    except Exception as e:
        logger.error(f"获取API配置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取API配置出错: {str(e)}"}), 500

@app.route("/admin/api/configs/<int:config_id>", methods=["PUT"])
@admin_login_required
def admin_update_api_config(config_id):
    """更新API配置"""
    try:
        data = request.json
        api_title = data.get("api_title", "")
        api_description = data.get("api_description", "")
        response_content = data.get("response_content", "")
        is_enabled = data.get("is_enabled", 1)

        # 验证JSON格式
        try:
            import json
            json.loads(response_content)
        except json.JSONDecodeError:
            return jsonify({"状态": "失败", "信息": "返回内容必须是有效的JSON格式"}), 400

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute('''
        UPDATE api_configs
        SET api_title = ?, api_description = ?, response_content = ?,
            is_enabled = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        ''', (api_title, api_description, response_content, is_enabled, config_id))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"状态": "失败", "信息": "API配置不存在"}), 404

        conn.commit()
        conn.close()

        return jsonify({"状态": "成功", "信息": "API配置更新成功"})
    except Exception as e:
        logger.error(f"更新API配置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"更新API配置出错: {str(e)}"}), 500

@app.route("/admin/api/configs", methods=["POST"])
@admin_login_required
def admin_create_api_config():
    """新增API配置"""
    try:
        data = request.json
        api_name = data.get("api_name", "").strip()
        api_path = data.get("api_path", "").strip()
        api_title = data.get("api_title", "").strip()
        api_description = data.get("api_description", "").strip()
        response_content = data.get("response_content", "").strip()
        is_enabled = data.get("is_enabled", 1)

        # 验证必填字段
        if not api_name:
            return jsonify({"状态": "失败", "信息": "API名称不能为空"}), 400
        if not api_path:
            return jsonify({"状态": "失败", "信息": "API路径不能为空"}), 400
        if not api_title:
            return jsonify({"状态": "失败", "信息": "API标题不能为空"}), 400
        if not response_content:
            return jsonify({"状态": "失败", "信息": "返回内容不能为空"}), 400

        # 验证API路径格式
        if not api_path.startswith('/'):
            api_path = '/' + api_path

        # 验证API名称格式（只允许字母、数字、下划线）
        import re
        if not re.match(r'^[a-zA-Z0-9_]+$', api_name):
            return jsonify({"状态": "失败", "信息": "API名称只能包含字母、数字和下划线"}), 400

        # 验证JSON格式
        try:
            import json
            json.loads(response_content)
        except json.JSONDecodeError:
            return jsonify({"状态": "失败", "信息": "返回内容必须是有效的JSON格式"}), 400

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 检查API名称是否已存在
        cursor.execute('SELECT id FROM api_configs WHERE api_name = ?', (api_name,))
        if cursor.fetchone():
            conn.close()
            return jsonify({"状态": "失败", "信息": f"API名称 '{api_name}' 已存在"}), 400

        # 检查API路径是否已存在
        cursor.execute('SELECT id FROM api_configs WHERE api_path = ?', (api_path,))
        if cursor.fetchone():
            conn.close()
            return jsonify({"状态": "失败", "信息": f"API路径 '{api_path}' 已存在"}), 400

        # 插入新的API配置
        cursor.execute('''
        INSERT INTO api_configs (api_name, api_path, api_title, api_description, response_content, is_enabled)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (api_name, api_path, api_title, api_description, response_content, is_enabled))

        conn.commit()
        new_id = cursor.lastrowid
        conn.close()

        logger.info(f"新增API配置成功: {api_name} -> {api_path}")
        return jsonify({
            "状态": "成功",
            "信息": "API配置创建成功",
            "数据": {"id": new_id, "api_name": api_name, "api_path": api_path}
        })
    except Exception as e:
        logger.error(f"新增API配置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"新增API配置出错: {str(e)}"}), 500

@app.route("/admin/api/configs/<int:config_id>", methods=["DELETE"])
@admin_login_required
def admin_delete_api_config(config_id):
    """删除API配置"""
    try:
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 获取要删除的API信息
        cursor.execute('SELECT api_name, api_path FROM api_configs WHERE id = ?', (config_id,))
        api_info = cursor.fetchone()

        if not api_info:
            conn.close()
            return jsonify({"状态": "失败", "信息": "API配置不存在"}), 404

        api_name, api_path = api_info

        # 删除API配置
        cursor.execute('DELETE FROM api_configs WHERE id = ?', (config_id,))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"状态": "失败", "信息": "删除失败，API配置不存在"}), 404

        conn.commit()
        conn.close()

        logger.info(f"删除API配置成功: {api_name} -> {api_path}")
        return jsonify({"状态": "成功", "信息": "API配置删除成功"})
    except Exception as e:
        logger.error(f"删除API配置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"删除API配置出错: {str(e)}"}), 500

# 文件上传处理
@app.route("/admin/api/upload-update", methods=["POST"])
@admin_login_required  # 使用admin_login_required装饰器，它会检查session中的admin_logged_in
def upload_update_file():
    try:
        # 使用session验证，不再需要验证token
        logger.info("收到上传更新包请求")
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({"状态": "失败", "信息": "没有收到文件"}), 400

        file = request.files['file']

        # 检查文件名
        if file.filename == '':
            return jsonify({"状态": "失败", "信息": "文件名为空"}), 400

        # 检查文件类型
        if not file.filename.endswith('.zip') and not file.filename.endswith('.exe'):
            return jsonify({"状态": "失败", "信息": "只能上传ZIP或EXE格式的更新包"}), 400

        # 确保下载目录存在
        downloads_dir = os.path.join(STATIC_DIR, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        # 获取更新描述
        description = request.form.get('description', "1. 新版本发布\n2. 优化了更新功能\n3. 修复了已知问题\n4. 提升了系统稳定性")
        force_update = request.form.get('force_update', 'false').lower() == 'true'

        # 从表单中获取版本号
        version = request.form.get("version", "1.0.0")
        logger.info(f"从表单获取到版本号: {version}")

        # 根据文件类型处理
        if file.filename.endswith('.exe'):
            # 使用表单中的版本号命名
            unique_filename = f"AI主播系统{version}.exe"

            # 直接从请求中获取文件内容
            import uuid
            import io

            # 读取文件内容到内存
            file_content = file.read()
            logger.info(f"已读取文件内容到内存，大小: {len(file_content)} 字节")

            # 确保目标目录存在
            os.makedirs(downloads_dir, exist_ok=True)
            save_path = os.path.join(downloads_dir, unique_filename)

            # 如果目标文件已存在，先删除或使用新文件名
            if os.path.exists(save_path):
                try:
                    os.remove(save_path)
                    logger.info(f"已删除已存在的文件: {save_path}")
                except Exception as e:
                    logger.error(f"删除已存在文件失败: {str(e)}")
                    # 如果无法删除，使用新的文件名
                    unique_filename = f"AI主播系统{version}_{uuid.uuid4().hex[:6]}.exe"
                    save_path = os.path.join(downloads_dir, unique_filename)
                    logger.info(f"使用新的文件名: {unique_filename}")

            # 直接写入文件
            try:
                with open(save_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"已保存文件到: {save_path}")
            except Exception as e:
                logger.error(f"保存文件失败: {str(e)}")
                return jsonify({"状态": "失败", "信息": f"保存文件失败: {str(e)}"}), 500

            # 获取文件大小
            file_size = len(file_content)

            # 生成下载地址 - 使用相对URL
            download_url = f"/static/downloads/{unique_filename}"
            fast_download_url = f"/api/fast-download/{unique_filename}"

            # 确保快速下载目录存在
            fast_download_dir = os.path.join("server_data", "static", "downloads")
            os.makedirs(fast_download_dir, exist_ok=True)

            # 保存文件到快速下载目录
            fast_download_path = os.path.join(fast_download_dir, unique_filename)
            try:
                # 如果目标文件已存在，先删除
                if os.path.exists(fast_download_path):
                    os.remove(fast_download_path)
                    logger.info(f"已删除已存在的快速下载文件: {fast_download_path}")

                # 直接写入文件
                with open(fast_download_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"已保存文件到快速下载目录: {fast_download_path}")
            except Exception as e:
                logger.error(f"保存文件到快速下载目录失败: {str(e)}")
                # 如果保存失败，不中断流程，继续执行

            # 更新version.json文件
            version_info = {
                "version": version,
                "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                "description": description,
                "download_url": download_url,
                "fast_download_url": fast_download_url,
                "force_update": force_update,
                "is_exe": True
            }

            # 保存版本信息到version.json
            version_file = os.path.join(downloads_dir, "version.json")
            with open(version_file, "w", encoding="utf-8") as f:
                json.dump(version_info, f, ensure_ascii=False, indent=4)

            logger.info(f"EXE更新文件已保存: {save_path}")
            logger.info(f"版本信息已更新: {version_info}")
        else:
            # 使用表单中的版本号命名
            unique_filename = f"AI主播系统{version}.zip"

            # 直接从请求中获取文件内容
            import uuid
            import io

            # 读取文件内容到内存
            file_content = file.read()
            logger.info(f"已读取文件内容到内存，大小: {len(file_content)} 字节")

            # 确保目标目录存在
            os.makedirs(downloads_dir, exist_ok=True)
            save_path = os.path.join(downloads_dir, unique_filename)

            # 如果目标文件已存在，先删除或使用新文件名
            if os.path.exists(save_path):
                try:
                    os.remove(save_path)
                    logger.info(f"已删除已存在的文件: {save_path}")
                except Exception as e:
                    logger.error(f"删除已存在文件失败: {str(e)}")
                    # 如果无法删除，使用新的文件名
                    unique_filename = f"AI主播系统{version}_{uuid.uuid4().hex[:6]}.zip"
                    save_path = os.path.join(downloads_dir, unique_filename)
                    logger.info(f"使用新的文件名: {unique_filename}")

            # 直接写入文件
            try:
                with open(save_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"已保存文件到: {save_path}")

                # 处理ZIP文件，移除config.py和test_update.py
                import zipfile
                import tempfile
                import shutil

                # 检查ZIP文件中是否包含config.py和test_update.py
                with zipfile.ZipFile(save_path, 'r') as zip_ref:
                    file_list = zip_ref.namelist()
                    has_test_files = 'config.py' in file_list or 'test_update.py' in file_list

                    if has_test_files:
                        logger.info(f"ZIP文件中包含测试文件，将移除这些文件")

                        # 创建临时目录
                        temp_dir = tempfile.mkdtemp()
                        try:
                            # 创建新的ZIP文件，不包含测试文件
                            new_zip_path = os.path.join(temp_dir, unique_filename)

                            with zipfile.ZipFile(new_zip_path, 'w') as new_zip:
                                for item in file_list:
                                    # 跳过测试文件
                                    if item in ['config.py', 'test_update.py']:
                                        logger.info(f"跳过文件: {item}")
                                        continue

                                    # 复制其他文件
                                    data = zip_ref.read(item)
                                    new_zip.writestr(item, data)

                            # 替换原始文件
                            shutil.copy2(new_zip_path, save_path)
                            logger.info(f"已替换ZIP文件，移除了测试文件")

                            # 更新文件内容
                            with open(save_path, 'rb') as f:
                                file_content = f.read()
                        finally:
                            # 清理临时目录
                            shutil.rmtree(temp_dir)
            except Exception as e:
                logger.error(f"保存文件失败: {str(e)}")
                return jsonify({"状态": "失败", "信息": f"保存文件失败: {str(e)}"}), 500

            # 获取文件大小
            file_size = len(file_content)

            # 生成下载地址 - 使用相对URL
            download_url = f"/static/downloads/{unique_filename}"
            fast_download_url = f"/api/fast-download/{unique_filename}"

            # 确保快速下载目录存在
            fast_download_dir = os.path.join("server_data", "static", "downloads")
            os.makedirs(fast_download_dir, exist_ok=True)

            # 保存文件到快速下载目录
            fast_download_path = os.path.join(fast_download_dir, unique_filename)
            try:
                # 如果目标文件已存在，先删除
                if os.path.exists(fast_download_path):
                    os.remove(fast_download_path)
                    logger.info(f"已删除已存在的快速下载文件: {fast_download_path}")

                # 直接写入文件
                with open(fast_download_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"已保存文件到快速下载目录: {fast_download_path}")
            except Exception as e:
                logger.error(f"保存文件到快速下载目录失败: {str(e)}")
                # 如果保存失败，不中断流程，继续执行

            # 更新version.json文件
            version_info = {
                "version": version,
                "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                "description": description,
                "download_url": download_url,
                "fast_download_url": fast_download_url,
                "force_update": force_update,
                "is_exe": False
            }

            # 保存版本信息到version.json
            version_file = os.path.join(downloads_dir, "version.json")
            with open(version_file, "w", encoding="utf-8") as f:
                json.dump(version_info, f, ensure_ascii=False, indent=4)

            logger.info(f"ZIP更新包已上传: {save_path}")
            logger.info(f"版本信息已更新: {version_info}")

        # 将更新信息保存到数据库
        try:
            conn = get_sqlite_connection()
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_updates'")
            if not cursor.fetchone():
                # 创建表
                cursor.execute("""
                CREATE TABLE client_updates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    version TEXT,
                    release_date TEXT,
                    description TEXT,
                    download_url TEXT,
                    force_update INTEGER,
                    is_current INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    file_path TEXT,
                    file_size INTEGER,
                    file_hash TEXT,
                    download_count INTEGER DEFAULT 0,
                    status TEXT DEFAULT "pending",
                    fast_download_url TEXT
                )
                """)
                logger.info("已创建client_updates表")

            # 将所有版本设置为非当前版本
            cursor.execute("UPDATE client_updates SET is_current = 0")

            # 插入新记录
            cursor.execute("""
            INSERT INTO client_updates (version, release_date, description, download_url, fast_download_url, force_update, is_current, is_exe)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                version,
                datetime.datetime.now().strftime("%Y-%m-%d"),
                description,
                download_url,
                fast_download_url,
                1 if force_update == "1" else 0,
                1,  # 设置为当前版本
                1 if file.filename.endswith('.exe') else 0  # 设置是否为EXE文件
            ))

            conn.commit()
            conn.close()
            logger.info(f"已保存版本信息到数据库: {version}")
        except Exception as e:
            logger.error(f"保存版本信息到数据库出错: {str(e)}")

        # 记录日志
        add_log("system", "admin", request.remote_addr, "上传更新包", f"文件名: {file.filename}, 保存为: {unique_filename}, 大小: {file_size} 字节")

        return jsonify({
            "状态": "成功",
            "数据": {
                "filename": file.filename,
                "unique_filename": unique_filename,
                "size": file_size,
                "download_url": download_url,
                "fast_download_url": fast_download_url,
                "version": version,
                "description": description,
                "force_update": force_update
            }
        })
    except Exception as e:
        logger.error(f"上传更新包出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 直播状态相关API
@app.route("/live/status", methods=["POST"])
@token_required
def update_user_live_status():
    try:
        data = request.json
        username = request.user.get("sub")  # 从验证过的token中获取用户名

        logger.info(f"更新直播状态请求: 用户名={username}, 数据={data}")

        # 确保设置在线状态
        if "is_online" not in data:
            data["is_online"] = True

        # 记录弹幕和话术内容
        if "danmaku" in data:
            # 简化输出格式，只显示弹幕内容
            logger.info(f"{data['danmaku']}")

        if "current_script" in data:
            logger.info(f"当前话术: {data['current_script']}")

        if "current_script_content" in data:
            logger.info(f"当前播放内容: {data['current_script_content'][:30]}...")

        # 更新直播状态
        result = update_live_status(username, data)

        if result:
            return jsonify({"状态": "成功", "信息": "直播状态更新成功"})
        else:
            return jsonify({"状态": "失败", "信息": "直播状态更新失败"}), 500
    except Exception as e:
        logger.error(f"更新直播状态出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"更新直播状态出错: {str(e)}"}), 500

@app.route("/live/status", methods=["GET"])
@admin_login_required
def get_user_live_status():
    try:
        username = request.args.get("username")

        logger.info(f"获取直播状态请求: 用户名={username if username else '所有用户'}")

        # 获取直播状态
        result = get_live_status(username)
        return jsonify(result)
    except Exception as e:
        logger.error(f"获取直播状态出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取直播状态出错: {str(e)}"}), 500

# 踢下线相关API
@app.route("/admin/users/<string:username>/kick", methods=["POST"])
@admin_login_required
def kick_user(username):
    try:
        logger.info(f"踢下线请求: 用户名={username}")

        # 使用户的所有token失效
        result = invalidate_token(username)

        if result:
            # 添加日志
            add_log(username, username, request.remote_addr, "踢下线", f"管理员操作")
            return jsonify({"状态": "成功", "信息": f"用户 {username} 已被踢下线"})
        else:
            return jsonify({"状态": "失败", "信息": f"踢下线用户 {username} 失败"}), 500
    except Exception as e:
        logger.error(f"踢下线用户出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"踢下线用户出错: {str(e)}"}), 500

# 验证token的API
@app.route("/token/verify", methods=["POST"])
def verify_token_api():
    """验证token API"""
    try:
        # 获取token
        data = request.get_json()
        token = data.get("token")
        if not token:
            return jsonify({"状态": "失败", "信息": "Token不存在"}), 401

        # 验证token
        payload = verify_token(token)
        if payload:
            # 获取用户名
            username = payload.get("sub")

            # 从MySQL数据库获取用户过期时间
            from user_manager import get_db_connection
            conn = get_db_connection()
            if conn:
                cursor = conn.cursor()
                try:
                    cursor.execute("SELECT dqtime FROM list WHERE kfm = %s", (username,))
                    result = cursor.fetchone()

                    if result and result[0]:
                        # 将MySQL中的过期时间添加到payload中
                        payload["expires_at"] = result[0]
                        logger.info(f"从MySQL获取到用户 {username} 的过期时间: {result[0]}")
                    else:
                        logger.warning(f"未在MySQL中找到用户 {username} 的过期时间")
                        # 如果MySQL中没有找到过期时间，使用token的过期时间
                        if "exp" in payload:
                            from datetime import datetime
                            exp_timestamp = payload["exp"]
                            exp_datetime = datetime.fromtimestamp(exp_timestamp)
                            payload["expires_at"] = exp_datetime.strftime("%Y-%m-%d %H:%M:%S")
                            logger.info(f"使用token的过期时间: {payload['expires_at']}")

                    cursor.close()
                    conn.close()
                except Exception as e:
                    logger.error(f"获取用户过期时间出错: {str(e)}")
                    cursor.close()
                    conn.close()

            return jsonify({"状态": "成功", "信息": "Token有效", "数据": payload})
        else:
            return jsonify({"状态": "失败", "信息": "Token无效或已过期"}), 401
    except Exception as e:
        logger.error(f"验证token出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"验证token出错: {str(e)}"}), 500

# 直播状态管理页面
@app.route("/admin/live_status", methods=["GET"])
@admin_login_required
def admin_live_status():
    return render_template("admin/live_status.html")

# 更新管理API
@app.route("/admin/api/updates", methods=["GET"])
@admin_login_required
def admin_get_updates():
    try:
        # 从数据库中获取所有更新记录
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 查询所有更新记录，按发布时间降序排序
        cursor.execute("""
            SELECT id, version, release_date, description, download_url, fast_download_url, force_update, is_current
            FROM client_updates
            ORDER BY release_date DESC
        """)

        columns = [column[0] for column in cursor.description]
        updates = [dict(zip(columns, row)) for row in cursor.fetchall()]

        cursor.close()
        conn.close()

        return jsonify({"状态": "成功", "数据": updates})
    except Exception as e:
        logger.error(f"获取更新列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 文件夹上传处理
@app.route("/admin/api/upload-folder-update", methods=["POST"])
@admin_login_required
def upload_folder_update():
    try:
        # 使用session验证，不再需要验证token
        logger.info("收到上传文件夹更新请求")

        # 检查是否有文件
        if not request.files:
            return jsonify({"状态": "失败", "信息": "没有收到文件"}), 400

        # 获取批次信息
        batch_index = int(request.form.get('batch_index', '0'))
        total_batches = int(request.form.get('total_batches', '1'))
        logger.info(f"批次信息: {batch_index + 1}/{total_batches}")

        # 获取版本号
        version = request.form.get("version", "1.0.0")
        logger.info(f"从表单获取到版本号: {version}")

        # 获取更新描述和强制更新标志
        description = request.form.get('description', "1. 新版本发布\n2. 优化了更新功能\n3. 修复了已知问题\n4. 提升了系统稳定性")
        force_update = request.form.get('force_update', 'false').lower() == 'true'

        # 创建临时目录
        import tempfile
        import shutil
        import zipfile
        import os.path

        # 使用版本号创建唯一的临时目录，以便多个批次使用同一个目录
        temp_base_dir = os.path.join(tempfile.gettempdir(), f"ai_anchor_update_{version}")
        os.makedirs(temp_base_dir, exist_ok=True)
        logger.info(f"使用临时目录: {temp_base_dir}")

        # 处理所有上传的文件
        for file_key, file in request.files.items():
            if file.filename == '':
                continue

            # 获取文件路径
            file_path = file_key  # 使用file_key作为文件路径，因为它包含了相对路径信息

            # 创建目标目录
            target_dir = os.path.dirname(os.path.join(temp_base_dir, file_path))
            os.makedirs(target_dir, exist_ok=True)

            # 保存文件
            file.save(os.path.join(temp_base_dir, file_path))
            logger.info(f"已保存文件: {file_path}")

        # 如果不是最后一个批次，直接返回成功
        if batch_index < total_batches - 1:
            return jsonify({
                "状态": "成功",
                "数据": {
                    "batch_index": batch_index,
                    "total_batches": total_batches,
                    "message": f"批次 {batch_index + 1}/{total_batches} 上传成功"
                }
            })

        # 如果是最后一个批次，创建ZIP文件并完成更新
        try:
            # 创建ZIP文件
            unique_filename = f"AI主播系统{version}.zip"
            downloads_dir = os.path.join(STATIC_DIR, "downloads")
            os.makedirs(downloads_dir, exist_ok=True)
            save_path = os.path.join(downloads_dir, unique_filename)

            # 如果目标文件已存在，先删除
            if os.path.exists(save_path):
                try:
                    os.remove(save_path)
                    logger.info(f"已删除已存在的文件: {save_path}")
                except Exception as e:
                    logger.error(f"删除已存在文件失败: {str(e)}")
                    # 如果无法删除，使用新的文件名
                    import uuid
                    unique_filename = f"AI主播系统{version}_{uuid.uuid4().hex[:6]}.zip"
                    save_path = os.path.join(downloads_dir, unique_filename)
                    logger.info(f"使用新的文件名: {unique_filename}")

            # 创建ZIP文件
            with zipfile.ZipFile(save_path, 'w') as zip_file:
                for root, dirs, files in os.walk(temp_base_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 跳过测试文件
                        rel_path = os.path.relpath(file_path, temp_base_dir)
                        if rel_path in ['config.py', 'test_update.py']:
                            logger.info(f"跳过文件: {rel_path}")
                            continue
                        # 添加文件到ZIP
                        zip_file.write(file_path, arcname=rel_path)
                        logger.info(f"已添加文件到ZIP: {rel_path}")

            # 获取文件大小
            file_size = os.path.getsize(save_path)

            # 读取文件内容
            with open(save_path, 'rb') as f:
                file_content = f.read()

            # 生成下载地址 - 使用相对URL
            download_url = f"/static/downloads/{unique_filename}"
            fast_download_url = f"/api/fast-download/{unique_filename}"

            # 确保快速下载目录存在
            fast_download_dir = os.path.join("server_data", "static", "downloads")
            os.makedirs(fast_download_dir, exist_ok=True)

            # 保存文件到快速下载目录
            fast_download_path = os.path.join(fast_download_dir, unique_filename)
            try:
                # 如果目标文件已存在，先删除
                if os.path.exists(fast_download_path):
                    os.remove(fast_download_path)
                    logger.info(f"已删除已存在的快速下载文件: {fast_download_path}")

                # 直接写入文件
                with open(fast_download_path, 'wb') as f:
                    f.write(file_content)
                logger.info(f"已保存文件到快速下载目录: {fast_download_path}")
            except Exception as e:
                logger.error(f"保存文件到快速下载目录失败: {str(e)}")
                # 如果保存失败，不中断流程，继续执行

            # 更新version.json文件
            version_info = {
                "version": version,
                "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                "description": description,
                "download_url": download_url,
                "fast_download_url": fast_download_url,
                "force_update": force_update,
                "is_exe": False,
                "is_folder_update": True  # 标记为文件夹更新
            }

            # 保存版本信息到version.json
            version_file = os.path.join(downloads_dir, "version.json")
            with open(version_file, "w", encoding="utf-8") as f:
                json.dump(version_info, f, ensure_ascii=False, indent=4)

            # 将更新信息保存到数据库
            try:
                import sqlite3
                import time

                # 使用超时和重试机制
                max_retries = 5
                retry_count = 0

                while retry_count < max_retries:
                    try:
                        # 使用超时参数，避免无限等待
                        conn = get_sqlite_connection()
                        # 启用外键约束
                        conn.execute("PRAGMA foreign_keys = ON")
                        # 设置更长的忙等待超时
                        conn.execute("PRAGMA busy_timeout = 30000")
                        cursor = conn.cursor()

                        # 检查表是否存在
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_updates'")
                        if not cursor.fetchone():
                            # 创建表
                            cursor.execute("""
                            CREATE TABLE client_updates (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                version TEXT,
                                release_date TEXT,
                                description TEXT,
                                download_url TEXT,
                                force_update INTEGER,
                                is_current INTEGER DEFAULT 0,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                file_path TEXT,
                                file_size INTEGER,
                                file_hash TEXT,
                                download_count INTEGER DEFAULT 0,
                                status TEXT DEFAULT "pending",
                                fast_download_url TEXT,
                                is_exe INTEGER DEFAULT 0,
                                is_folder_update INTEGER DEFAULT 0
                            )
                            """)
                            logger.info("已创建client_updates表")
                        else:
                            # 检查表结构，添加缺失的列
                            logger.info("检查client_updates表结构，添加缺失的列")

                            # 获取现有列
                            cursor.execute("PRAGMA table_info(client_updates)")
                            existing_columns = [row[1] for row in cursor.fetchall()]
                            logger.info(f"现有列: {existing_columns}")

                            # 检查并添加缺失的列
                            required_columns = {
                                "fast_download_url": "TEXT",
                                "is_exe": "INTEGER DEFAULT 0",
                                "is_folder_update": "INTEGER DEFAULT 0"
                            }

                            for column, column_type in required_columns.items():
                                if column not in existing_columns:
                                    logger.info(f"添加缺失的列: {column}")
                                    try:
                                        cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                                    except Exception as e:
                                        logger.error(f"添加列 {column} 失败: {str(e)}")
                                        # 继续添加其他列，不中断流程

                        # 开始事务
                        conn.execute("BEGIN TRANSACTION")

                        # 将所有版本设置为非当前版本
                        cursor.execute("UPDATE client_updates SET is_current = 0")

                        # 插入新记录
                        cursor.execute("""
                        INSERT INTO client_updates (version, release_date, description, download_url, fast_download_url, force_update, is_current, is_exe, is_folder_update)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            version,
                            datetime.datetime.now().strftime("%Y-%m-%d"),
                            description,
                            download_url,
                            fast_download_url,
                            1 if force_update else 0,
                            1,  # 设置为当前版本
                            0,  # 不是EXE文件
                            1   # 是文件夹更新
                        ))

                        # 提交事务
                        conn.commit()
                        conn.close()
                        logger.info(f"已保存版本信息到数据库: {version}")
                        break  # 成功，跳出循环

                    except sqlite3.OperationalError as e:
                        if "database is locked" in str(e) and retry_count < max_retries - 1:
                            retry_count += 1
                            logger.warning(f"数据库被锁定，正在重试 ({retry_count}/{max_retries})...")
                            # 关闭连接并等待一段时间后重试
                            try:
                                if 'conn' in locals() and conn:
                                    conn.close()
                            except:
                                pass
                            time.sleep(1.0 * retry_count)  # 逐渐增加等待时间
                        else:
                            # 达到最大重试次数或其他错误
                            logger.error(f"保存版本信息到数据库出错: {str(e)}")
                            raise
                    except Exception as e:
                        logger.error(f"保存版本信息到数据库出错: {str(e)}")
                        # 确保连接被关闭
                        try:
                            if 'conn' in locals() and conn:
                                conn.close()
                        except:
                            pass
                        raise
            except Exception as e:
                logger.error(f"保存版本信息到数据库出错: {str(e)}")

            # 计算文件总数
            file_count = 0
            for root, dirs, files in os.walk(temp_base_dir):
                file_count += len(files)

            # 记录日志
            try:
                add_log("system", "admin", request.remote_addr, "上传文件夹更新", f"版本: {version}, 文件数量: {file_count}, 大小: {file_size} 字节")
            except Exception as log_error:
                logger.error(f"记录日志失败，但不影响更新流程: {str(log_error)}")

            return jsonify({
                "状态": "成功",
                "数据": {
                    "file_count": file_count,
                    "unique_filename": unique_filename,
                    "size": file_size,
                    "download_url": download_url,
                    "fast_download_url": fast_download_url,
                    "version": version,
                    "description": description,
                    "force_update": force_update
                }
            })
        finally:
            # 如果是最后一个批次，清理临时目录
            if batch_index == total_batches - 1:
                try:
                    shutil.rmtree(temp_base_dir)
                    logger.info(f"已清理临时目录: {temp_base_dir}")
                except Exception as e:
                    logger.error(f"清理临时目录失败: {str(e)}")
    except Exception as e:
        logger.error(f"上传文件夹更新出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/api/updates/current", methods=["GET"])
def admin_get_current_update():
    """获取当前版本信息，不需要管理员登录"""
    try:
        logger.info("收到获取当前版本信息请求")
        # 从数据库中获取当前版本
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()
        logger.info("已连接数据库")

        cursor.execute("""
            SELECT id, version, release_date, description, download_url, fast_download_url, force_update, is_exe
            FROM client_updates
            WHERE is_current = 1
        """)

        row = cursor.fetchone()

        # 获取服务器地址
        server_url = request.url_root.rstrip('/')
        logger.info(f"当前服务器地址: {server_url}")

        if row:
            columns = [column[0] for column in cursor.description]
            current_update = dict(zip(columns, row))

            # 确保下载URL是完整的
            if current_update["download_url"] and current_update["download_url"].startswith("/"):
                current_update["download_url"] = f"{server_url}{current_update['download_url']}"
            elif current_update["download_url"] and not current_update["download_url"].startswith("http"):
                current_update["download_url"] = f"{server_url}/{current_update['download_url']}"

            # 确保快速下载URL是完整的
            if "fast_download_url" in current_update and current_update["fast_download_url"]:
                if current_update["fast_download_url"].startswith("/"):
                    current_update["fast_download_url"] = f"{server_url}{current_update['fast_download_url']}"
                elif not current_update["fast_download_url"].startswith("http"):
                    current_update["fast_download_url"] = f"{server_url}/{current_update['fast_download_url']}"

            # 添加has_update字段，方便客户端判断
            current_update["has_update"] = True

            logger.info(f"从数据库获取到当前版本信息: {current_update}")
        else:
            # 如果数据库中没有找到当前版本信息，使用默认值
            current_update = {
                "version": "1.8",
                "download_url": f"{server_url}/static/downloads/AI主播系统.zip",
                "fast_download_url": f"{server_url}/api/fast-download/AI主播系统.zip",
                "release_date": time.strftime("%Y-%m-%d"),
                "description": "1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性",
                "force_update": False,
                "has_update": True
            }
            logger.info("数据库中没有找到当前版本信息，使用默认值")

        cursor.close()
        conn.close()

        # 返回标准格式的响应，兼容客户端代码
        response = {
            "状态": "成功",
            "数据": current_update
        }

        logger.info(f"返回当前版本信息: {response}")
        return jsonify(response)
    except Exception as e:
        logger.error(f"获取当前版本出错: {str(e)}")

        # 获取服务器地址
        server_url = request.url_root.rstrip('/')
        logger.info(f"异常情况下使用服务器地址: {server_url}")

        # 返回默认版本信息
        default_update = {
            "version": "1.8",
            "download_url": f"{server_url}/static/downloads/AI主播系统.zip",
            "fast_download_url": f"{server_url}/api/fast-download/AI主播系统.zip",
            "release_date": time.strftime("%Y-%m-%d"),
            "description": "1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性",
            "force_update": False,
            "has_update": True
        }

        return jsonify({
            "状态": "成功",
            "数据": default_update
        })

@app.route("/admin/api/updates", methods=["POST"])
@admin_login_required
def admin_create_update():
    try:
        # 获取表单数据
        version = request.form.get("version", "")
        release_date = request.form.get("release_date", "")
        description = request.form.get("description", "")
        force_update = True if request.form.get("force_update") == "on" else False

        # 获取上传的文件
        update_file = request.files.get("update_file")

        if not version or not release_date or not description:
            return jsonify({"状态": "失败", "信息": "缺少必要参数"}), 400

        # 检查版本号是否已存在
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id FROM client_updates WHERE version = ?", (version,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": f"版本 {version} 已存在"}), 400

        # 如果有上传文件，处理文件
        if update_file:
            # 使用版本号命名文件
            base_name = "AI主播系统"
            file_name = f"{base_name}{version}.zip"

            # 确保下载目录存在
            downloads_dir = os.path.join(STATIC_DIR, "downloads")
            os.makedirs(downloads_dir, exist_ok=True)

            # 保存文件
            file_path = os.path.join(downloads_dir, file_name)
            update_file.save(file_path)

            # 生成下载 URL
            download_url = f"/static/downloads/{file_name}"
            fast_download_url = f"/api/fast-download/{file_name}"

            # 记录日志
            logger.info(f"更新包已上传: {file_path}, 下载URL: {download_url}, 快速下载URL: {fast_download_url}")
        else:
            # 如果没有上传文件，使用表单中的下载URL
            download_url = request.form.get("download_url", "")
            if not download_url:
                return jsonify({"状态": "失败", "信息": "未上传文件且未提供下载URL"}), 400

        # 将更新信息保存到数据库
        cursor.execute("""
            INSERT INTO client_updates (version, release_date, description, download_url, force_update, is_current)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (version, release_date, description, download_url, force_update, 0))

        # 如果这是第一个版本，将其设置为当前版本
        cursor.execute("SELECT COUNT(*) FROM client_updates")
        if cursor.fetchone()[0] == 1:
            cursor.execute("UPDATE client_updates SET is_current = 1 WHERE version = ?", (version,))

        conn.commit()
        cursor.close()
        conn.close()

        # 添加日志
        try:
            add_log("admin", "admin", request.remote_addr, "发布更新", f"版本: {version}")
        except Exception as log_error:
            logger.error(f"记录日志失败，但不影响更新流程: {str(log_error)}")

        return jsonify({"状态": "成功", "信息": f"版本 {version} 发布成功"})
    except Exception as e:
        logger.error(f"发布更新出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/api/updates/uploaded", methods=["POST"])
@admin_login_required
def admin_create_uploaded_update():
    try:
        # 获取JSON数据
        data = request.json

        # 验证必要参数
        version = data.get("version")
        release_date = data.get("release_date")
        description = data.get("description")
        download_url = data.get("download_url")
        force_update = data.get("force_update", False)
        set_current = data.get("set_current", False)

        if not version or not release_date or not description or not download_url:
            return jsonify({"状态": "失败", "信息": "缺少必要参数"}), 400

        # 检查版本号是否已存在
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id FROM client_updates WHERE version = ?", (version,))
        if cursor.fetchone():
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": f"版本 {version} 已存在"}), 400

        # 将更新信息保存到数据库
        cursor.execute("""
            INSERT INTO client_updates (version, release_date, description, download_url, force_update, is_current)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (version, release_date, description, download_url, force_update, 0))

        # 获取新插入的记录ID
        cursor.execute("SELECT last_insert_rowid()")
        update_id = cursor.fetchone()[0]

        # 如果需要设置为当前版本
        if set_current:
            # 将所有版本设置为非当前版本
            cursor.execute("UPDATE client_updates SET is_current = 0")

            # 将新版本设置为当前版本
            cursor.execute("UPDATE client_updates SET is_current = 1 WHERE id = ?", (update_id,))

        conn.commit()
        cursor.close()
        conn.close()

        # 添加日志
        try:
            add_log("admin", "admin", request.remote_addr, "发布上传的更新", f"版本: {version}, 设为当前版本: {set_current}")
        except Exception as log_error:
            logger.error(f"记录日志失败，但不影响更新流程: {str(log_error)}")

        return jsonify({"状态": "成功", "信息": f"版本 {version} 已成功发布"})
    except Exception as e:
        logger.error(f"发布上传的更新出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/api/updates/<string:update_id>/set-current", methods=["POST"])
@admin_login_required
def admin_set_current_update(update_id):
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 检查更新是否存在
        cursor.execute("""
            SELECT id, version, release_date, description, download_url, force_update
            FROM client_updates
            WHERE id = ?
        """, (update_id,))

        row = cursor.fetchone()
        if not row:
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": "更新不存在"}), 404

        # 获取更新信息
        columns = [column[0] for column in cursor.description]
        update_info = dict(zip(columns, row))

        version = update_info["version"]

        # 将所有版本设置为非当前版本
        cursor.execute("UPDATE client_updates SET is_current = 0")

        # 将指定版本设置为当前版本
        cursor.execute("UPDATE client_updates SET is_current = 1 WHERE id = ?", (update_id,))

        conn.commit()
        cursor.close()
        conn.close()

        # 获取服务器地址
        server_url = request.url_root.rstrip('/')

        # 更新version.json文件
        version_info = {
            "version": update_info["version"],
            "release_date": update_info["release_date"],
            "description": update_info["description"],
            "download_url": update_info["download_url"],
            "force_update": bool(update_info["force_update"])
        }

        # 添加快速下载URL
        if "download_url" in update_info and update_info["download_url"]:
            # 基于下载URL构建快速下载URL
            download_url = update_info["download_url"]
            if download_url.startswith("http"):
                # 如果是完整URL，提取文件名
                filename = download_url.split("/")[-1]
            else:
                # 如果是相对路径，直接使用
                filename = download_url.split("/")[-1]

            version_info["fast_download_url"] = f"{server_url}/api/fast-download/{filename}"

        # 保存到version.json文件
        version_file = os.path.join("static", "downloads", "version.json")
        with open(version_file, "w", encoding="utf-8") as f:
            json.dump(version_info, f, ensure_ascii=False, indent=4)

        logger.info(f"已更新version.json文件: {version_info}")

        # 添加日志
        try:
            add_log("admin", "admin", request.remote_addr, "设置当前版本", f"版本: {version}")
        except Exception as log_error:
            logger.error(f"记录日志失败，但不影响更新流程: {str(log_error)}")

        return jsonify({"状态": "成功", "信息": f"版本 {version} 已设置为当前版本"})
    except Exception as e:
        logger.error(f"设置当前版本出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

@app.route("/admin/api/updates/<string:update_id>", methods=["DELETE"])
@admin_login_required
def admin_delete_update(update_id):
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 检查更新是否存在
        cursor.execute("SELECT version, download_url, is_current FROM client_updates WHERE id = ?", (update_id,))
        row = cursor.fetchone()
        if not row:
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": "更新不存在"}), 404

        version, download_url, is_current = row

        # 如果是当前版本，不允许删除
        if is_current:
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": "不能删除当前版本"}), 400

        # 删除数据库记录
        cursor.execute("DELETE FROM client_updates WHERE id = ?", (update_id,))
        conn.commit()

        # 删除文件
        file_name = os.path.basename(download_url)
        file_path = os.path.join(UPDATES_DIR, file_name)
        if os.path.exists(file_path):
            os.remove(file_path)

        cursor.close()
        conn.close()

        # 添加日志
        add_log("admin", "admin", request.remote_addr, "删除更新", f"版本: {version}")

        return jsonify({"状态": "成功", "信息": f"版本 {version} 已删除"})
    except Exception as e:
        logger.error(f"删除更新出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500



# 统一的客户端更新检查API - 重定向到/admin/api/updates/current
@app.route("/api/check-update", methods=["POST"])
def check_update():
    """检查更新API - 重定向到/admin/api/updates/current"""
    try:
        data = request.json
        if not data:
            logger.error("请求数据为空")
            return jsonify({
                "status": "error",
                "message": "请求数据为空",
                "timestamp": time.time()
            }), 400

        # 获取客户端版本和机器码
        client_version = data.get("version", "")
        machine_code = data.get("machine_code", "")

        # 记录请求信息
        logger.info(f"收到更新请求: 客户端版本={client_version}, 机器码={machine_code}")

        # 直接从/admin/api/updates/current获取更新信息
        try:
            # 获取服务器地址
            server_url = request.url_root.rstrip('/')

            # 请求/admin/api/updates/current端点
            logger.info(f"从 {server_url}/admin/api/updates/current 获取更新信息")

            # 使用内部调用方式获取更新信息
            with app.test_client() as client:
                response = client.get('/admin/api/updates/current')

                if response.status_code != 200:
                    logger.error(f"从 /admin/api/updates/current 获取更新信息失败: {response.status_code}")
                    raise Exception(f"获取更新信息失败: {response.status_code}")

                # 解析响应
                update_info = response.json
                logger.info(f"从 /admin/api/updates/current 获取到更新信息: {update_info}")

                # 检查是否有版本信息
                if "状态" in update_info and update_info["状态"] == "成功" and "数据" in update_info:
                    update_data = update_info["数据"]

                    # 比较版本号
                    if "version" in update_data and compare_versions(update_data["version"], client_version):
                        logger.info(f"发现新版本: {update_data['version']}")

                        # 记录检查更新的日志
                        add_log("system", machine_code, request.remote_addr, "检查更新",
                               f"当前版本: {client_version}, 最新版本: {update_data['version']}, 需要更新: True")

                        # 返回更新信息
                        return jsonify({
                            "status": "success",
                            "data": {
                                "has_update": True,
                                "version": update_data["version"],
                                "release_date": update_data.get("release_date", time.strftime("%Y-%m-%d")),
                                "description": update_data.get("description", "1. 系统更新\n2. 功能优化"),
                                "download_url": update_data.get("download_url", ""),
                                "fast_download_url": update_data.get("fast_download_url", ""),
                                "force_update": update_data.get("force_update", False),
                                "update_page": "/update.html"
                            },
                            "timestamp": time.time()
                        })
                    else:
                        logger.info(f"客户端已是最新版本: {client_version}")

                        # 记录检查更新的日志
                        add_log("system", machine_code, request.remote_addr, "检查更新",
                               f"当前版本: {client_version}, 最新版本: {update_data.get('version', '未知')}, 需要更新: False")

                        return jsonify({
                            "status": "success",
                            "data": {
                                "has_update": False,
                                "version": update_data.get("version", "1.0.0"),
                                "message": "客户端已是最新版本"
                            },
                            "timestamp": time.time()
                        })

                # 如果没有获取到有效的更新信息，返回默认响应
                logger.warning("从 /admin/api/updates/current 未获取到有效的更新信息")
                return jsonify({
                    "status": "success",
                    "data": {
                        "has_update": False,
                        "version": "1.0.0",
                        "message": "无法获取更新信息"
                    },
                    "timestamp": time.time()
                })

        except Exception as e:
            logger.error(f"从 /admin/api/updates/current 获取更新信息失败: {str(e)}")
            # 返回错误信息
            return jsonify({
                "status": "error",
                "message": f"检查更新出错: {str(e)}",
                "timestamp": time.time()
            }), 500

    except Exception as e:
        logger.error(f"检查更新出错: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"检查更新出错: {str(e)}",
            "timestamp": time.time()
        }), 500

# 保留旧的API端点以兼容旧版本客户端
@app.route("/api/check-update-file", methods=["POST"])
def check_update_file():
    """检查更新API（基于文件）- 兼容旧版本，重定向到/admin/api/updates/current"""
    return check_update()

# 公共的更新检查API - 重定向到/admin/api/updates/current
@app.route("/api/updates/current", methods=["GET"])
@app.route("/api/updates/current-new", methods=["GET"])  # 添加旧路由支持
def public_get_current_update():
    """公共的更新检查API - 重定向到/admin/api/updates/current"""
    try:
        logger.info("收到/api/updates/current请求，重定向到/admin/api/updates/current")

        # 使用内部调用方式获取更新信息
        with app.test_client() as client:
            response = client.get('/admin/api/updates/current')

            if response.status_code != 200:
                logger.error(f"从 /admin/api/updates/current 获取更新信息失败: {response.status_code}")
                raise Exception(f"获取更新信息失败: {response.status_code}")

            # 解析响应
            update_info = response.json
            logger.info(f"从 /admin/api/updates/current 获取到更新信息: {update_info}")

            # 检查是否有版本信息
            if "状态" in update_info and update_info["状态"] == "成功" and "数据" in update_info:
                # 直接返回数据部分，保持与旧API格式兼容
                return jsonify(update_info["数据"])

            # 如果没有获取到有效的更新信息，返回默认响应
            logger.warning("从 /admin/api/updates/current 未获取到有效的更新信息")

            # 获取服务器地址
            server_url = request.url_root.rstrip('/')

            # 返回默认版本信息
            default_info = {
                "version": "3.5.2",
                "download_url": f"{server_url}/static/downloads/test_update_v3.5.2.zip",
                "fast_download_url": f"{server_url}/api/fast-download/test_update_v3.5.2.zip",
                "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                "description": "1. 全新版本3.5.2发布\n2. 修复了更新下载速度慢的问题\n3. 优化了更新界面\n4. 增强了系统稳定性",
                "force_update": False
            }

            logger.info(f"返回默认版本信息: {default_info}")
            return jsonify(default_info)

    except Exception as e:
        logger.error(f"获取当前版本出错: {str(e)}")
        # 获取服务器地址
        server_url = request.url_root.rstrip('/')

        # 返回默认版本信息
        return jsonify({
            "version": "3.5.2",
            "download_url": f"{server_url}/static/downloads/test_update_v3.5.2.zip",
            "fast_download_url": f"{server_url}/api/fast-download/test_update_v3.5.2.zip",
            "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "description": "1. 全新版本3.5.2发布\n2. 修复了更新下载速度慢的问题\n3. 优化了更新界面\n4. 增强了系统稳定性",
            "force_update": False
        })

# 更新历史API
@app.route("/api/update-history", methods=["GET"])
def get_update_history():
    """获取更新历史"""
    try:
        # 首先尝试从数据库获取更新历史
        try:
            from user_manager import get_sqlite_connection
            conn = get_sqlite_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT version, release_date, description, download_url, force_update
                FROM client_updates
                ORDER BY release_date DESC
            """)

            columns = [column[0] for column in cursor.description]
            versions = []

            for row in cursor.fetchall():
                version_data = dict(zip(columns, row))
                # 修正下载URL，确保它是完整的URL
                if version_data["download_url"].startswith("/"):
                    # 如果是相对路径，添加服务器地址
                    server_url = "http://127.0.0.1:12456"
                    logger.info(f"使用本地服务器地址: {server_url}")
                    version_data["download_url"] = f"{server_url}{version_data['download_url']}"
                versions.append(version_data)

            cursor.close()
            conn.close()

            if versions:
                return jsonify({"versions": versions})
            # 如果数据库中没有更新历史，继续尝试从文件获取
        except Exception as db_error:
            logger.error(f"从数据库获取更新历史出错: {str(db_error)}")
            # 如果数据库获取失败，继续尝试从文件获取

        # 从文件获取更新历史
        if not os.path.exists(UPDATES_FILE):
            init_updates_file()

        with open(UPDATES_FILE, "r", encoding="utf-8") as f:
            updates = json.load(f)

        # 修正下载URL，确保它们是完整的URL
        for version in updates["versions"]:
            if version["download_url"].startswith("/"):
                # 如果是相对路径，添加服务器地址
                server_url = "http://127.0.0.1:12456"
                logger.info(f"使用本地服务器地址: {server_url}")
                version["download_url"] = f"{server_url}{version['download_url']}"

        return jsonify(updates)
    except Exception as e:
        logger.error(f"获取更新历史出错: {str(e)}")
        return jsonify({"error": f"获取更新历史出错: {str(e)}"}), 500

# 更新页面路由
@app.route("/update.html", methods=["GET"])
def update_page():
    """更新页面"""
    return render_template("update.html")

# 更新管理页面路由
@app.route("/admin/update-management", methods=["GET"])
@admin_login_required
def update_management_page():
    """更新管理页面"""
    return render_template("admin/update_management.html")

# 更新文件下载路由
@app.route("/downloads/<string:file_name>", methods=["GET"])
def download_update_file(file_name):
    """下载更新文件"""
    try:
        # 检查文件是否存在于downloads目录
        downloads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server_data", "downloads")
        file_path = os.path.join(downloads_dir, file_name)

        if not os.path.exists(file_path):
            logger.error(f"更新文件不存在: {file_path}")
            return jsonify({"状态": "失败", "信息": "文件不存在"}), 404

        # 记录下载日志
        add_log("system", "unknown", request.remote_addr, "下载更新", f"文件: {file_name}")
        logger.info(f"用户 {request.remote_addr} 正在下载更新文件: {file_name}")

        # 设置正确的MIME类型
        if file_name.endswith('.zip'):
            mimetype = 'application/zip'
        else:
            mimetype = 'application/octet-stream'

        return send_from_directory(downloads_dir, file_name, as_attachment=True, mimetype=mimetype)
    except Exception as e:
        logger.error(f"下载更新文件出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 兼容旧版本的下载路由
@app.route("/download/updates/<string:file_name>", methods=["GET"])
def download_update(file_name):
    """兼容旧版本的下载路由"""
    try:
        # 检查文件是否存在于downloads目录
        downloads_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server_data", "downloads")
        file_path = os.path.join(downloads_dir, file_name)

        if not os.path.exists(file_path):
            # 如果在downloads目录中找不到，尝试在updates目录中查找
            updates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "server_data", "updates")
            file_path = os.path.join(updates_dir, file_name)

            if not os.path.exists(file_path):
                logger.error(f"更新文件不存在: {file_path}")
                return jsonify({"状态": "失败", "信息": "文件不存在"}), 404

            # 记录下载日志
            add_log("system", "unknown", request.remote_addr, "下载更新", f"文件: {file_name} (旧路径)")
            logger.info(f"用户 {request.remote_addr} 正在从旧路径下载更新文件: {file_name}")

            # 设置正确的MIME类型
            if file_name.endswith('.zip'):
                mimetype = 'application/zip'
            else:
                mimetype = 'application/octet-stream'

            return send_from_directory(updates_dir, file_name, as_attachment=True, mimetype=mimetype)

        # 记录下载日志
        add_log("system", "unknown", request.remote_addr, "下载更新", f"文件: {file_name} (旧路由)")
        logger.info(f"用户 {request.remote_addr} 正在通过旧路由下载更新文件: {file_name}")

        # 设置正确的MIME类型
        if file_name.endswith('.zip'):
            mimetype = 'application/zip'
        else:
            mimetype = 'application/octet-stream'

        return send_from_directory(downloads_dir, file_name, as_attachment=True, mimetype=mimetype)
    except Exception as e:
        logger.error(f"下载更新文件出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 静态文件下载路由
@app.route("/downloads/<path:filename>", methods=["GET"])
def download_static_file(filename):
    """下载静态文件"""
    try:
        return send_from_directory(os.path.join(STATIC_DIR, "downloads"), filename, as_attachment=True)
    except Exception as e:
        logger.error(f"下载静态文件出错: {str(e)}")
        return jsonify({
            "状态": "失败",
            "信息": f"下载文件出错: {str(e)}"
        }), 500

# 快速下载API
@app.route("/api/fast-download/<path:filename>", methods=["GET"])
def fast_download_file(filename):
    """快速下载文件"""
    try:
        # 检查文件是否存在于downloads目录
        downloads_dir = os.path.join(STATIC_DIR, "downloads")
        file_path = os.path.join(downloads_dir, filename)

        logger.info(f"尝试下载文件: {file_path}")

        if not os.path.exists(file_path):
            logger.error(f"找不到文件: {file_path}")

            # 尝试在downloads目录中查找匹配的文件
            found = False
            for file in os.listdir(downloads_dir):
                if filename in file:
                    file_path = os.path.join(downloads_dir, file)
                    logger.info(f"找到匹配的文件: {file_path}")
                    found = True
                    break

            # 如果仍然找不到文件，尝试使用测试更新文件
            if not found:
                test_file_path = os.path.join(downloads_dir, "test_update_2.3.zip")
                if os.path.exists(test_file_path):
                    file_path = test_file_path
                    logger.info(f"使用测试更新文件: {file_path}")
                    found = True

            # 如果仍然找不到文件，返回404
            if not found or not os.path.exists(file_path):
                return jsonify({"状态": "失败", "信息": "文件不存在"}), 404

        # 记录下载日志
        add_log("system", "unknown", request.remote_addr, "快速下载", f"文件: {filename}")
        logger.info(f"用户 {request.remote_addr} 正在快速下载文件: {file_path}")

        # 设置正确的MIME类型
        if filename.endswith('.zip'):
            mimetype = 'application/zip'
        else:
            mimetype = 'application/octet-stream'

        # 获取实际的文件名
        actual_filename = os.path.basename(file_path)

        return send_from_directory(downloads_dir, actual_filename, as_attachment=True, mimetype=mimetype)
    except Exception as e:
        logger.error(f"快速下载文件出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": str(e)}), 500

# 管理后台更新检查API，不需要验证cookie
@app.route("/admin/api/updates/current", methods=["GET"])
def admin_get_current_update_new():
    """管理后台更新检查API，不需要验证cookie"""
    try:
        # 获取服务器地址
        server_url = request.url_root.rstrip('/')

        # 优先从version.json文件获取更新信息
        version_file = os.path.join("static", "downloads", "version.json")
        if os.path.exists(version_file):
            try:
                with open(version_file, "r", encoding="utf-8") as f:
                    version_info = json.load(f)

                    # 确保下载URL是完整的URL
                    if "download_url" in version_info and version_info["download_url"]:
                        if version_info["download_url"].startswith("/"):
                            version_info["download_url"] = f"{server_url}{version_info['download_url']}"
                        elif not version_info["download_url"].startswith("http"):
                            version_info["download_url"] = f"{server_url}/{version_info['download_url']}"

                    # 确保快速下载URL是完整的URL
                    if "fast_download_url" in version_info and version_info["fast_download_url"]:
                        if version_info["fast_download_url"].startswith("/"):
                            version_info["fast_download_url"] = f"{server_url}{version_info['fast_download_url']}"
                        elif not version_info["fast_download_url"].startswith("http"):
                            version_info["fast_download_url"] = f"{server_url}/{version_info['fast_download_url']}"

                    logger.info(f"从version.json获取到更新信息: {version_info}")
                    return jsonify(version_info)
            except Exception as e:
                logger.error(f"读取version.json出错: {str(e)}")
                # 如果读取version.json出错，继续尝试从数据库获取

        # 如果没有找到version.json或读取出错，尝试从数据库获取
        try:
            from user_manager import get_sqlite_connection
            conn = get_sqlite_connection()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, version, release_date, description, download_url, force_update
                FROM client_updates
                WHERE is_current = 1
            """)

            row = cursor.fetchone()

            if row:
                columns = [column[0] for column in cursor.description]
                current_update = dict(zip(columns, row))

                # 确保下载URL是完整的URL
                if "download_url" in current_update and current_update["download_url"]:
                    if current_update["download_url"].startswith("/"):
                        current_update["download_url"] = f"{server_url}{current_update['download_url']}"
                    elif not current_update["download_url"].startswith("http"):
                        current_update["download_url"] = f"{server_url}/{current_update['download_url']}"

                # 添加快速下载URL
                if "download_url" in current_update and current_update["download_url"]:
                    # 基于下载URL构建快速下载URL
                    download_url = current_update["download_url"]
                    if download_url.startswith("http"):
                        # 如果是完整URL，提取文件名
                        filename = download_url.split("/")[-1]
                    else:
                        # 如果是相对路径，直接使用
                        filename = download_url.split("/")[-1]

                    # 检查是否已经有fast_download_url
                    if "fast_download_url" not in current_update or not current_update["fast_download_url"]:
                        current_update["fast_download_url"] = f"{server_url}/api/fast-download/{filename}"
                    # 如果fast_download_url是相对路径，添加服务器地址
                    elif current_update["fast_download_url"].startswith("/"):
                        current_update["fast_download_url"] = f"{server_url}{current_update['fast_download_url']}"
                    # 如果fast_download_url不是http开头，也添加服务器地址
                    elif not current_update["fast_download_url"].startswith("http"):
                        current_update["fast_download_url"] = f"{server_url}/{current_update['fast_download_url']}"

                logger.info(f"从数据库获取到更新信息: {current_update}")

                cursor.close()
                conn.close()

                return jsonify(current_update)

            cursor.close()
            conn.close()
        except Exception as db_error:
            logger.error(f"从数据库获取更新信息出错: {str(db_error)}")

        # 如果都失败了，返回默认版本信息
        default_info = {
            "version": "3.5.2",
            "download_url": f"{server_url}/static/downloads/test_update_v3.5.2.zip",
            "fast_download_url": f"{server_url}/api/fast-download/test_update_v3.5.2.zip",
            "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "description": "1. 全新版本3.5.2发布\n2. 修复了更新下载速度慢的问题\n3. 优化了更新界面\n4. 增强了系统稳定性",
            "force_update": False
        }

        logger.info(f"返回默认版本信息: {default_info}")
        return jsonify(default_info)
    except Exception as e:
        logger.error(f"获取当前版本出错: {str(e)}")
        # 获取服务器地址
        server_url = request.url_root.rstrip('/')

        # 返回默认版本信息
        return jsonify({
            "version": "3.5.2",
            "download_url": f"{server_url}/static/downloads/test_update_v3.5.2.zip",
            "fast_download_url": f"{server_url}/api/fast-download/test_update_v3.5.2.zip",
            "release_date": datetime.datetime.now().strftime("%Y-%m-%d"),
            "description": "1. 全新版本3.5.2发布\n2. 修复了更新下载速度慢的问题\n3. 优化了更新界面\n4. 增强了系统稳定性",
            "force_update": False
        })

# 更新信息文件管理API
@app.route("/admin/api/updates/file", methods=["GET"])
@admin_login_required
def admin_get_updates_file():
    """获取更新信息文件"""
    try:
        if not os.path.exists(UPDATES_FILE):
            init_updates_file()

        with open(UPDATES_FILE, "r", encoding="utf-8") as f:
            updates = json.load(f)

        return jsonify({
            "状态": "成功",
            "数据": updates
        })
    except Exception as e:
        logger.error(f"获取更新信息文件出错: {str(e)}")
        return jsonify({
            "状态": "失败",
            "信息": f"获取更新信息文件出错: {str(e)}"
        }), 500



@app.route("/admin/api/updates/file", methods=["POST"])
@admin_login_required
def admin_update_updates_file():
    """更新更新信息文件"""
    try:
        data = request.json

        if not data:
            return jsonify({
                "状态": "失败",
                "信息": "缺少必要参数"
            }), 400

        with open(UPDATES_FILE, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

        logger.info(f"更新信息文件已更新")
        return jsonify({
            "状态": "成功",
            "信息": "更新信息文件已更新"
        })
    except Exception as e:
        logger.error(f"更新更新信息文件出错: {str(e)}")
        return jsonify({
            "状态": "失败",
            "信息": f"更新更新信息文件出错: {str(e)}"
        }), 500

# 系统设置API
@app.route("/admin/api/settings", methods=["GET"])
@admin_login_required
def admin_get_settings():
    try:
        # 返回默认设置
        settings = {
            "server": {
                "port": 12456,
                "debug_mode": True,
                "db_host": "**************",
                "db_port": 3306,
                "db_name": "reg"
            },
            "app": {
                "token_expiry": 1,
                "default_expiry": 30,
                "voice_api_url": "http://localhost:12456/voice/speakers",
                "enable_cors": True
            }
        }
        return jsonify({"状态": "成功", "数据": settings})
    except Exception as e:
        logger.error(f"获取设置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取设置出错: {str(e)}"}), 500

@app.route("/admin/api/settings", methods=["POST"])
@admin_login_required
def admin_update_settings():
    try:
        data = request.json
        setting_type = data.get("type")
        settings = data.get("settings")

        if not setting_type or not settings:
            return jsonify({"状态": "失败", "信息": "缺少必要参数"}), 400

        # 这里应该实现保存设置的逻辑
        # 当前只是模拟成功响应
        return jsonify({"状态": "成功", "信息": "设置已保存"})
    except Exception as e:
        logger.error(f"保存设置出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"保存设置出错: {str(e)}"}), 500

# 数据统计API
@app.route("/admin/api/stats", methods=["GET"])
@admin_login_required
def admin_get_stats():
    try:
        # 返回真实的统计数据
        from user_manager import get_db_connection, get_sqlite_connection
        import os
        import datetime

        # 获取MySQL数据库连接
        mysql_conn = get_db_connection()
        mysql_cursor = mysql_conn.cursor()

        # 获取SQLite数据库连接
        sqlite_conn = get_sqlite_connection()
        sqlite_cursor = sqlite_conn.cursor()

        # 获取用户数量
        mysql_cursor.execute("SELECT COUNT(*) FROM list")
        user_count = mysql_cursor.fetchone()[0]

        # 获取话术数量
        try:
            mysql_cursor.execute("SELECT COUNT(*) FROM scripts")
            script_count = mysql_cursor.fetchone()[0]
        except Exception as e:
            logger.warning(f"获取话术数量出错: {str(e)}")
            script_count = 0

        # 获取对话数量
        try:
            mysql_cursor.execute("SELECT COUNT(*) FROM dialogues")
            dialogue_count = mysql_cursor.fetchone()[0]
        except Exception as e:
            logger.warning(f"获取对话数量出错: {str(e)}")
            dialogue_count = 0

        # 获取语音数量
        try:
            sqlite_cursor.execute("SELECT COUNT(*) FROM voices")
            voice_count = sqlite_cursor.fetchone()[0]
        except Exception as e:
            logger.warning(f"获取语音数量出错: {str(e)}")
            voice_count = 0

        # 获取数据库大小
        db_size = 0
        if os.path.exists('local.db'):
            db_size = os.path.getsize('local.db')

        # 获取表数量
        sqlite_cursor.execute("SELECT count(*) FROM sqlite_master WHERE type='table'")
        table_count = sqlite_cursor.fetchone()[0]

        # 获取记录总数
        record_count = 0
        sqlite_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = sqlite_cursor.fetchall()
        for table in tables:
            table_name = table[0]
            if table_name.startswith('sqlite_'):
                continue
            sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            record_count += sqlite_cursor.fetchone()[0]

        # 获取索引数量
        sqlite_cursor.execute("SELECT count(*) FROM sqlite_master WHERE type='index'")
        index_count = sqlite_cursor.fetchone()[0]

        # 获取用户注册趋势
        current_year = datetime.datetime.now().year
        month_data = [0] * 12

        try:
            mysql_cursor.execute("SELECT DATE_FORMAT(reg_time, '%Y-%m') as month, COUNT(*) as count FROM list WHERE YEAR(reg_time) = %s GROUP BY month ORDER BY month", (current_year,))
            for row in mysql_cursor.fetchall():
                month_str = row[0]
                count = row[1]
                month = int(month_str.split('-')[1]) - 1  # 月份从0开始
                month_data[month] = count
        except Exception as e:
            logger.warning(f"获取用户注册趋势出错: {str(e)}")

        # 获取用户活跃度
        days_of_week = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
        activity_data = [0] * 7

        # 获取最近7天的登录数据
        sqlite_cursor.execute("SELECT strftime('%w', time) as day_of_week, COUNT(*) as count FROM logs WHERE action = '用户登录' GROUP BY day_of_week")
        for row in sqlite_cursor.fetchall():
            day = int(row[0])
            count = row[1]
            # 将周日(0)调整为数组的最后一个元素
            if day == 0:
                activity_data[6] = count
            else:
                activity_data[day-1] = count

        # 获取话术使用统计
        script_labels = []
        script_data = []

        try:
            mysql_cursor.execute("SELECT script_type, COUNT(*) as count FROM scripts GROUP BY script_type ORDER BY count DESC LIMIT 5")
            for row in mysql_cursor.fetchall():
                script_type = row[0] or '其他'
                count = row[1]
                script_labels.append(script_type)
                script_data.append(count)
        except Exception as e:
            logger.warning(f"获取话术使用统计出错: {str(e)}")
            # 添加默认数据
            script_labels = ['欢迎话术', '互动话术', '感谢话术', '广告话术', '其他话术']
            script_data = [0, 0, 0, 0, 0]

        # 获取系统资源使用情况
        # 由于无法安装psutil库，我们使用静态数据
        import random

        # 生成一些随机数据作为演示
        cpu_usage = random.randint(20, 80)
        memory_usage = random.randint(30, 90)
        disk_usage = random.randint(10, 70)
        network_usage = random.randint(5, 50)

        # 构建统计数据
        stats = {
            "user_count": user_count,
            "script_count": script_count,
            "dialogue_count": dialogue_count,
            "voice_count": voice_count,
            "db_size": db_size,
            "table_count": table_count,
            "record_count": record_count,
            "index_count": index_count,
            "user_registration_trend": {
                "labels": ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
                "data": month_data
            },
            "user_activity": {
                "labels": days_of_week,
                "data": activity_data
            },
            "script_usage": {
                "labels": script_labels,
                "data": script_data
            },
            "resource_usage": {
                "cpu": cpu_usage,
                "memory": memory_usage,
                "disk": disk_usage,
                "network": network_usage
            }
        }

        mysql_cursor.close()
        mysql_conn.close()
        sqlite_cursor.close()
        sqlite_conn.close()

        return jsonify({"状态": "成功", "数据": stats})
    except Exception as e:
        logger.error(f"获取统计数据出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取统计数据出错: {str(e)}"}), 500

# 日志查看API
@app.route("/admin/api/logs", methods=["GET"])
@admin_login_required
def admin_get_logs_api():
    try:
        page = request.args.get('page', 1, type=int)
        page_size = request.args.get('page_size', 10, type=int)
        username = request.args.get('username', '')
        level = request.args.get('level', 'all')
        date = request.args.get('date', '')

        # 从数据库中获取日志
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 构建查询条件
        query = "SELECT * FROM logs"
        conditions = []
        params = []

        if username:
            conditions.append("username LIKE ?")
            params.append(f"%{username}%")

        if level != 'all':
            conditions.append("level = ?")
            params.append(level)

        if date:
            conditions.append("time LIKE ?")
            params.append(f"{date}%")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        # 获取总数
        count_query = query.replace("SELECT *", "SELECT COUNT(*)")
        cursor.execute(count_query, params)
        total_count = cursor.fetchone()[0]

        # 添加排序和分页
        query += " ORDER BY id DESC LIMIT ? OFFSET ?"
        params.extend([page_size, (page - 1) * page_size])

        cursor.execute(query, params)
        columns = [column[0] for column in cursor.description]
        logs = [dict(zip(columns, row)) for row in cursor.fetchall()]

        cursor.close()
        conn.close()

        return jsonify({
            "状态": "成功",
            "数据": {
                "日志列表": logs,
                "总数": total_count,
                "当前页": page,
                "每页数量": page_size
            }
        })
    except Exception as e:
        logger.error(f"获取日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取日志出错: {str(e)}"}), 500

@app.route("/admin/api/logs/stats", methods=["GET"])
@admin_login_required
def admin_get_log_stats_api():
    try:
        # 从数据库中获取日志统计
        from user_manager import get_sqlite_connection
        import datetime

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 获取总数
        cursor.execute("SELECT COUNT(*) FROM logs")
        total = cursor.fetchone()[0]

        # 获取不同级别的数量
        cursor.execute("SELECT COUNT(*) FROM logs WHERE level = 'info'")
        info_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM logs WHERE level = 'warning'")
        warning_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM logs WHERE level = 'error'")
        error_count = cursor.fetchone()[0]

        # 获取今天的日志数量
        today = datetime.datetime.now().strftime('%Y-%m-%d')
        cursor.execute("SELECT COUNT(*) FROM logs WHERE time LIKE ?", (f"{today}%",))
        today_count = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return jsonify({
            "状态": "成功",
            "数据": {
                "total": total,
                "info": info_count,
                "warning": warning_count,
                "error": error_count,
                "today": today_count
            }
        })
    except Exception as e:
        logger.error(f"获取日志统计出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取日志统计出错: {str(e)}"}), 500

# 日志统计已经在 /admin/api/logs/stats 路由中处理

@app.route("/admin/api/logs/<int:log_id>", methods=["GET"])
@admin_login_required
def admin_get_log_detail_api(log_id):
    try:
        # 从数据库中获取日志详情
        from user_manager import get_sqlite_connection

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM logs WHERE id = ?", (log_id,))
        columns = [column[0] for column in cursor.description]
        log = cursor.fetchone()

        if not log:
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": "日志不存在"}), 404

        log_dict = dict(zip(columns, log))

        # 确保所有值都是简单类型
        for key, value in log_dict.items():
            if isinstance(value, (dict, list)):
                log_dict[key] = json.dumps(value, ensure_ascii=False)

        cursor.close()
        conn.close()

        return jsonify({"状态": "成功", "数据": log_dict})
    except Exception as e:
        logger.error(f"获取日志详情出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取日志详情出错: {str(e)}"}), 500

# 日志详情已经在 /admin/api/logs/<int:log_id> 路由中处理

@app.route("/admin/api/logs/clear-old", methods=["POST"])
@admin_login_required
def admin_clear_old_logs_api():
    try:
        # 清理30天前的日志
        from user_manager import get_sqlite_connection
        import datetime

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 计算30天前的日期
        thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')

        # 获取要删除的日志数量
        cursor.execute("SELECT COUNT(*) FROM logs WHERE time < ?", (thirty_days_ago,))
        count = cursor.fetchone()[0]

        # 删除日志
        cursor.execute("DELETE FROM logs WHERE time < ?", (thirty_days_ago,))
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify({"状态": "成功", "数据": {"count": count}})
    except Exception as e:
        logger.error(f"清理旧日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"清理旧日志出错: {str(e)}"}), 500

# 清理旧日志已经在 /admin/api/logs/clear-old 路由中处理

@app.route("/admin/api/logs/clear-all", methods=["POST"])
@admin_login_required
def admin_clear_all_logs_api():
    try:
        # 清空所有日志
        from user_manager import get_sqlite_connection

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        cursor.execute("DELETE FROM logs")
        conn.commit()

        cursor.close()
        conn.close()

        return jsonify({"状态": "成功"})
    except Exception as e:
        logger.error(f"清空日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"清空日志出错: {str(e)}"}), 500

# 清空所有日志已经在 /admin/api/logs/clear-all 路由中处理

@app.route("/admin/api/logs/export", methods=["GET"])
@admin_login_required
def admin_export_logs_api():
    try:
        # 导出日志
        username = request.args.get('username', '')
        level = request.args.get('level', 'all')
        date = request.args.get('date', '')
        days = request.args.get('days', type=int)  # 新增：按天数导出
        clear_after_export = request.args.get('clear', 'false').lower() == 'true'  # 新增：导出后清理

        from user_manager import get_sqlite_connection
        import csv
        import io
        from flask import Response
        import datetime

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 构建查询条件
        query = "SELECT * FROM logs"
        conditions = []
        params = []

        if username:
            conditions.append("username LIKE ?")
            params.append(f"%{username}%")

        if level != 'all':
            conditions.append("level = ?")
            params.append(level)

        # 处理日期条件
        if date:
            conditions.append("time LIKE ?")
            params.append(f"{date}%")
        elif days:
            # 计算N天前的日期
            today = datetime.datetime.now()
            n_days_ago = today - datetime.timedelta(days=days)
            n_days_ago_str = n_days_ago.strftime("%Y-%m-%d")

            conditions.append("time >= ?")
            params.append(n_days_ago_str)

            # 记录日志
            logger.info(f"按天数导出日志: {days}天, 从 {n_days_ago_str} 到今天")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        query += " ORDER BY id DESC"

        cursor.execute(query, params)
        columns = [column[0] for column in cursor.description]
        logs = cursor.fetchall()

        # 如果需要导出后清理
        if clear_after_export and (date or days):
            try:
                # 构建删除条件
                delete_query = "DELETE FROM logs"
                if conditions:
                    delete_query += " WHERE " + " AND ".join(conditions)

                # 执行删除
                cursor.execute(delete_query, params)
                affected_rows = cursor.rowcount
                conn.commit()

                logger.info(f"导出后清理日志成功，共删除 {affected_rows} 条记录")
            except Exception as e:
                logger.error(f"导出后清理日志失败: {str(e)}")
                conn.rollback()

        cursor.close()
        conn.close()

        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入列名
        writer.writerow(columns)

        # 写入数据
        for log in logs:
            writer.writerow(log)

        # 返回响应
        output.seek(0)
        filename = f"logs_{date or (f'last_{days}_days' if days else 'all')}.csv"
        return Response(
            output.getvalue(),
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename={filename}"}
        )
    except Exception as e:
        logger.error(f"导出日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"导出日志出错: {str(e)}"}), 500

# 导出日志已经在 /admin/api/logs/export 路由中处理

# 服务器重启API
@app.route("/admin/api/restart", methods=["POST"])
@admin_login_required
def admin_restart_server():
    try:
        import os
        import sys
        import subprocess
        import threading
        import time

        # 记录重启操作日志
        add_log("admin", "admin", request.remote_addr, "重启服务器", "管理员操作")

        # 定义重启函数
        def restart_server():
            time.sleep(2)  # 等待2秒，确保响应已经发送

            # 获取当前脚本路径
            script_path = os.path.abspath(__file__)

            # 使用Python解释器重新启动服务器
            subprocess.Popen([sys.executable, script_path])

            # 退出当前进程
            os._exit(0)

        # 在新线程中执行重启，以便能够先返回响应
        threading.Thread(target=restart_server).start()

        return jsonify({"状态": "成功", "信息": "服务器正在重启，请稍后刷新页面"})
    except Exception as e:
        logger.error(f"重启服务器出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"重启服务器出错: {str(e)}"}), 500

# 清理日志API


@app.route("/admin/api/danmaku/export", methods=["GET"])
@admin_login_required
def admin_export_danmaku_api():
    """导出特定账号的所有弹幕记录"""
    try:
        # 获取请求参数
        username = request.args.get('username', '')
        if not username:
            return jsonify({"状态": "失败", "信息": "必须提供用户名参数"}), 400

        import sqlite3
        import csv
        import io
        from flask import Response
        import datetime

        # 连接数据库
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 查询弹幕记录
        try:
            # 创建弹幕记录表（如果不存在）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,            -- 账号用户名
                sender TEXT,              -- 发送弹幕的用户名
                content TEXT,             -- 弹幕内容
                timestamp TEXT,           -- 时间戳
                created_at TEXT           -- 记录创建时间
            )
            """)
            conn.commit()

            # 查询弹幕记录
            cursor.execute("""
            SELECT id, username, sender, content, timestamp, created_at
            FROM danmaku_records
            WHERE username LIKE ?
            ORDER BY timestamp DESC
            """, (f"%{username}%",))

            columns = [column[0] for column in cursor.description]
            danmaku_records = cursor.fetchall()

            cursor.close()
            conn.close()

            # 创建CSV文件
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入列名
            writer.writerow(columns)

            # 写入数据
            for record in danmaku_records:
                writer.writerow(record)

            # 返回响应
            output.seek(0)
            current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"danmaku_{username}_{current_time}.csv"

            return Response(
                output.getvalue(),
                mimetype="text/csv",
                headers={"Content-Disposition": f"attachment;filename={filename}"}
            )
        except Exception as e:
            logger.error(f"查询弹幕记录出错: {str(e)}")
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": f"查询弹幕记录出错: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"导出弹幕记录出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"导出弹幕记录出错: {str(e)}"}), 500

@app.route("/admin/api/danmaku/list", methods=["GET"])
@admin_login_required
def admin_get_danmaku_list_api():
    """获取特定账号的弹幕记录列表"""
    try:
        # 获取请求参数
        username = request.args.get('username', '')
        if not username:
            return jsonify({"状态": "失败", "信息": "必须提供用户名参数"}), 400

        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)

        import sqlite3

        # 连接数据库
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 查询弹幕记录
        try:
            # 创建弹幕记录表（如果不存在）
            cursor.execute("""
            CREATE TABLE IF NOT EXISTS danmaku_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,            -- 账号用户名
                sender TEXT,              -- 发送弹幕的用户名
                content TEXT,             -- 弹幕内容
                timestamp TEXT,           -- 时间戳
                created_at TEXT           -- 记录创建时间
            )
            """)
            conn.commit()

            # 查询总记录数
            cursor.execute("""
            SELECT COUNT(*) FROM danmaku_records
            WHERE username LIKE ?
            """, (f"%{username}%",))

            total_count = cursor.fetchone()[0]

            # 查询弹幕记录
            cursor.execute("""
            SELECT id, username, sender, content, timestamp, created_at
            FROM danmaku_records
            WHERE username LIKE ?
            ORDER BY timestamp DESC
            LIMIT ? OFFSET ?
            """, (f"%{username}%", limit, offset))

            columns = [column[0] for column in cursor.description]
            danmaku_records = cursor.fetchall()

            # 转换为字典列表
            records = []
            for record in danmaku_records:
                record_dict = {}
                for i, column in enumerate(columns):
                    record_dict[column] = record[i]
                records.append(record_dict)

            cursor.close()
            conn.close()

            return jsonify({
                "状态": "成功",
                "数据": {
                    "记录": records,
                    "总数": total_count,
                    "当前页": offset // limit + 1,
                    "每页数量": limit,
                    "总页数": (total_count + limit - 1) // limit
                }
            })
        except Exception as e:
            logger.error(f"查询弹幕记录出错: {str(e)}")
            cursor.close()
            conn.close()
            return jsonify({"状态": "失败", "信息": f"查询弹幕记录出错: {str(e)}"}), 500
    except Exception as e:
        logger.error(f"获取弹幕记录列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取弹幕记录列表出错: {str(e)}"}), 500

@app.route("/admin/api/account/logs/export", methods=["GET"])
@admin_login_required
def admin_export_account_logs_api():
    """导出特定账号的所有操作日志"""
    try:
        # 获取请求参数
        username = request.args.get('username', '')
        if not username:
            return jsonify({"状态": "失败", "信息": "必须提供用户名参数"}), 400

        from user_manager import get_sqlite_connection
        import csv
        import io
        from flask import Response
        import datetime
        import sqlite3

        # 连接数据库
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 查询系统日志
        system_logs_query = "SELECT * FROM logs WHERE username LIKE ? ORDER BY time DESC"
        cursor.execute(system_logs_query, (f"%{username}%",))
        system_logs_columns = [column[0] for column in cursor.description]
        system_logs = cursor.fetchall()

        # 尝试查询客户端日志
        client_logs = []
        client_logs_columns = []
        try:
            # 连接客户端日志数据库
            client_conn = get_sqlite_connection()
            client_cursor = client_conn.cursor()

            # 查询客户端日志
            client_logs_query = "SELECT * FROM client_logs WHERE username LIKE ? ORDER BY timestamp DESC"
            client_cursor.execute(client_logs_query, (f"%{username}%",))
            client_logs_columns = [column[0] for column in client_cursor.description]
            client_logs = client_cursor.fetchall()

            client_cursor.close()
            client_conn.close()
        except Exception as e:
            logger.warning(f"获取客户端日志出错: {str(e)}")

        # 尝试查询直播状态记录
        live_status_logs = []
        live_status_columns = []
        try:
            # 查询直播状态记录
            live_status_query = "SELECT * FROM live_status WHERE username LIKE ? ORDER BY last_update_time DESC"
            cursor.execute(live_status_query, (f"%{username}%",))
            live_status_columns = [column[0] for column in cursor.description]
            live_status_logs = cursor.fetchall()
        except Exception as e:
            logger.warning(f"获取直播状态记录出错: {str(e)}")

        cursor.close()
        conn.close()

        # 创建CSV文件
        output = io.StringIO()
        writer = csv.writer(output)

        # 写入系统日志
        if system_logs:
            writer.writerow(["=== 系统日志 ==="])
            writer.writerow(system_logs_columns)
            for log in system_logs:
                writer.writerow(log)
            writer.writerow([])  # 空行分隔

        # 写入客户端日志
        if client_logs:
            writer.writerow(["=== 客户端日志 ==="])
            writer.writerow(client_logs_columns)
            for log in client_logs:
                writer.writerow(log)
            writer.writerow([])  # 空行分隔

        # 写入直播状态记录
        if live_status_logs:
            writer.writerow(["=== 直播状态记录 ==="])
            writer.writerow(live_status_columns)
            for log in live_status_logs:
                writer.writerow(log)

        # 返回响应
        output.seek(0)
        current_time = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"account_logs_{username}_{current_time}.csv"

        return Response(
            output.getvalue(),
            mimetype="text/csv",
            headers={"Content-Disposition": f"attachment;filename={filename}"}
        )
    except Exception as e:
        logger.error(f"导出账号操作日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"导出账号操作日志出错: {str(e)}"}), 500


@app.route("/admin/api/logs/clear", methods=["POST"])
@admin_login_required
def admin_clear_logs():
    try:
        # 从数据库中清空日志表
        from user_manager import get_sqlite_connection

        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 获取当前日志数量
        cursor.execute("SELECT COUNT(*) FROM logs")
        log_count = cursor.fetchone()[0]

        # 清空日志表
        cursor.execute("DELETE FROM logs")
        conn.commit()

        # 重置自增ID
        cursor.execute("DELETE FROM sqlite_sequence WHERE name='logs'")
        conn.commit()

        cursor.close()
        conn.close()

        # 记录清理日志操作
        add_log("admin", "admin", request.remote_addr, "清理日志", f"已清理 {log_count} 条日志")

        return jsonify({"状态": "成功", "信息": f"已清理 {log_count} 条日志"})
    except Exception as e:
        logger.error(f"清理日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"清理日志出错: {str(e)}"}), 500

# 数据库备份API
@app.route("/admin/api/backup", methods=["POST"])
@admin_login_required
def admin_backup_database():
    try:
        # 使用备份管理器创建备份
        if backup_manager:
            backup_file = backup_manager.create_backup(backup_name="manual")
            if backup_file:
                # 记录备份操作
                add_log("admin", "admin", request.remote_addr, "备份数据库", f"已创建备份: {backup_file}")

                return jsonify({
                    "状态": "成功",
                    "信息": "数据库备份成功",
                    "文件路径": backup_file
                })
            else:
                return jsonify({
                    "状态": "失败",
                    "信息": "创建备份失败"
                }), 500
        else:
            # 如果备份管理器不可用，使用传统方式备份
            import shutil
            import datetime

            # 获取当前时间作为备份文件名的一部分
            now = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 创建备份目录
            backup_dir = os.path.join(DATA_DIR, "backups")
            os.makedirs(backup_dir, exist_ok=True)

            # 备份SQLite数据库
            sqlite_backup_path = os.path.join(backup_dir, f"local_{now}.db")
            if os.path.exists("local.db"):
                shutil.copy2("local.db", sqlite_backup_path)

            # 记录备份操作
            add_log("admin", "admin", request.remote_addr, "备份数据库", f"已创建备份: {sqlite_backup_path}")

            return jsonify({
                "状态": "成功",
                "信息": "数据库备份成功",
                "文件路径": sqlite_backup_path
            })
    except Exception as e:
        logger.error(f"备份数据库出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"备份数据库出错: {str(e)}"}), 500

# 监控系统相关API
@app.route("/admin/api/monitor/status", methods=["GET"])
@admin_login_required
def admin_get_monitor_status():
    try:
        # 获取服务器监控状态
        if server_monitor:
            status = server_monitor.get_current_status()
            return jsonify({
                "状态": "成功",
                "数据": status
            })
        else:
            return jsonify({
                "状态": "失败",
                "信息": "服务器监控未初始化"
            }), 500
    except Exception as e:
        logger.error(f"获取监控状态出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取监控状态出错: {str(e)}"}), 500

@app.route("/admin/api/monitor/history", methods=["GET"])
@admin_login_required
def admin_get_monitor_history():
    try:
        # 获取监控历史数据
        if server_monitor:
            limit = request.args.get("limit", default=60, type=int)
            history = server_monitor.get_history(limit=limit)
            return jsonify({
                "状态": "成功",
                "数据": history
            })
        else:
            return jsonify({
                "状态": "失败",
                "信息": "服务器监控未初始化"
            }), 500
    except Exception as e:
        logger.error(f"获取监控历史数据出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取监控历史数据出错: {str(e)}"}), 500

@app.route("/admin/api/monitor/health", methods=["GET"])
@admin_login_required
def admin_get_system_health():
    try:
        # 获取系统健康状态
        if server_monitor:
            health = server_monitor.check_server_health()
            return jsonify({
                "状态": "成功",
                "数据": health
            })
        else:
            return jsonify({
                "状态": "失败",
                "信息": "服务器监控未初始化"
            }), 500
    except Exception as e:
        logger.error(f"获取系统健康状态出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取系统健康状态出错: {str(e)}"}), 500

@app.route("/admin/api/backup/list", methods=["GET"])
@admin_login_required
def admin_get_backup_list():
    try:
        # 获取备份列表
        if backup_manager:
            backups = backup_manager.list_backups()
            return jsonify({
                "状态": "成功",
                "数据": backups
            })
        else:
            # 如果备份管理器不可用，手动获取备份列表
            backup_dir = os.path.join(DATA_DIR, "backups")
            if not os.path.exists(backup_dir):
                return jsonify({
                    "状态": "成功",
                    "数据": []
                })

            backups = []
            for f in os.listdir(backup_dir):
                if f.endswith(".db") or f.endswith(".zip"):
                    file_path = os.path.join(backup_dir, f)
                    backups.append({
                        "name": f,
                        "path": file_path,
                        "time": datetime.datetime.fromtimestamp(os.path.getmtime(file_path)).strftime("%Y-%m-%d %H:%M:%S"),
                        "size": os.path.getsize(file_path),
                        "size_mb": round(os.path.getsize(file_path) / (1024 * 1024), 2)
                    })

            # 按时间排序
            backups.sort(key=lambda x: x["time"], reverse=True)

            return jsonify({
                "状态": "成功",
                "数据": backups
            })
    except Exception as e:
        logger.error(f"获取备份列表出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取备份列表出错: {str(e)}"}), 500

@app.route("/admin/api/backup/restore", methods=["POST"])
@admin_login_required
def admin_restore_backup():
    try:
        data = request.json
        backup_file = data.get("backup_file", "")

        if not backup_file:
            return jsonify({"状态": "失败", "信息": "备份文件路径不能为空"}), 400

        # 使用备份管理器恢复备份
        if backup_manager:
            result = backup_manager.restore_backup(backup_file, confirm=True)
            if result:
                # 记录恢复操作
                add_log("admin", "admin", request.remote_addr, "恢复数据库", f"已从备份恢复: {backup_file}")

                return jsonify({
                    "状态": "成功",
                    "信息": "数据库恢复成功"
                })
            else:
                return jsonify({
                    "状态": "失败",
                    "信息": "恢复备份失败"
                }), 500
        else:
            # 如果备份管理器不可用，使用传统方式恢复
            import shutil

            # 检查备份文件是否存在
            if not os.path.exists(backup_file):
                return jsonify({"状态": "失败", "信息": f"备份文件不存在: {backup_file}"}), 400

            # 先备份当前数据库
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            current_backup = os.path.join(DATA_DIR, "backups", f"pre_restore_{timestamp}.db")

            if os.path.exists("local.db"):
                shutil.copy2("local.db", current_backup)

            # 复制备份文件到数据库文件
            shutil.copy2(backup_file, "local.db")

            # 记录恢复操作
            add_log("admin", "admin", request.remote_addr, "恢复数据库", f"已从备份恢复: {backup_file}")

            return jsonify({
                "状态": "成功",
                "信息": "数据库恢复成功"
            })
    except Exception as e:
        logger.error(f"恢复数据库出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"恢复数据库出错: {str(e)}"}), 500

@app.route("/admin/api/errors", methods=["GET"])
@admin_login_required
def admin_get_errors():
    try:
        # 获取错误日志
        if error_handler:
            limit = request.args.get("limit", default=50, type=int)
            include_resolved = request.args.get("include_resolved", default="false") == "true"
            errors = error_handler.get_recent_errors(limit=limit, include_resolved=include_resolved)
            return jsonify({
                "状态": "成功",
                "数据": errors
            })
        else:
            return jsonify({
                "状态": "失败",
                "信息": "错误处理器未初始化"
            }), 500
    except Exception as e:
        logger.error(f"获取错误日志出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取错误日志出错: {str(e)}"}), 500

@app.route("/admin/api/errors/stats", methods=["GET"])
@admin_login_required
def admin_get_error_stats():
    try:
        # 获取错误统计信息
        if error_handler:
            stats = error_handler.get_error_stats()
            return jsonify({
                "状态": "成功",
                "数据": stats
            })
        else:
            return jsonify({
                "状态": "失败",
                "信息": "错误处理器未初始化"
            }), 500
    except Exception as e:
        logger.error(f"获取错误统计信息出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"获取错误统计信息出错: {str(e)}"}), 500

@app.route("/admin/api/client_logs", methods=["GET"])
@admin_login_required
def admin_get_client_logs():
    try:
        # 获取客户端日志
        import sqlite3
        import json
        import traceback

        # 获取查询参数
        username = request.args.get("username", "")
        limit = request.args.get("limit", default=100, type=int)
        offset = request.args.get("offset", default=0, type=int)
        start_time = request.args.get("start_time", "")
        end_time = request.args.get("end_time", "")
        log_type = request.args.get("log_type", "")

        logger.info(f"获取客户端日志: username={username}, log_type={log_type}, limit={limit}")

        # 连接数据库
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 检查数据库表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='client_logs'")
        table_exists = cursor.fetchone() is not None
        logger.info(f"客户端日志表存在状态: {table_exists}")

        # 检查并创建表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            machine_code TEXT,
            timestamp TEXT,
            message TEXT,
            log_type TEXT,
            log_data TEXT
        )
        """)
        conn.commit()
        logger.info("客户端日志表创建成功")

        # 构建查询
        query = "SELECT * FROM client_logs"
        params = []

        # 添加过滤条件
        conditions = []

        if username:
            conditions.append("username = ?")
            params.append(username)
            logger.info(f"按用户名查询: {username}")

        if start_time:
            conditions.append("timestamp >= ?")
            params.append(start_time)
            logger.info(f"按开始时间查询: {start_time}")

        if end_time:
            conditions.append("timestamp <= ?")
            params.append(end_time)
            logger.info(f"按结束时间查询: {end_time}")

        if log_type:
            conditions.append("log_type = ?")
            params.append(log_type)
            logger.info(f"按日志类型查询: {log_type}")

        if conditions:
            query += " WHERE " + " AND ".join(conditions)

        # 添加排序和分页
        query += " ORDER BY id DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])

        logger.info(f"最终查询SQL: {query}")
        logger.info(f"查询参数: {params}")

        # 执行查询
        try:
            cursor.execute(query, params)
            columns = [col[0] for col in cursor.description]
            logs = [dict(zip(columns, row)) for row in cursor.fetchall()]
            logger.info(f"查询到 {len(logs)} 条日志记录")
        except Exception as e:
            logger.error(f"执行日志查询出错: {str(e)}")
            logger.error(traceback.format_exc())
            logs = []

        # 获取总记录数
        try:
            count_query = "SELECT COUNT(*) FROM client_logs"
            if conditions:
                count_query += " WHERE " + " AND ".join(conditions)

            logger.info(f"总数查询SQL: {count_query}")
            logger.info(f"总数查询参数: {params[:-2] if params else []}")

            cursor.execute(count_query, params[:-2] if params else [])
            total_count = cursor.fetchone()[0]
            logger.info(f"总记录数: {total_count}")
        except Exception as e:
            logger.error(f"获取日志总数出错: {str(e)}")
            logger.error(traceback.format_exc())
            total_count = 0

        conn.close()
        logger.info("数据库连接关闭")

        response_data = {
            "状态": "成功",
            "数据": {
                "日志": logs,
                "总数": total_count,
                "当前页": offset // limit + 1,
                "每页数量": limit,
                "总页数": (total_count + limit - 1) // limit
            }
        }

        logger.info(f"返回日志数据: {len(logs)} 条记录, 总数: {total_count}")
        return jsonify(response_data)
    except Exception as e:
        logger.error(f"获取客户端日志出错: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({"状态": "失败", "信息": f"获取客户端日志出错: {str(e)}"}), 500

@app.route("/admin/api/errors/resolve", methods=["POST"])
@admin_login_required
def admin_resolve_error():
    try:
        data = request.json
        error_id = data.get("error_id", 0)
        resolution_note = data.get("resolution_note", "")

        if not error_id:
            return jsonify({"状态": "失败", "信息": "错误 ID 不能为空"}), 400

        # 标记错误为已解决
        if error_handler:
            result = error_handler.mark_error_resolved(error_id, resolution_note)
            if result:
                return jsonify({
                    "状态": "成功",
                    "信息": "错误已标记为已解决"
                })
            else:
                return jsonify({
                    "状态": "失败",
                    "信息": "标记错误失败"
                }), 500
        else:
            return jsonify({
                "状态": "失败",
                "信息": "错误处理器未初始化"
            }), 500
    except Exception as e:
        logger.error(f"标记错误为已解决出错: {str(e)}")
        return jsonify({"状态": "失败", "信息": f"标记错误为已解决出错: {str(e)}"}), 500

# WebSocket服务器实现
class WebSocketServer:
    def __init__(self, app=None):
        """初始化WebSocket服务器"""
        self.app = app or Flask(__name__)
        # 添加CORS支持
        CORS(self.app, resources={r"/*": {"origins": "*"}})

        # 初始化SocketIO
        self.socketio = SocketIO(
            self.app,
            ping_interval=WS_CONFIG["ping_interval"],
            ping_timeout=WS_CONFIG["ping_timeout"],
            cors_allowed_origins=WS_CONFIG["cors_allowed_origins"],
            path=WS_CONFIG["path"],
            async_mode='threading',
            engineio_logger=True,
            logger=True,
            allow_upgrades=True,
            transports=WS_CONFIG["transports"],
            json=True
        )

        # 导入必要的模块
        from flask_socketio import emit, join_room, leave_room
        import traceback

        # 初始化缓存
        self.token_cache = {}

        # 注册事件处理函数
        self._register_handlers()

        logger.info("WebSocket服务器初始化完成")

    def _authenticate_user(self, token):
        """验证用户token并返回用户信息"""
        # 检查缓存
        if token in self.token_cache:
            cache_data = self.token_cache[token]
            # 检查缓存是否过期（缓存时间5分钟）
            if time.time() - cache_data['timestamp'] < 300:  # 5分钟 = 300秒
                return cache_data['user']

        try:
            # 验证token
            user_data = verify_token(token)
            if not user_data:
                return {"status": "error", "message": "无效的token"}

            username = user_data.get("sub")
            if not username:
                return {"status": "error", "message": "token中缺少用户名"}

            # 更新用户状态为在线（在单独的线程中进行，避免阻塞主线程）
            def update_status():
                try:
                    update_live_status(username, {"is_online": True})
                    logger.info(f"用户 {username} 已通过WebSocket认证并标记为在线")
                except Exception as e:
                    logger.error(f"更新用户 {username} 状态出错: {str(e)}")

            # 启动状态更新线程
            threading.Thread(target=update_status, daemon=True).start()

            # 缓存结果
            result = {"status": "success", "username": username, "message": "认证成功"}
            self.token_cache[token] = {
                'user': result,
                'timestamp': time.time()
            }

            return result
        except Exception as e:
            logger.error(f"验证token出错: {str(e)}")
            return {"status": "error", "message": f"验证出错: {str(e)}"}

    def _register_handlers(self):
        """注册WebSocket事件处理函数"""
        # 导入必要的模块
        from flask_socketio import emit, join_room, leave_room
        import traceback

        # 测试事件
        @self.socketio.on('test')
        def handle_test(data):
            print(f"收到测试事件: {data}")
            emit('test_response', {'status': 'success', 'message': '测试成功'})

        # 连接事件
        @self.socketio.on('connect')
        def handle_connect():
            client_id = request.sid
            logger.info(f"客户端连接: {client_id}")
            ws_clients[client_id] = {
                "id": client_id,
                "username": None,
                "authenticated": False,
                "connected_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "last_activity": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            emit('connection_response', {"status": "connected", "client_id": client_id})
            # 发送连接成功消息
            emit('connect_response', {'status': 'success', 'message': '连接成功'})

        # 断开连接事件
        @self.socketio.on('disconnect')
        def handle_disconnect():
            client_id = request.sid
            if client_id in ws_clients:
                username = ws_clients[client_id].get("username")
                logger.info(f"客户端断开连接: {client_id}, 用户名: {username}")

                # 如果客户端已认证，更新其状态
                if username and ws_clients[client_id].get("authenticated", False):
                    # 这里可以调用更新用户状态的函数，标记为离线
                    try:
                        update_live_status(username, {"is_online": False})
                        logger.info(f"用户 {username} 已标记为离线")
                    except Exception as e:
                        logger.error(f"更新用户 {username} 状态出错: {str(e)}")

                # 离开所有房间
                if client_id in ws_client_rooms:
                    for room in ws_client_rooms[client_id]:
                        leave_room(room)
                    del ws_client_rooms[client_id]

                # 删除客户端记录
                del ws_clients[client_id]

        # 认证事件
        @self.socketio.on('authenticate')
        def handle_authenticate(data):
            client_id = request.sid
            token = data.get('token')

            if not token:
                emit('authentication_response', {"status": "error", "message": "缺少token"})
                return

            try:
                # 验证token
                user = self._authenticate_user(token)
                if not user or not user.get("status") == "success":
                    emit('authentication_response', {"status": "error", "message": "无效的token"})
                    return

                username = user.get("username")
                if not username:
                    emit('authentication_response', {"status": "error", "message": "token中缺少用户名"})
                    return

                # 更新客户端信息
                ws_clients[client_id]["username"] = username
                ws_clients[client_id]["authenticated"] = True
                ws_clients[client_id]["last_activity"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                # 加入用户专属房间
                user_room = f"user_{username}"
                join_room(user_room)
                if client_id not in ws_client_rooms:
                    ws_client_rooms[client_id] = set()
                ws_client_rooms[client_id].add(user_room)

                emit('authentication_response', {
                    "status": "success",
                    "username": username,
                    "message": "认证成功"
                })

            except Exception as e:
                logger.error(f"客户端认证出错: {str(e)}")
                emit('authentication_response', {"status": "error", "message": f"认证出错: {str(e)}"})

        # 状态更新事件
        @self.socketio.on('status_update')
        def handle_status_update(data):
            client_id = request.sid
            if client_id not in ws_clients:
                emit('status_response', {"status": "error", "message": "未知的客户端"})
                return

            if not ws_clients[client_id].get("authenticated", False):
                emit('status_response', {"status": "error", "message": "未认证的客户端"})
                return

            username = ws_clients[client_id].get("username")
            if not username:
                emit('status_response', {"status": "error", "message": "未知的用户名"})
                return

            # 更新客户端活动时间
            ws_clients[client_id]["last_activity"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 更新用户状态
            try:
                # 确保状态中包含在线标志
                status_data = data.copy()
                status_data["is_online"] = True

                # 处理客户端日志（在单独的线程中进行）
                if "client_log" in status_data:
                    client_log = status_data["client_log"]
                    # 从状态数据中移除客户端日志，避免存储到live_status表
                    del status_data["client_log"]

                    # 在单独线程中处理日志
                    def process_log():
                        try:
                            log_type = client_log.get('log_type', 'system')

                            # 如果是弹幕日志，直接输出已格式化的弹幕内容
                            if log_type == 'danmaku':
                                # 直接输出弹幕内容，已经是"时间：发言人：内容"格式
                                logger.info(f"{client_log.get('message', '')}")
                            else:
                                # 其他类型的日志保持原来的格式
                                logger.info(f"收到客户端日志: [{client_log.get('timestamp', '')}] [类型: {log_type}] {client_log.get('message', '')}")

                            # 将日志保存到数据库
                            import sqlite3
                            import json

                            try:
                                # 检查必要字段
                                client_username = client_log.get("username", username)
                                client_machine_code = client_log.get("machine_code", "")
                                client_timestamp = client_log.get("timestamp", "")
                                client_message = client_log.get("message", "")
                                client_log_type = client_log.get("log_type", "")

                                # 只记录正在播放的语音话本和弹幕信息
                                if client_log_type == 'voice' and '正在播放' in client_message:
                                    # 记录正在播放的语音
                                    should_log = True
                                elif client_log_type == 'danmaku':
                                    # 记录弹幕信息 - 已经格式化为"时间：发言人：内容"格式
                                    should_log = True
                                    # 弹幕日志内容已经格式化，直接使用
                                    client_message = client_message
                                else:
                                    # 其他类型的日志不记录
                                    should_log = False

                                if should_log:
                                    # 连接数据库
                                    conn = get_sqlite_connection()
                                    cursor = conn.cursor()

                                    # 插入日志记录
                                    insert_query = """
                                    INSERT INTO client_logs (username, machine_code, timestamp, message, log_type, log_data)
                                    VALUES (?, ?, ?, ?, ?, ?)
                                    """

                                    insert_params = (
                                        client_username,
                                        client_machine_code,
                                        client_timestamp,
                                        client_message,
                                        log_type,
                                        json.dumps(client_log)
                                    )

                                    cursor.execute(insert_query, insert_params)
                                    conn.commit()
                                    conn.close()
                                    # 如果是弹幕日志，简化输出
                                    if client_log_type == 'danmaku':
                                        # 不输出额外的描述文本
                                        pass  # 已经在前面输出了弹幕内容，这里不需要重复输出
                                    else:
                                        # 其他类型的日志保持原来的格式
                                        logger.info(f"已记录客户端日志: {client_log_type} - {client_message[:50]}...")
                            except Exception as e:
                                logger.error(f"保存客户端日志到数据库出错: {str(e)}")
                        except Exception as e:
                            logger.error(f"处理客户端日志出错: {str(e)}")

                    # 启动日志处理线程
                    threading.Thread(target=process_log, daemon=True).start()

                # 在单独线程中更新状态
                def update_status():
                    try:
                        update_result = update_live_status(username, status_data)
                        if not update_result:
                            logger.error(f"更新用户 {username} 状态失败")

                        # 如果有监控系统，也更新监控系统中的状态
                        try:
                            monitor = get_monitor_system()
                            if monitor:
                                monitor.update_client_status(username, status_data)
                        except Exception as e:
                            logger.error(f"更新监控系统状态出错: {str(e)}")
                    except Exception as e:
                        logger.error(f"更新用户 {username} 状态出错: {str(e)}")

                # 启动状态更新线程
                threading.Thread(target=update_status, daemon=True).start()

                # 立即响应客户端，不等待数据库操作
                emit('status_response', {"status": "success", "message": "状态更新成功"})
            except Exception as e:
                logger.error(f"处理状态更新请求出错: {str(e)}")
                emit('status_response', {"status": "error", "message": f"状态更新出错: {str(e)}"})

        # 心跳事件
        @self.socketio.on('ping')
        def handle_ping(data=None):
            client_id = request.sid
            if client_id in ws_clients:
                ws_clients[client_id]["last_activity"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                emit('pong', {"timestamp": time.time()})

    def send_to_client(self, client_id, event, data):
        """向指定客户端发送事件"""
        if client_id in ws_clients:
            try:
                self.socketio.emit(event, data, room=client_id)
                return True
            except Exception as e:
                logger.error(f"向客户端 {client_id} 发送事件 {event} 出错: {str(e)}")
        return False

    def send_to_user(self, username, event, data):
        """向指定用户发送事件"""
        if not username:
            return False

        user_room = f"user_{username}"
        try:
            self.socketio.emit(event, data, room=user_room)
            return True
        except Exception as e:
            logger.error(f"向用户 {username} 发送事件 {event} 出错: {str(e)}")
        return False

    def broadcast(self, event, data, include_anonymous=False):
        """广播事件到所有客户端"""
        try:
            if include_anonymous:
                # 广播给所有客户端
                self.socketio.emit(event, data)
            else:
                # 只广播给已认证的客户端
                authenticated_clients = [c_id for c_id, c in ws_clients.items() if c.get("authenticated", False)]
                for client_id in authenticated_clients:
                    self.socketio.emit(event, data, room=client_id)
            return True
        except Exception as e:
            logger.error(f"广播事件 {event} 出错: {str(e)}")
        return False

    def get_clients(self):
        """获取所有客户端信息"""
        return ws_clients

    def get_client(self, client_id):
        """获取指定客户端信息"""
        return ws_clients.get(client_id)

    def get_user_clients(self, username):
        """获取指定用户的所有客户端"""
        if not username:
            return []

        return [c_id for c_id, c in ws_clients.items() if c.get("username") == username]

    def run(self, host='0.0.0.0', port=8888, debug=False):
        """运行WebSocket服务器"""
        # 添加一个默认路由，以支持WebSocket连接
        @self.app.route('/')
        def index():
            return 'WebSocket Server is running!'

        # 添加其他HTTP路由以支持非线程模式
        @self.app.route('/authenticate', methods=['POST'])
        def authenticate_route():
            data = request.json
            # 处理认证请求
            if 'authenticate' in data and 'token' in data['authenticate']:
                token = data['authenticate']['token']
                username = data['authenticate'].get('username')

                # 验证token
                try:
                    # 使用导入的verify_token函数
                    if verify_token(token):
                        # 如果有用户名，更新在线状态
                        if username:
                            try:
                                update_live_status(username, {"is_online": True})
                            except Exception as e:
                                logger.error(f"更新用户 {username} 状态出错: {str(e)}")

                        return jsonify({
                            "status": "success",
                            "username": username,
                            "message": "认证成功"
                        })
                    else:
                        return jsonify({"status": "error", "message": "无效的token"})
                except Exception as e:
                    logger.error(f"认证出错: {str(e)}")
                    return jsonify({"status": "error", "message": f"认证出错: {str(e)}"})
            return jsonify({"status": "error", "message": "缺少认证信息"})

        @self.app.route('/status', methods=['POST'])
        def status_route():
            data = request.json
            # 处理状态更新请求
            if 'status_update' in data:
                status_data = data['status_update']

                # 如果有用户名，更新用户状态
                username = status_data.get('username')
                if username:
                    try:
                        # 确保状态中包含在线标志
                        status_data["is_online"] = True
                        update_live_status(username, status_data)
                        logger.info(f"已更新用户 {username} 状态")
                    except Exception as e:
                        logger.error(f"更新用户 {username} 状态出错: {str(e)}")

                return jsonify({"status": "success", "message": "状态更新成功"})
            return jsonify({"status": "error", "message": "缺少状态信息"})

        @self.app.route('/ping', methods=['POST'])
        def ping_route():
            # 处理心跳请求
            return jsonify({"pong": {"timestamp": time.time()}})

        @self.app.route('/message', methods=['POST'])
        def message_route():
            # 处理通用消息
            return jsonify({"status": "success", "message": "消息已接收"})

        logger.info(f"WebSocket服务器正在启动，监听 {host}:{port}")
        # 使用默认方式运行服务器，但优化配置
        # 添加调试日志
        print(f"WebSocket服务器正在启动，监听 {host}:{port}")
        logger.info(f"WebSocket服务器正在启动，监听 {host}:{port}")
        self.socketio.run(self.app, host=host, port=port, debug=debug, use_reloader=False, log_output=True, allow_unsafe_werkzeug=True)

# 全局WebSocket服务器实例
websocket_server = None

def init_websocket_server(app=None):
    """初始化WebSocket服务器"""
    global websocket_server
    websocket_server = WebSocketServer(app)

    # 初始化客户端日志表
    try:
        conn = get_sqlite_connection()
        cursor = conn.cursor()

        # 创建客户端日志表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            machine_code TEXT,
            timestamp TEXT,
            message TEXT,
            log_type TEXT,
            log_data TEXT
        )
        """)

        # 创建弹幕记录表（如果不存在）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS danmaku_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,            -- 账号用户名
            sender TEXT,              -- 发送弹幕的用户名
            content TEXT,             -- 弹幕内容
            timestamp TEXT,           -- 时间戳
            created_at TEXT           -- 记录创建时间
        )
        """)

        conn.commit()
        conn.close()
        logger.info("客户端日志表和弹幕记录表初始化成功")
    except Exception as e:
        logger.error(f"初始化数据库表出错: {str(e)}")

    return websocket_server

def get_websocket_server():
    """获取WebSocket服务器实例"""
    global websocket_server
    return websocket_server


def init_database_tables():
    """初始化数据库表"""
    try:
        conn = get_sqlite_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("数据库表初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库表初始化失败: {str(e)}")
        return False


def init_all_database_tables():
    """初始化所有数据库表"""
    try:
        from user_manager import get_sqlite_connection
        conn = get_sqlite_connection()
        if not conn:
            logger.error("无法连接到数据库")
            return False
        
        cursor = conn.cursor()
        
        # 创建client_updates表（包含所有必要的列）
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS client_updates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            version TEXT NOT NULL,
            release_date TEXT NOT NULL,
            description TEXT,
            download_url TEXT NOT NULL,
            force_update INTEGER DEFAULT 0,
            is_current INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            file_path TEXT,
            file_size INTEGER,
            file_hash TEXT,
            download_count INTEGER DEFAULT 0,
            status TEXT DEFAULT "pending",
            fast_download_url TEXT,
            is_exe INTEGER DEFAULT 0,
            is_folder_update INTEGER DEFAULT 0
        )
        """)
        
        # 检查并添加缺失的列
        cursor.execute("PRAGMA table_info(client_updates)")
        existing_columns = [row[1] for row in cursor.fetchall()]
        
        required_columns = {
            "fast_download_url": "TEXT",
            "is_exe": "INTEGER DEFAULT 0",
            "is_folder_update": "INTEGER DEFAULT 0"
        }
        
        for column, column_type in required_columns.items():
            if column not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE client_updates ADD COLUMN {column} {column_type}")
                    logger.info(f"添加列: {column}")
                except Exception as e:
                    logger.warning(f"添加列 {column} 失败: {str(e)}")
        
        # 创建API配置表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS api_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT UNIQUE NOT NULL,
            api_path TEXT NOT NULL,
            api_title TEXT NOT NULL,
            api_description TEXT,
            response_content TEXT NOT NULL,
            is_enabled INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        # 创建其他必要的表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            ip TEXT,
            action TEXT NOT NULL,
            details TEXT,
            time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS live_status (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            login_time TIMESTAMP,
            last_update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            voice_play_time INTEGER DEFAULT 0,
            current_script TEXT,
            online_count INTEGER DEFAULT 0,
            danmaku TEXT,
            danmaku_history TEXT,
            voice_history TEXT,
            obs_source TEXT,
            token TEXT,
            ip TEXT,
            machine_code TEXT,
            is_online INTEGER DEFAULT 1
        )
        """)
        
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL,
            token TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            is_active INTEGER DEFAULT 1,
            ip TEXT,
            machine_code TEXT
        )
        """)
        
        conn.commit()
        conn.close()
        
        logger.info("所有数据库表初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"数据库表初始化失败: {str(e)}")
        return False

if __name__ == "__main__":
    import platform
    import sys

    # 初始化监控系统
    try:
        monitor = init_monitor_system(app=app, socketio=socketio)
        logger.info("监控系统初始化成功")
    except Exception as e:
        logger.error(f"监控系统初始化失败: {str(e)}")

    # 已删除WebSocket服务器初始化代码

    # 检查操作系统
    if platform.system() == "Windows":
        # Windows 环境下尝试多种启动方式
        try:
            # 尝试方式1: 使用 waitress
            try:
                import waitress
                logger.info("使用 waitress 启动服务器...")
                # 增加上传文件大小限制
                waitress.serve(
                    app,
                    host="0.0.0.0",
                    port=12456,
                    max_request_body_size=1024*1024*1024,  # 1GB
                    threads=8  # 增加线程数，提高并发处理能力
                )
            except ImportError:
                # 如果没有安装 waitress，尝试方式2
                logger.info("waitress 未安装，尝试使用其他方式...")
                # 尝试方式2: 使用 werkzeug
                try:
                    from werkzeug.serving import run_simple
                    logger.info("使用 werkzeug 启动服务器...")
                    run_simple("0.0.0.0", 12456, app, use_debugger=True, use_reloader=False)
                except Exception as e:
                    logger.error(f"werkzeug 启动失败: {str(e)}")
                    # 如果方式2失败，尝试方式3
                    logger.info("尝试使用最简单的方式启动...")
                    app.run(host="0.0.0.0", port=12456)
        except Exception as e:
            logger.error(f"启动服务器出错: {str(e)}")
            # 如果所有方法失败，尝试最简单的方式
            logger.info("尝试使用最简单的方式启动...")
            app.run(host="0.0.0.0", port=12456)
    else:
        # 非 Windows 环境使用标准配置
        app.run(host="0.0.0.0", port=12456, debug=True)