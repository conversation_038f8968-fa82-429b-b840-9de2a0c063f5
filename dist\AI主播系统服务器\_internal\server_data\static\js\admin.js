// 管理员后台脚本
document.addEventListener('DOMContentLoaded', function() {
    // 侧边栏菜单激活状态
    const currentPath = window.location.pathname;
    const sidebarLinks = document.querySelectorAll('.sidebar a');
    
    sidebarLinks.forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            link.classList.add('active');
        }
    });
    
    // 退出登录
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function() {
            if (confirm('确定要退出登录吗？')) {
                fetch('/admin/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        window.location.href = '/admin';
                    } else {
                        alert('退出失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('退出请求出错:', error);
                    alert('退出请求出错，请重试');
                });
            }
        });
    }
    
    // 模态框通用函数
    window.showModal = function(modalId) {
        document.getElementById(modalId).style.display = 'block';
    };
    
    window.closeModal = function(modalId) {
        document.getElementById(modalId).style.display = 'none';
    };
    
    // 关闭模态框按钮
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modalId = this.closest('.modal').id;
            window.closeModal(modalId);
        });
    });
    
    // 点击模态框外部关闭
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === this) {
                window.closeModal(this.id);
            }
        });
    });
    
    // 分页处理
    const paginationLinks = document.querySelectorAll('.pagination a');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (!this.classList.contains('active') && !this.classList.contains('disabled')) {
                const page = this.getAttribute('data-page');
                if (page) {
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('page', page);
                    window.location.href = currentUrl.toString();
                }
            }
            e.preventDefault();
        });
    });
});

// 用户管理页面脚本
function initUserManagement() {
    // 编辑用户
    const editUserButtons = document.querySelectorAll('.edit-user-btn');
    editUserButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            const phone = this.getAttribute('data-phone');
            const machineCode = this.getAttribute('data-machine-code');
            const expireTime = this.getAttribute('data-expire-time');
            const status = this.getAttribute('data-status');
            const remark = this.getAttribute('data-remark');
            
            document.getElementById('edit-user-id').value = userId;
            document.getElementById('edit-username').value = username;
            document.getElementById('edit-phone').value = phone || '';
            document.getElementById('edit-machine-code').value = machineCode || '';
            document.getElementById('edit-expire-time').value = expireTime || '';
            document.getElementById('edit-status').value = status;
            document.getElementById('edit-remark').value = remark || '';
            
            window.showModal('edit-user-modal');
        });
    });
    
    // 提交编辑用户表单
    const editUserForm = document.getElementById('edit-user-form');
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const userId = document.getElementById('edit-user-id').value;
            const phone = document.getElementById('edit-phone').value;
            const machineCode = document.getElementById('edit-machine-code').value;
            const expireTime = document.getElementById('edit-expire-time').value;
            const status = document.getElementById('edit-status').value;
            const remark = document.getElementById('edit-remark').value;
            
            const data = {
                phone: phone,
                machine_code: machineCode,
                expire_time: expireTime,
                status: parseInt(status),
                remark: remark
            };
            
            fetch(`/admin/users/${userId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    alert('用户信息更新成功');
                    window.location.reload();
                } else {
                    alert('更新失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('更新请求出错:', error);
                alert('更新请求出错，请重试');
            });
        });
    }
    
    // 删除用户
    const deleteUserButtons = document.querySelectorAll('.delete-user-btn');
    deleteUserButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const username = this.getAttribute('data-username');
            
            if (confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复！`)) {
                fetch(`/admin/users/${userId}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        alert('用户删除成功');
                        window.location.reload();
                    } else {
                        alert('删除失败: ' + data.信息);
                    }
                })
                .catch(error => {
                    console.error('删除请求出错:', error);
                    alert('删除请求出错，请重试');
                });
            }
        });
    });
}

// 卡密管理页面脚本
function initCardManagement() {
    // 生成卡密表单提交
    const generateCardForm = document.getElementById('generate-card-form');
    if (generateCardForm) {
        generateCardForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const days = document.getElementById('card-days').value;
            const count = document.getElementById('card-count').value;
            
            const data = {
                days: parseInt(days),
                count: parseInt(count)
            };
            
            fetch('/admin/cards/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.状态 === '成功') {
                    let message = `成功生成 ${data.卡密列表.length} 个卡密：\n\n`;
                    data.卡密列表.forEach(card => {
                        message += card + '\n';
                    });
                    alert(message);
                    window.location.reload();
                } else {
                    alert('生成失败: ' + data.信息);
                }
            })
            .catch(error => {
                console.error('生成请求出错:', error);
                alert('生成请求出错，请重试');
            });
        });
    }
    
    // 卡密状态筛选
    const cardStatusFilter = document.getElementById('card-status-filter');
    if (cardStatusFilter) {
        cardStatusFilter.addEventListener('change', function() {
            const status = this.value;
            const currentUrl = new URL(window.location.href);
            
            if (status === 'all') {
                currentUrl.searchParams.delete('status');
            } else {
                currentUrl.searchParams.set('status', status);
            }
            
            currentUrl.searchParams.set('page', '1');
            window.location.href = currentUrl.toString();
        });
    }
}

// 日志管理页面脚本
function initLogManagement() {
    // 用户名筛选
    const usernameFilterForm = document.getElementById('username-filter-form');
    if (usernameFilterForm) {
        usernameFilterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username-filter').value.trim();
            const currentUrl = new URL(window.location.href);
            
            if (username) {
                currentUrl.searchParams.set('username', username);
            } else {
                currentUrl.searchParams.delete('username');
            }
            
            currentUrl.searchParams.set('page', '1');
            window.location.href = currentUrl.toString();
        });
    }
    
    // 清除筛选
    const clearFilterBtn = document.getElementById('clear-filter-btn');
    if (clearFilterBtn) {
        clearFilterBtn.addEventListener('click', function() {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.delete('username');
            currentUrl.searchParams.set('page', '1');
            window.location.href = currentUrl.toString();
        });
    }
}
