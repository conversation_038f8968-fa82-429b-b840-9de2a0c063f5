<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>

    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard" class="active"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/live_status"><i class="fas fa-broadcast-tower"></i> 直播状态</a></li>
            <li><a href="/admin/settings"><i class="fas fa-cog"></i> 系统设置</a></li>
            <li><a href="/admin/update_management"><i class="fas fa-sync"></i> 更新管理</a></li>
            <li><a href="/admin/api_management"><i class="fas fa-code"></i> API管理</a></li>
        </ul>
    </div>

    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>系统概览</h2>
            </div>
            <div class="card-body">
                <div class="dashboard-stats">
                    <div class="stat-card users">
                        <h3>用户总数</h3>
                        <div class="number" id="total-users">--</div>
                    </div>
                    <div class="stat-card cards">
                        <h3>卡密总数</h3>
                        <div class="number" id="total-cards">--</div>
                    </div>
                    <div class="stat-card expired">
                        <h3>已过期用户</h3>
                        <div class="number" id="expired-users">--</div>
                    </div>
                    <div class="stat-card logs">
                        <h3>操作日志</h3>
                        <div class="number" id="total-logs">--</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>最近注册用户</h2>
                <a href="/admin/user_management" class="btn btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                <table class="table" id="recent-users-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>手机号</th>
                            <th>注册时间</th>
                            <th>到期时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="6" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>最近操作日志</h2>
                <a href="/admin/log_management" class="btn btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                <table class="table" id="recent-logs-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>IP地址</th>
                            <th>操作</th>
                            <th>时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="5" style="text-align: center;">加载中...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="/static/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取统计数据
            fetchStats();

            // 获取最近用户
            fetchRecentUsers();

            // 获取最近日志
            fetchRecentLogs();
        });

        function fetchStats() {
            // 获取用户总数
            fetch('/admin/users?page=1&page_size=1')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        document.getElementById('total-users').textContent = data.数据.总数;
                    }
                })
                .catch(error => {
                    console.error('获取用户总数出错:', error);
                });

            // 获取卡密总数
            fetch('/admin/cards?page=1&page_size=1')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        document.getElementById('total-cards').textContent = data.数据.总数;
                    }
                })
                .catch(error => {
                    console.error('获取卡密总数出错:', error);
                });

            // 获取日志总数
            fetch('/admin/logs?page=1&page_size=1')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        document.getElementById('total-logs').textContent = data.数据.总数;
                    }
                })
                .catch(error => {
                    console.error('获取日志总数出错:', error);
                });

            // 这里需要后端提供一个API来获取已过期用户数量
            // 暂时使用占位符
            document.getElementById('expired-users').textContent = "--";
        }

        function fetchRecentUsers() {
            fetch('/admin/users?page=1&page_size=5')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const users = data.数据.用户列表;
                        const tbody = document.querySelector('#recent-users-table tbody');

                        if (users.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        users.forEach(user => {
                            const statusText = user.status === 1 ? '正常' : '封禁';
                            const statusClass = user.status === 1 ? 'success' : 'danger';

                            tbody.innerHTML += `
                                <tr>
                                    <td>${user.id}</td>
                                    <td>${user.username}</td>
                                    <td>${user.phone || '--'}</td>
                                    <td>${user.register_time}</td>
                                    <td>${user.expire_time || '--'}</td>
                                    <td><span class="btn btn-sm btn-${statusClass}">${statusText}</span></td>
                                </tr>
                            `;
                        });
                    }
                })
                .catch(error => {
                    console.error('获取最近用户出错:', error);
                    const tbody = document.querySelector('#recent-users-table tbody');
                    tbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">加载失败</td></tr>';
                });
        }

        function fetchRecentLogs() {
            fetch('/admin/logs?page=1&page_size=5')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const logs = data.数据.日志列表;
                        const tbody = document.querySelector('#recent-logs-table tbody');

                        if (logs.length === 0) {
                            tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">暂无数据</td></tr>';
                            return;
                        }

                        tbody.innerHTML = '';
                        logs.forEach(log => {
                            tbody.innerHTML += `
                                <tr>
                                    <td>${log.id}</td>
                                    <td>${log.username || '--'}</td>
                                    <td>${log.ip || '--'}</td>
                                    <td>${log.action}</td>
                                    <td>${log.time}</td>
                                </tr>
                            `;
                        });
                    }
                })
                .catch(error => {
                    console.error('获取最近日志出错:', error);
                    const tbody = document.querySelector('#recent-logs-table tbody');
                    tbody.innerHTML = '<tr><td colspan="5" style="text-align: center;">加载失败</td></tr>';
                });
        }
    </script>
</body>
</html>
