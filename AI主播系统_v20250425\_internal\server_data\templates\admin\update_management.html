<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新管理 - AI主播系统</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        .update-form {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .update-form .form-group {
            margin-bottom: 15px;
        }
        .update-form textarea {
            min-height: 100px;
        }
        .update-list {
            margin-top: 20px;
        }
        .update-item {
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .update-item.force {
            border-left: 4px solid #f44336;
        }
        .update-item h3 {
            margin-top: 0;
            display: flex;
            justify-content: space-between;
        }
        .update-item .version-tag {
            background-color: #4CAF50;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 0.8em;
        }
        .update-item.force .version-tag {
            background-color: #f44336;
        }
        .update-item .description {
            white-space: pre-line;
            margin: 10px 0;
        }
        .update-item .meta {
            font-size: 0.9em;
            color: #666;
        }
        .update-actions {
            margin-top: 10px;
        }
        .file-upload-container {
            margin-top: 20px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border: 2px dashed #ccc;
            text-align: center;
        }
        .file-upload-container.dragover {
            border-color: #4CAF50;
            background-color: #e8f5e9;
        }
        .file-upload-container p {
            margin: 0;
            padding: 10px;
        }
        .file-upload-container input[type="file"] {
            display: none;
        }
        .file-upload-container label {
            display: inline-block;
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .file-upload-container label:hover {
            background-color: #388E3C;
        }
        .upload-progress {
            margin-top: 10px;
            display: none;
        }
        .upload-progress .progress-bar {
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
        }
        .upload-progress .progress-bar-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
        .upload-progress .progress-text {
            margin-top: 5px;
            font-size: 0.9em;
            text-align: center;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #f9f9f9;
            border-color: #ddd;
            border-bottom: 1px solid #f9f9f9;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI主播系统管理后台</h1>
        <div class="user-info">
            <span>管理员</span>
            <button id="logout-btn" class="logout-btn">退出登录</button>
        </div>
    </div>
    
    <div class="sidebar">
        <ul>
            <li><a href="/admin/dashboard"><i class="fas fa-tachometer-alt"></i> 仪表盘</a></li>
            <li><a href="/admin/user_management"><i class="fas fa-users"></i> 用户管理</a></li>
            <li><a href="/admin/card_management"><i class="fas fa-credit-card"></i> 卡密管理</a></li>
            <li><a href="/admin/log_management"><i class="fas fa-history"></i> 日志查看</a></li>
            <li><a href="/admin/update_management" class="active"><i class="fas fa-sync"></i> 更新管理</a></li>
        </ul>
    </div>
    
    <div class="main-content">
        <div class="card">
            <div class="card-header">
                <h2>更新管理</h2>
            </div>
            <div class="card-body">
                <div class="tabs">
                    <div class="tab active" data-tab="updates-list">更新列表</div>
                    <div class="tab" data-tab="add-update">添加更新</div>
                    <div class="tab" data-tab="upload-package">上传更新包</div>
                </div>
                
                <!-- 更新列表 -->
                <div class="tab-content active" id="updates-list">
                    <div class="update-list" id="update-items-container">
                        <p>加载中...</p>
                    </div>
                </div>
                
                <!-- 添加更新 -->
                <div class="tab-content" id="add-update">
                    <div class="update-form">
                        <h3>添加新版本</h3>
                        <form id="update-form">
                            <div class="form-group">
                                <label for="version">版本号</label>
                                <input type="text" id="version" class="form-control" placeholder="例如: 1.1.0" required>
                            </div>
                            <div class="form-group">
                                <label for="release-date">发布日期</label>
                                <input type="date" id="release-date" class="form-control" required>
                            </div>
                            <div class="form-group">
                                <label for="description">更新内容</label>
                                <textarea id="description" class="form-control" placeholder="每行一个更新内容，例如:&#10;1. 修复了xxx问题&#10;2. 优化了xxx功能" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="download-url">下载地址</label>
                                <input type="text" id="download-url" class="form-control" placeholder="例如: /downloads/update_1.1.0.zip" required>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="force-update"> 强制更新
                                </label>
                                <small style="display: block; margin-top: 5px; color: #666;">如果勾选，用户必须更新才能继续使用软件</small>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">保存</button>
                                <button type="button" id="cancel-update-btn" class="btn btn-secondary">取消</button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- 上传更新包 -->
                <div class="tab-content" id="upload-package">
                    <div class="file-upload-container" id="drop-area">
                        <p>拖放更新包文件到这里，或者点击下方按钮选择文件</p>
                        <input type="file" id="file-upload" accept=".zip">
                        <label for="file-upload">选择文件</label>
                        <div class="upload-progress" id="upload-progress">
                            <div class="progress-bar">
                                <div class="progress-bar-fill" id="progress-bar-fill"></div>
                            </div>
                            <div class="progress-text" id="progress-text">0%</div>
                        </div>
                    </div>
                    <div id="upload-result" style="margin-top: 20px;"></div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="/static/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化标签页
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.getAttribute('data-tab');
                    
                    // 移除所有标签页的激活状态
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // 激活当前标签页
                    tab.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
            
            // 加载更新列表
            loadUpdates();
            
            // 初始化更新表单
            initUpdateForm();
            
            // 初始化文件上传
            initFileUpload();
        });
        
        // 加载更新列表
        function loadUpdates() {
            const container = document.getElementById('update-items-container');
            container.innerHTML = '<p>加载中...</p>';
            
            fetch('/admin/api/updates/file')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const updates = data.数据.versions;
                        if (updates && updates.length > 0) {
                            container.innerHTML = '';
                            updates.forEach(update => {
                                const updateItem = document.createElement('div');
                                updateItem.className = update.force_update ? 'update-item force' : 'update-item';
                                updateItem.innerHTML = `
                                    <h3>
                                        版本 ${update.version}
                                        <span class="version-tag">${update.force_update ? '强制更新' : '普通更新'}</span>
                                    </h3>
                                    <div class="meta">发布日期: ${update.release_date}</div>
                                    <div class="description">${update.description}</div>
                                    <div class="meta">下载地址: ${update.download_url}</div>
                                    <div class="update-actions">
                                        <button class="btn btn-sm btn-primary edit-update-btn" data-version="${update.version}">编辑</button>
                                        <button class="btn btn-sm btn-danger delete-update-btn" data-version="${update.version}">删除</button>
                                    </div>
                                `;
                                container.appendChild(updateItem);
                            });
                            
                            // 添加编辑和删除事件
                            document.querySelectorAll('.edit-update-btn').forEach(btn => {
                                btn.addEventListener('click', function() {
                                    const version = this.getAttribute('data-version');
                                    editUpdate(version, updates);
                                });
                            });
                            
                            document.querySelectorAll('.delete-update-btn').forEach(btn => {
                                btn.addEventListener('click', function() {
                                    const version = this.getAttribute('data-version');
                                    deleteUpdate(version, updates);
                                });
                            });
                        } else {
                            container.innerHTML = '<p>暂无更新信息</p>';
                        }
                    } else {
                        container.innerHTML = `<p>加载失败: ${data.信息 || '未知错误'}</p>`;
                    }
                })
                .catch(error => {
                    container.innerHTML = `<p>加载失败: ${error.message}</p>`;
                });
        }
        
        // 初始化更新表单
        function initUpdateForm() {
            const form = document.getElementById('update-form');
            const cancelBtn = document.getElementById('cancel-update-btn');
            
            // 设置默认日期为今天
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            document.getElementById('release-date').value = `${year}-${month}-${day}`;
            
            // 表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const version = document.getElementById('version').value;
                const releaseDate = document.getElementById('release-date').value;
                const description = document.getElementById('description').value;
                const downloadUrl = document.getElementById('download-url').value;
                const forceUpdate = document.getElementById('force-update').checked;
                
                // 验证表单
                if (!version || !releaseDate || !description || !downloadUrl) {
                    alert('请填写所有必填字段');
                    return;
                }
                
                // 获取当前更新列表
                fetch('/admin/api/updates/file')
                    .then(response => response.json())
                    .then(data => {
                        if (data.状态 === '成功') {
                            const updates = data.数据;
                            const versions = updates.versions || [];
                            
                            // 检查是否是编辑模式
                            const editIndex = versions.findIndex(v => v.version === version);
                            
                            // 创建新的更新对象
                            const newUpdate = {
                                version: version,
                                release_date: releaseDate,
                                description: description,
                                download_url: downloadUrl,
                                force_update: forceUpdate
                            };
                            
                            if (editIndex !== -1) {
                                // 编辑现有版本
                                versions[editIndex] = newUpdate;
                            } else {
                                // 添加新版本（放在最前面）
                                versions.unshift(newUpdate);
                            }
                            
                            // 保存更新列表
                            fetch('/admin/api/updates/file', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    versions: versions
                                })
                            })
                            .then(response => response.json())
                            .then(result => {
                                if (result.状态 === '成功') {
                                    alert('更新信息保存成功');
                                    resetUpdateForm();
                                    loadUpdates();
                                    
                                    // 切换到更新列表标签页
                                    document.querySelector('.tab[data-tab="updates-list"]').click();
                                } else {
                                    alert(`保存失败: ${result.信息 || '未知错误'}`);
                                }
                            })
                            .catch(error => {
                                alert(`保存失败: ${error.message}`);
                            });
                        } else {
                            alert(`获取更新列表失败: ${data.信息 || '未知错误'}`);
                        }
                    })
                    .catch(error => {
                        alert(`获取更新列表失败: ${error.message}`);
                    });
            });
            
            // 取消按钮
            cancelBtn.addEventListener('click', function() {
                resetUpdateForm();
                document.querySelector('.tab[data-tab="updates-list"]').click();
            });
        }
        
        // 重置更新表单
        function resetUpdateForm() {
            document.getElementById('update-form').reset();
            
            // 设置默认日期为今天
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            document.getElementById('release-date').value = `${year}-${month}-${day}`;
            
            // 移除编辑模式标记
            document.getElementById('update-form').removeAttribute('data-edit-mode');
        }
        
        // 编辑更新
        function editUpdate(version, updates) {
            const update = updates.find(u => u.version === version);
            if (!update) {
                alert('找不到指定版本的更新信息');
                return;
            }
            
            // 填充表单
            document.getElementById('version').value = update.version;
            document.getElementById('release-date').value = update.release_date;
            document.getElementById('description').value = update.description;
            document.getElementById('download-url').value = update.download_url;
            document.getElementById('force-update').checked = update.force_update;
            
            // 标记为编辑模式
            document.getElementById('update-form').setAttribute('data-edit-mode', 'true');
            
            // 切换到添加更新标签页
            document.querySelector('.tab[data-tab="add-update"]').click();
        }
        
        // 删除更新
        function deleteUpdate(version, updates) {
            if (!confirm(`确定要删除版本 ${version} 的更新信息吗？`)) {
                return;
            }
            
            // 获取当前更新列表
            fetch('/admin/api/updates/file')
                .then(response => response.json())
                .then(data => {
                    if (data.状态 === '成功') {
                        const updatesData = data.数据;
                        const versions = updatesData.versions || [];
                        
                        // 过滤掉要删除的版本
                        const newVersions = versions.filter(v => v.version !== version);
                        
                        // 保存更新列表
                        fetch('/admin/api/updates/file', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                versions: newVersions
                            })
                        })
                        .then(response => response.json())
                        .then(result => {
                            if (result.状态 === '成功') {
                                alert('更新信息删除成功');
                                loadUpdates();
                            } else {
                                alert(`删除失败: ${result.信息 || '未知错误'}`);
                            }
                        })
                        .catch(error => {
                            alert(`删除失败: ${error.message}`);
                        });
                    } else {
                        alert(`获取更新列表失败: ${data.信息 || '未知错误'}`);
                    }
                })
                .catch(error => {
                    alert(`获取更新列表失败: ${error.message}`);
                });
        }
        
        // 初始化文件上传
        function initFileUpload() {
            const dropArea = document.getElementById('drop-area');
            const fileInput = document.getElementById('file-upload');
            const progressBar = document.getElementById('progress-bar-fill');
            const progressText = document.getElementById('progress-text');
            const progressContainer = document.getElementById('upload-progress');
            const uploadResult = document.getElementById('upload-result');
            
            // 阻止默认拖放行为
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // 高亮拖放区域
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });
            
            function highlight() {
                dropArea.classList.add('dragover');
            }
            
            function unhighlight() {
                dropArea.classList.remove('dragover');
            }
            
            // 处理拖放文件
            dropArea.addEventListener('drop', handleDrop, false);
            
            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                
                if (files.length > 0) {
                    handleFiles(files[0]);
                }
            }
            
            // 处理文件选择
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    handleFiles(this.files[0]);
                }
            });
            
            // 处理文件上传
            function handleFiles(file) {
                uploadFile(file);
            }
            
            function uploadFile(file) {
                // 检查文件类型
                if (!file.name.endsWith('.zip')) {
                    alert('请上传ZIP格式的更新包文件');
                    return;
                }
                
                // 显示进度条
                progressContainer.style.display = 'block';
                progressBar.style.width = '0%';
                progressText.textContent = '0%';
                uploadResult.innerHTML = '';
                
                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);
                
                // 创建XMLHttpRequest对象
                const xhr = new XMLHttpRequest();
                
                // 监听上传进度
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        progressBar.style.width = percentComplete + '%';
                        progressText.textContent = percentComplete + '%';
                    }
                });
                
                // 监听上传完成
                xhr.addEventListener('load', function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.状态 === '成功') {
                                uploadResult.innerHTML = `
                                    <div class="alert alert-success">
                                        <p>文件上传成功</p>
                                        <p>文件名: ${response.数据.filename}</p>
                                        <p>文件大小: ${formatFileSize(response.数据.size)}</p>
                                        <p>下载地址: ${response.数据.download_url}</p>
                                    </div>
                                `;
                                
                                // 自动填充下载地址到添加更新表单
                                document.getElementById('download-url').value = response.数据.download_url;
                                
                                // 提取版本号
                                const versionMatch = file.name.match(/update_(.+)\.zip/);
                                if (versionMatch && versionMatch[1]) {
                                    document.getElementById('version').value = versionMatch[1];
                                }
                                
                                // 切换到添加更新标签页
                                document.querySelector('.tab[data-tab="add-update"]').click();
                            } else {
                                uploadResult.innerHTML = `
                                    <div class="alert alert-danger">
                                        <p>上传失败: ${response.信息 || '未知错误'}</p>
                                    </div>
                                `;
                            }
                        } catch (e) {
                            uploadResult.innerHTML = `
                                <div class="alert alert-danger">
                                    <p>解析响应失败: ${e.message}</p>
                                </div>
                            `;
                        }
                    } else {
                        uploadResult.innerHTML = `
                            <div class="alert alert-danger">
                                <p>上传失败: 服务器返回状态码 ${xhr.status}</p>
                            </div>
                        `;
                    }
                });
                
                // 监听上传错误
                xhr.addEventListener('error', function() {
                    uploadResult.innerHTML = `
                        <div class="alert alert-danger">
                            <p>上传失败: 网络错误</p>
                        </div>
                    `;
                });
                
                // 监听上传中止
                xhr.addEventListener('abort', function() {
                    uploadResult.innerHTML = `
                        <div class="alert alert-warning">
                            <p>上传已取消</p>
                        </div>
                    `;
                });
                
                // 发送请求
                xhr.open('POST', '/admin/api/upload-update');
                xhr.send(formData);
            }
            
            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }
    </script>
</body>
</html>
