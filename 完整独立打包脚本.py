#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI主播系统服务器完整独立打包脚本
将服务器程序、数据库、更新包、配置文件等所有组件独立打包
确保在任何环境下都能正常运行，不会报错
"""

import os
import sys
import shutil
import subprocess
import sqlite3
import json
import zipfile
from datetime import datetime

def check_requirements():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查必要文件
    required_files = [
        'server.py',
        'config.py',
        'templates',
        'static'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def create_package_structure():
    """创建完整的打包目录结构"""
    print("📁 创建完整打包目录结构...")
    
    # 创建主目录
    package_name = "AI主播系统服务器_完整独立版"
    if os.path.exists(package_name):
        shutil.rmtree(package_name)
    
    # 创建目录结构
    dirs = [
        f"{package_name}",
        f"{package_name}/server",
        f"{package_name}/data",
        f"{package_name}/data/database",
        f"{package_name}/data/uploads",
        f"{package_name}/data/downloads",
        f"{package_name}/data/logs",
        f"{package_name}/data/config",
        f"{package_name}/data/backup",
        f"{package_name}/updates",
        f"{package_name}/tools",
        f"{package_name}/docs"
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"  ✅ 创建目录: {dir_path}")
    
    return package_name

def create_standalone_databases(package_name):
    """创建独立的数据库文件"""
    print("🗄️ 创建独立数据库文件...")
    
    # 主数据库
    main_db_path = f"{package_name}/data/database/server_data.db"
    create_main_database(main_db_path)
    
    # 本地数据库
    local_db_path = f"{package_name}/data/database/local.db"
    create_local_database(local_db_path)
    
    print("  ✅ 独立数据库文件创建完成")

def create_main_database(db_path):
    """创建主数据库"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建client_updates表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                version TEXT NOT NULL,
                release_date TEXT NOT NULL,
                description TEXT,
                download_url TEXT,
                fast_download_url TEXT,
                force_update INTEGER DEFAULT 0,
                is_exe INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入示例更新数据
        cursor.execute('''
            INSERT OR REPLACE INTO client_updates (id, version, release_date, description, download_url, fast_download_url, force_update, is_exe)
            VALUES (1, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '1.8',
            '2025-06-14',
            '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性',
            'http://localhost:12456/static/downloads/AI主播系统.zip',
            'http://localhost:12456/api/fast-download/AI主播系统.zip',
            0,
            0
        ))
        
        # 创建api_configs表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                endpoint TEXT NOT NULL,
                method TEXT DEFAULT 'GET',
                description TEXT,
                is_active INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入示例API配置
        api_configs = [
            ('aizhubo', '/api/aizhubo', 'GET', 'AI主播API接口'),
            ('user_status', '/api/user_status', 'GET', '用户状态查询'),
            ('live_status', '/api/live_status', 'GET', '直播状态查询'),
            ('update_check', '/api/update_check', 'GET', '更新检查'),
            ('download', '/api/download', 'GET', '文件下载'),
        ]
        
        for name, endpoint, method, desc in api_configs:
            cursor.execute('''
                INSERT OR REPLACE INTO api_configs (name, endpoint, method, description)
                VALUES (?, ?, ?, ?)
            ''', (name, endpoint, method, desc))
        
        conn.commit()
        conn.close()
        print(f"    ✅ 主数据库创建完成: {db_path}")
        
    except Exception as e:
        print(f"    ❌ 主数据库创建失败: {e}")

def create_local_database(db_path):
    """创建本地数据库"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建live_status表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS live_status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                status TEXT DEFAULT 'offline',
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                room_id TEXT,
                platform TEXT,
                viewer_count INTEGER DEFAULT 0
            )
        ''')
        
        # 创建logs表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                level TEXT NOT NULL,
                message TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                module TEXT,
                function TEXT
            )
        ''')
        
        # 创建sessions表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT NOT NULL UNIQUE,
                user_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active INTEGER DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"    ✅ 本地数据库创建完成: {db_path}")
        
    except Exception as e:
        print(f"    ❌ 本地数据库创建失败: {e}")

def create_config_files(package_name):
    """创建配置文件"""
    print("⚙️ 创建配置文件...")
    
    # 数据库配置
    db_config = {
        "database": {
            "sqlite": {
                "main_db": "./data/database/server_data.db",
                "local_db": "./data/database/local.db"
            },
            "mysql": {
                "host": "gz-cynosdbmysql-grp-4ow8k8kw.sql.tencentcdb.com",
                "port": 25641,
                "user": "root",
                "password": "Aa123456",
                "database": "aizhubo",
                "charset": "utf8mb4"
            }
        },
        "server": {
            "host": "0.0.0.0",
            "port": 12456,
            "debug": False,
            "threaded": True
        },
        "paths": {
            "data_dir": "./data",
            "uploads": "./data/uploads",
            "downloads": "./data/downloads",
            "logs": "./data/logs",
            "backup": "./data/backup",
            "static": "./static",
            "templates": "./templates"
        },
        "features": {
            "auto_backup": True,
            "backup_interval": 24,
            "log_rotation": True,
            "max_log_files": 10,
            "enable_api_logging": True
        }
    }
    
    with open(f'{package_name}/data/config/database.json', 'w', encoding='utf-8') as f:
        json.dump(db_config, f, indent=2, ensure_ascii=False)
    
    # 应用配置
    app_config = {
        "app": {
            "name": "AI主播系统服务器",
            "version": "1.8",
            "description": "完整独立版本",
            "author": "AI主播团队",
            "build_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        "security": {
            "admin_username": "kaer",
            "admin_password": "a13456A",
            "session_timeout": 3600,
            "max_login_attempts": 5
        },
        "logging": {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "file_handler": True,
            "console_handler": True
        }
    }
    
    with open(f'{package_name}/data/config/app.json', 'w', encoding='utf-8') as f:
        json.dump(app_config, f, indent=2, ensure_ascii=False)
    
    print("  ✅ 配置文件创建完成")

def create_update_packages(package_name):
    """创建更新包"""
    print("📦 创建更新包...")
    
    # 创建示例更新包
    update_files = [
        'AI主播系统.zip',
        'AI主播系统_v1.8.zip',
        'AI主播系统_最新版.zip'
    ]
    
    for update_file in update_files:
        update_path = f"{package_name}/updates/{update_file}"
        
        # 创建一个示例zip文件
        with zipfile.ZipFile(update_path, 'w') as zf:
            # 添加一个示例文件
            zf.writestr('version.txt', f'版本: 1.8\n更新时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n')
            zf.writestr('changelog.txt', '1. 修复了登录系统问题\n2. 优化了更新功能\n3. 增加了新特性\n4. 增强了系统稳定性\n')
        
        print(f"  ✅ 创建更新包: {update_file}")
    
    # 创建下载目录的软链接
    downloads_dir = f"{package_name}/data/downloads"
    for update_file in update_files:
        src = f"../../updates/{update_file}"
        dst = f"{downloads_dir}/{update_file}"
        try:
            if os.path.exists(dst):
                os.remove(dst)
            shutil.copy2(f"{package_name}/updates/{update_file}", dst)
            print(f"  ✅ 复制到下载目录: {update_file}")
        except Exception as e:
            print(f"  ⚠️ 复制失败: {e}")

def modify_server_for_standalone(package_name):
    """修改server.py以支持独立运行"""
    print("🔧 修改server.py以支持独立运行...")
    
    # 读取原始server.py
    with open('server.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加独立运行支持代码
    standalone_code = '''
# ==================== 独立运行支持 ====================
import json
import os
import sys

def get_base_dir():
    """获取程序基础目录"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.abspath(__file__))

def load_standalone_config():
    """加载独立配置"""
    base_dir = get_base_dir()
    config_path = os.path.join(base_dir, 'data', 'config', 'database.json')
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            
        # 更新路径为绝对路径
        if 'database' in config and 'sqlite' in config['database']:
            sqlite_config = config['database']['sqlite']
            for key, path in sqlite_config.items():
                if path.startswith('./'):
                    sqlite_config[key] = os.path.join(base_dir, path[2:])
        
        if 'paths' in config:
            for key, path in config['paths'].items():
                if path.startswith('./'):
                    config['paths'][key] = os.path.join(base_dir, path[2:])
        
        return config
    
    return None

def ensure_directories():
    """确保必要目录存在"""
    base_dir = get_base_dir()
    dirs = [
        'data/database',
        'data/uploads', 
        'data/downloads',
        'data/logs',
        'data/backup',
        'static/downloads'
    ]
    
    for dir_path in dirs:
        full_path = os.path.join(base_dir, dir_path)
        os.makedirs(full_path, exist_ok=True)

# 初始化独立运行环境
STANDALONE_CONFIG = load_standalone_config()
ensure_directories()

# 更新数据库路径
if STANDALONE_CONFIG:
    sqlite_config = STANDALONE_CONFIG.get('database', {}).get('sqlite', {})
    if 'main_db' in sqlite_config:
        # 这里可以更新数据库连接字符串
        pass

# ==================== 独立运行支持结束 ====================

'''
    
    # 在文件开头添加独立运行代码
    modified_content = standalone_code + '\n' + content
    
    # 保存修改后的文件
    standalone_server_path = f'{package_name}/server/server_standalone.py'
    with open(standalone_server_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    print(f"  ✅ 创建独立版本: {standalone_server_path}")
    
    # 复制其他必要文件
    files_to_copy = [
        'config.py',
        'gift_id_map.json', 
        'keyword_response_pairs.json'
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, f'{package_name}/server/')
            print(f"  ✅ 复制文件: {file_name}")
    
    # 复制目录
    dirs_to_copy = [
        'templates',
        'static'
    ]
    
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            dst_dir = f'{package_name}/server/{dir_name}'
            if os.path.exists(dst_dir):
                shutil.rmtree(dst_dir)
            shutil.copytree(dir_name, dst_dir)
            print(f"  ✅ 复制目录: {dir_name}")

def create_tools(package_name):
    """创建工具脚本"""
    print("🛠️ 创建工具脚本...")
    
    # 数据库修复工具
    if os.path.exists('更新管理数据库修复.py'):
        shutil.copy2('更新管理数据库修复.py', f'{package_name}/tools/')
        print("  ✅ 复制数据库修复工具")
    
    # 创建启动脚本
    startup_script = f'''@echo off
chcp 65001 > nul
title AI主播系统服务器 - 完整独立版
echo.
echo ========================================
echo    AI主播系统服务器 - 完整独立版
echo ========================================
echo.
echo 正在启动服务器...
echo 数据目录: %~dp0data
echo 服务器地址: http://localhost:12456
echo 管理后台: http://localhost:12456/admin
echo 默认账号: kaer / a13456A
echo.

cd /d "%~dp0"
"server\\server_standalone.exe"

echo.
echo 服务器已停止运行
pause
'''
    
    with open(f'{package_name}/启动服务器.bat', 'w', encoding='gbk') as f:
        f.write(startup_script)
    
    print("  ✅ 创建启动脚本")

def main():
    """主函数"""
    print("🚀 AI主播系统服务器完整独立打包工具")
    print("=" * 70)
    
    # 检查环境
    if not check_requirements():
        print("❌ 环境检查失败，请解决问题后重试")
        return False
    
    # 创建打包结构
    package_name = create_package_structure()
    
    # 创建独立数据库
    create_standalone_databases(package_name)
    
    # 创建配置文件
    create_config_files(package_name)
    
    # 创建更新包
    create_update_packages(package_name)
    
    # 修改服务器代码
    modify_server_for_standalone(package_name)
    
    # 创建工具
    create_tools(package_name)
    
    print("=" * 70)
    print("🎉 完整独立打包准备完成！")
    print(f"📁 打包目录: {package_name}/")
    print("📊 包含组件:")
    print("  ✅ 服务器程序 (server/)")
    print("  ✅ 独立数据库 (data/database/)")
    print("  ✅ 更新包 (updates/)")
    print("  ✅ 配置文件 (data/config/)")
    print("  ✅ 工具脚本 (tools/)")
    print("  ✅ 启动脚本 (启动服务器.bat)")
    print("=" * 70)
    print("💡 下一步:")
    print("1. 进入 server/ 目录")
    print("2. 使用 PyInstaller 打包 server_standalone.py")
    print("3. 将生成的 exe 文件放回 server/ 目录")
    print("4. 运行 启动服务器.bat 测试")
    print("=" * 70)
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("🎊 完整独立打包准备成功！")
        print("💡 现在所有组件都已独立，不会再有路径和依赖问题！")
    else:
        print("💥 打包准备失败，请检查错误信息")
    
    input("\n按回车键退出...")
