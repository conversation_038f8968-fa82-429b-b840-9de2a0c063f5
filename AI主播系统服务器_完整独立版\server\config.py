import json
import os
import hashlib
import uuid
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('config.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('config')

# 当前客户端版本
CLIENT_VERSION = "1.7.0"
ROOT_CONFIG_FILE = "config.json"
INTERNAL_CONFIG_DIR = "_internal"
INTERNAL_CONFIG_FILE = os.path.join(INTERNAL_CONFIG_DIR, "config.json")

def save_config(config):
    """保存配置到文件

    优先保存到_internal/config.json，如果失败则保存到根目录的config.json
    """
    # 确保_internal目录存在
    try:
        if not os.path.exists(INTERNAL_CONFIG_DIR):
            os.makedirs(INTERNAL_CONFIG_DIR, exist_ok=True)
            logger.info(f"已创建_internal目录")
    except Exception as e:
        logger.error(f"创建_internal目录失败: {str(e)}")

    # 尝试保存到_internal/config.json
    try:
        with open(INTERNAL_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        logger.info(f"配置已保存到 {INTERNAL_CONFIG_FILE}")
        return True
    except Exception as e:
        logger.error(f"保存配置到 {INTERNAL_CONFIG_FILE} 失败: {str(e)}")

        # 如果保存到_internal/config.json失败，尝试保存到根目录的config.json
        try:
            with open(ROOT_CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
            logger.info(f"配置已保存到 {ROOT_CONFIG_FILE}")
            return True
        except Exception as e2:
            logger.error(f"保存配置到 {ROOT_CONFIG_FILE} 失败: {str(e2)}")
            return False

def get_machine_code():
    """获取机器码，基于MAC地址和固定字符串"pclovelily"的组合生成哈希值"""
    try:
        # 尝试导入machine_code模块中的函数
        from machine_code import get_machine_code as get_detailed_machine_code
        return get_detailed_machine_code()
    except ImportError:
        # 如果无法导入，使用简化版本
        try:
            # 获取MAC地址
            try:
                import re
                mac_address = ':'.join(re.findall('..', '%012x' % uuid.getnode()))
            except Exception:
                # 如果格式化失败，使用原始数值
                mac_address = str(uuid.getnode())

            # 固定字符串
            fixed_string = "pclovelily"

            # 组合MAC地址和固定字符串
            combined_str = f"{mac_address}|{fixed_string}"

            # 生成哈希值
            machine_code = hashlib.md5(combined_str.encode()).hexdigest()

            return machine_code
        except Exception as e:
            print(f"获取机器码失败: {str(e)}")
            # 如果出错，生成一个随机的机器码
            return hashlib.md5(str(uuid.uuid4()).encode()).hexdigest()

def load_config():
    """从文件加载配置

    优先从_internal/config.json加载，如果失败则从根目录的config.json加载
    """
    # 默认配置
    default_config = {
        "voice": {
            "prepared_count": 5,
            "loop_mode": "random",
            "volume": 1.0,
            "pitch": 1.0,
            "interval": 1.0,
            "min_interval": 0.5,
            "max_interval": 2.0,
            "sound_card": "默认声卡",
            "speed": 1.0,
            "equalizer": "标准"
        },
        "game": {
            "type": "",
            "name": ""
        },
        "last_selected": {
            "speaker": 0,
            "script": 0,
            "dialogue": 0
        }
    }

    # 首先尝试从_internal/config.json加载
    logger.info(f"尝试从 {INTERNAL_CONFIG_FILE} 加载配置")

    if os.path.exists(INTERNAL_CONFIG_FILE):
        try:
            with open(INTERNAL_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"成功从 {INTERNAL_CONFIG_FILE} 加载配置")

                # 检查token
                if "token" in config:
                    token = config["token"]
                    logger.info(f"配置中包含token: {token[:10] if token else None}...")
                else:
                    logger.info("配置中不包含token")

                # 确保版本号存在
                if "client_version" not in config or not config["client_version"]:
                    config["client_version"] = CLIENT_VERSION
                    logger.info(f"配置中不包含版本号，已设置为当前版本: {CLIENT_VERSION}")

                    # 保存更新后的配置
                    try:
                        with open(INTERNAL_CONFIG_FILE, 'w', encoding='utf-8') as f_write:
                            json.dump(config, f_write, ensure_ascii=False, indent=4)
                        logger.info(f"已更新配置文件中的版本号")
                    except Exception as save_err:
                        logger.error(f"更新配置文件中的版本号失败: {str(save_err)}")

                return config
        except Exception as e:
            logger.error(f"从 {INTERNAL_CONFIG_FILE} 加载配置失败: {str(e)}")
            # 如果从_internal/config.json加载失败，尝试从根目录的config.json加载

    # 如果_internal/config.json不存在或加载失败，尝试从根目录的config.json加载
    logger.info(f"尝试从 {ROOT_CONFIG_FILE} 加载配置")

    if os.path.exists(ROOT_CONFIG_FILE):
        try:
            with open(ROOT_CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"成功从 {ROOT_CONFIG_FILE} 加载配置")

                # 检查token
                if "token" in config:
                    token = config["token"]
                    logger.info(f"配置中包含token: {token[:10] if token else None}...")
                else:
                    logger.info("配置中不包含token")

                # 确保版本号存在
                if "client_version" not in config or not config["client_version"]:
                    config["client_version"] = CLIENT_VERSION
                    logger.info(f"配置中不包含版本号，已设置为当前版本: {CLIENT_VERSION}")

                # 将配置保存到_internal/config.json
                try:
                    # 确保_internal目录存在
                    if not os.path.exists(INTERNAL_CONFIG_DIR):
                        os.makedirs(INTERNAL_CONFIG_DIR, exist_ok=True)
                        logger.info(f"已创建_internal目录")

                    # 保存配置到_internal/config.json
                    with open(INTERNAL_CONFIG_FILE, 'w', encoding='utf-8') as f_write:
                        json.dump(config, f_write, ensure_ascii=False, indent=4)
                    logger.info(f"已将配置从 {ROOT_CONFIG_FILE} 迁移到 {INTERNAL_CONFIG_FILE}")
                except Exception as save_err:
                    logger.error(f"将配置迁移到 {INTERNAL_CONFIG_FILE} 失败: {str(save_err)}")

                return config
        except Exception as e:
            logger.error(f"从 {ROOT_CONFIG_FILE} 加载配置失败: {str(e)}")

    # 如果两个配置文件都不存在或加载失败，返回默认配置
    logger.info("配置文件不存在或加载失败，返回默认配置")
    return default_config