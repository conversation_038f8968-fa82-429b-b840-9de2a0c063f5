('C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\AI主播系统服务器_路径修复版.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('server_fixed',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\server_fixed.py',
   'PYSOURCE'),
  ('python312.dll',
   'D:\\Program Files (x86)\\python32\\python312.dll',
   'BINARY'),
  ('select.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope\\interface\\_zope_interface_coptimizations.cp312-win32.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet\\_greenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\markupsafe\\_speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('yarl\\_quoting_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\yarl\\_quoting_c.cp312-win32.pyd',
   'EXTENSION'),
  ('propcache\\_helpers_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\propcache\\_helpers_c.cp312-win32.pyd',
   'EXTENSION'),
  ('multidict\\_multidict.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\multidict\\_multidict.cp312-win32.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_writer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_writer.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_http_parser.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_http_parser.cp312-win32.pyd',
   'EXTENSION'),
  ('frozenlist\\_frozenlist.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\frozenlist\\_frozenlist.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\mask.cp312-win32.pyd',
   'EXTENSION'),
  ('aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\aiohttp\\_websocket\\reader_c.cp312-win32.pyd',
   'EXTENSION'),
  ('_testcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testcapi.pyd',
   'EXTENSION'),
  ('_testinternalcapi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_testinternalcapi.pyd',
   'EXTENSION'),
  ('gevent\\resolver\\cares.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\resolver\\cares.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\libuv\\_corecffi.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libuv\\_corecffi.pyd',
   'EXTENSION'),
  ('gevent\\libev\\corecext.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\libev\\corecext.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cqueue.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cqueue.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_clocal.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_clocal.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cgreenlet.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_cevent.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_cevent.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_waiter.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_tracer.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_semaphore.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_imap.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_imap.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_ident.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_ident.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_hub_local.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_greenlet_primitives.cp312-win32.pyd',
   'EXTENSION'),
  ('gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent\\_gevent_c_abstract_linkable.cp312-win32.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp312-win32.pyd',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets\\speedups.cp312-win32.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'D:\\Program Files (x86)\\python32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files (x86)\\python32\\VCRUNTIME140.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('MSVCP140.dll', 'C:\\windows\\system32\\MSVCP140.dll', 'BINARY'),
  ('python3.dll', 'D:\\Program Files (x86)\\python32\\python3.dll', 'BINARY'),
  ('sqlite3.dll',
   'D:\\Program Files (x86)\\python32\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('config.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\config.py',
   'DATA'),
  ('gift_id_map.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\gift_id_map.json',
   'DATA'),
  ('keyword_response_pairs.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\keyword_response_pairs.json',
   'DATA'),
  ('static\\admin\\api\\updates\\current',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\admin\\api\\updates\\current',
   'DATA'),
  ('static\\api\\check-update',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\api\\check-update',
   'DATA'),
  ('static\\css\\admin.css',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\css\\admin.css',
   'DATA'),
  ('static\\downloads\\fast_updates\\version.json',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\downloads\\fast_updates\\version.json',
   'DATA'),
  ('static\\js\\admin.js',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\static\\js\\admin.js',
   'DATA'),
  ('templates\\admin\\api_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\api_management.html',
   'DATA'),
  ('templates\\admin\\card_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\card_management.html',
   'DATA'),
  ('templates\\admin\\dashboard.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\dashboard.html',
   'DATA'),
  ('templates\\admin\\index.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\index.html',
   'DATA'),
  ('templates\\admin\\live_status.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745245087',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745245087',
   'DATA'),
  ('templates\\admin\\live_status.html.bak.1745246592',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\live_status.html.bak.1745246592',
   'DATA'),
  ('templates\\admin\\log_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745243487',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745243487',
   'DATA'),
  ('templates\\admin\\log_management_new.html.bak.1745244551',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\log_management_new.html.bak.1745244551',
   'DATA'),
  ('templates\\admin\\login.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\login.html',
   'DATA'),
  ('templates\\admin\\logs.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\logs.html',
   'DATA'),
  ('templates\\admin\\recharge.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\recharge.html',
   'DATA'),
  ('templates\\admin\\settings.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\stats.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\stats.html',
   'DATA'),
  ('templates\\admin\\update_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\update_management.html',
   'DATA'),
  ('templates\\admin\\user_management.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\admin\\user_management.html',
   'DATA'),
  ('templates\\update.html',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\templates\\update.html',
   'DATA'),
  ('user_manager.py',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\user_manager.py',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cryptography-45.0.3.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('zope.interface-7.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\INSTALLER',
   'DATA'),
  ('gevent-25.5.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\entry_points.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\top_level.txt',
   'DATA'),
  ('gevent-25.5.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\REQUESTED',
   'DATA'),
  ('gevent-25.5.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\top_level.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\RECORD',
   'DATA'),
  ('zope.event-5.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\METADATA',
   'DATA'),
  ('pycparser-2.22.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\WHEEL',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\REQUESTED',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE.PSF',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\METADATA',
   'DATA'),
  ('gevent-25.5.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\entry_points.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\INSTALLER',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\INSTALLER',
   'DATA'),
  ('cffi-1.17.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\WHEEL',
   'DATA'),
  ('pycparser-2.22.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\RECORD',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\WHEEL',
   'DATA'),
  ('cffi-1.17.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\METADATA',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('zope.event-5.0.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\namespace_packages.txt',
   'DATA'),
  ('zope.event-5.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\top_level.txt',
   'DATA'),
  ('greenlet-3.2.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\greenlet-3.2.2.dist-info\\METADATA',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\INSTALLER',
   'DATA'),
  ('gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cffi-1.17.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\LICENSE',
   'DATA'),
  ('gevent-25.5.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\gevent-25.5.1.dist-info\\RECORD',
   'DATA'),
  ('pycparser-2.22.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\WHEEL',
   'DATA'),
  ('zope.event-5.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.event-5.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('pycparser-2.22.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\pycparser-2.22.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools-80.9.0.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\setuptools-80.9.0.dist-info\\top_level.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\top_level.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\METADATA',
   'DATA'),
  ('zope.interface-7.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\WHEEL',
   'DATA'),
  ('zope.interface-7.2.dist-info\\namespace_packages.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\namespace_packages.txt',
   'DATA'),
  ('zope.interface-7.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\zope.interface-7.2.dist-info\\RECORD',
   'DATA'),
  ('cffi-1.17.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\entry_points.txt',
   'DATA'),
  ('cffi-1.17.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\cffi-1.17.1.dist-info\\RECORD',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files (x86)\\python32\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\REQUESTED',
   'DATA'),
  ('attrs-25.3.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.0.dist-info\\entry_points.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\entry_points.txt',
   'DATA'),
  ('flask-3.1.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\RECORD',
   'DATA'),
  ('attrs-25.3.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('flask-3.1.0.dist-info\\REQUESTED',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('flask-3.1.0.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\flask-3.1.0.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('attrs-25.3.0.dist-info\\INSTALLER',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\attrs-25.3.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\RECORD',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\RECORD',
   'DATA'),
  ('dnspython-2.7.0.dist-info\\METADATA',
   'D:\\Program Files '
   '(x86)\\python32\\Lib\\site-packages\\dnspython-2.7.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\wrzb\\backups\\5.28\\build\\AI主播系统服务器_路径修复版\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
